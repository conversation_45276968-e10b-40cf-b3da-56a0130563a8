# Learning Review Debugger 插件

## 插件概述

Learning Review Debugger 是一个WordPress插件，用于调试和测试学习复习计划系统。该插件从主题中迁移而来，现在完全独立运行，提供了全面的用户学习数据管理和调试功能。

## 主要功能

### 1. 用户数据查询
- 获取用户基本学习数据
- 查看复习计划信息
- 检查课程进度状态
- 监控学习行为数据

### 2. 数据重置功能
- 重置复习时间
- 清除学习详情
- 重置黑卡数据
- 清理打卡记录

### 3. 测试数据生成
- 创建测试复习计划
- 生成模拟学习数据
- 构建调试环境

### 4. 分类访问管理
- 记录分类访问时间
- 重置访问记录
- 监控学习路径

## 重构历史

### v2.0.0 重大重构 (2025-07-09)

**迁移来源**：
- 原文件：`wp-content/themes/shuimitao.online/plugins/functions/LearningReviewDebuggerAjax.php`
- 新位置：`wp-content/plugins/LearningReviewDebugger/includes/ajax-handlers.php`

**重构内容**：

#### 函数重命名（添加lrd_前缀）
```php
// 原函数名 → 新函数名
admin_review_get_user_data() → lrd_admin_review_get_user_data()
admin_record_category_visit() → lrd_admin_record_category_visit()
reset_alter_review_time() → lrd_reset_alter_review_time()
reset_alter_heika_data_time() → lrd_reset_alter_heika_data_time()
// ... 等15个核心函数
```

#### AJAX Action保持兼容
```php
// AJAX action名称保持不变，确保前端兼容
add_action('wp_ajax_admin_review_get_user_data', 'lrd_admin_review_get_user_data');
add_action('wp_ajax_reset_alter_review_time', 'lrd_reset_alter_review_time');
// ... 所有action名称保持原样
```

#### 独立性增强
- 完全脱离主题依赖
- 独立的插件结构
- 自包含的功能模块

## 核心AJAX接口

### 用户数据管理
- `admin_review_get_user_data` - 获取用户完整学习数据
- `admin_review_get_user_review_mes` - 获取用户复习信息
- `admin_review_clean_user_all_data` - 清理用户所有数据

### 时间和进度管理
- `reset_alter_review_time` - 重置复习时间
- `reset_alter_heika_data_time` - 重置黑卡数据时间
- `admin_reset_user_review_mes_and_course_rate_of_progress` - 重置复习和进度

### 学习详情管理
- `reset_current_user_learn_detail` - 重置学习详情
- `reset_current_user_today_learn_detail` - 重置今日学习详情
- `reset_current_user_heika_check_in_detail` - 重置打卡详情

### 任务内容管理
- `reset_current_user_heika_daily_learning_task_content` - 重置日常任务内容
- `reset_current_user_heika_daily_learning_task_content_total` - 重置任务总数据

### 分类访问管理
- `admin_record_category_visit` - 记录分类访问
- `admin_reset_category_visit` - 重置分类访问记录

### 测试和工具
- `admin_review_create_test_review_mes` - 创建测试复习数据
- `admin_delete_single_course_progress` - 删除单个课程进度
- `get_current_timestamp` - 获取当前时间戳

## 数据结构

### 用户元数据字段
插件管理以下用户元数据字段：

```php
$meta_fields = [
    'review_mes',                                    // 复习计划
    'course_rate_of_progress',                       // 课程进度
    'current_user_learn_detail',                     // 学习详情
    'current_user_today_learn_detail',               // 今日学习详情
    'heika_check_in_detail',                         // 黑卡打卡详情
    'heika_daily_learning_task_content',             // 日常学习任务内容
    'heika_daily_learning_task_content_total',       // 任务总数据
    'alter_review_time',                             // 复习时间修改记录
    'alter_heika_data_time',                         // 黑卡数据修改时间
    'cat_view_time',                                 // 分类访问时间
    'user_orders',                                   // 用户订单
    'cash_back_times',                               // 返现次数
    'total_learning_days_cash_back_chance'           // 学习天数返现机会
];
```

## 安全特性

### 权限验证
- 所有AJAX函数都要求管理员权限
- 使用`current_user_can('manage_options')`验证

### 参数验证
- 严格的用户ID验证
- 数据类型检查
- 边界条件处理

### 错误处理
- 详细的错误日志记录
- 优雅的错误响应
- 异常情况处理

## 日志和调试

### 日志位置
插件的详细日志记录在：`wp-content/debug.log`

### 日志内容
- 插件加载状态
- AJAX请求处理过程
- 数据操作结果
- 错误和异常信息

### 调试模式
在`wp-config.php`中启用调试：
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## 使用方法

### 访问插件
1. 登录WordPress后台
2. 进入"设置" → "学习复习调试器"
3. 使用界面进行调试操作

### 基本操作流程
1. **查询用户数据**：输入用户ID，获取完整学习信息
2. **重置特定数据**：选择要重置的数据类型
3. **创建测试数据**：生成用于测试的复习计划
4. **监控学习行为**：记录和分析用户学习路径

## 兼容性说明

### 前端兼容
- 保持所有原有AJAX action名称
- 前端JavaScript无需修改
- 完全向后兼容

### 主题独立
- 不依赖特定主题
- 可在任何WordPress主题下运行
- 独立的功能模块

## 技术支持

如有问题或建议，请联系开发者：showlin

## 版本历史

- **v2.0.0** (2025-07-09) - 重大重构，从主题迁移到独立插件
- **v1.0.1** (2025-07-08) - 基础功能实现
- **v1.0.0** (2025-07-08) - 初始版本
