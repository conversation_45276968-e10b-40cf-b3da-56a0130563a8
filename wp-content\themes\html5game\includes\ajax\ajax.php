<?php
function testAjax()
{
    echo 1;

}

add_action('wp_ajax_nopriv_testAjax', 'testAjax');
add_action('wp_ajax_testAjax', 'testAjax');

function selectDictData()
{
    global $wpdb;//全局数据库变量
    $word = $_POST['word'];//要查询的单词
    $word = addslashes($word);

    $queryStr = 'SELECT * FROM ' . $wpdb->prefix . 'dict WHERE word="' . $word . '" LIMIT 0,1';//组成查询语句
    $res = $wpdb->get_results($queryStr);//获得结果

    //如果结果集为true
    if ($res) {

        //过滤单词中的反斜杠,两次
        $word = stripslashes($word);
        $word = stripslashes($word);

        //获得音标
        $phonetic = $res[0]->phonetic;//音标

        //音标处理
        if (!$phonetic) {
            $phonetic = "";
        } else {
            $phonetic = stripslashes($phonetic);
        }

        //获得释义
        $explains = $res[0]->explains;//释义


        if (!empty($explains)) {//如果释义非空
            $explain = explode("@", $explains);//释义分割为数组
            echo '[{"word":"' . $word . '"},{"phonetic":"' . $phonetic . '"},{"explain":"' . $explain[0] . '"}]';//AJAX返回json
        } else {//释义为空
            echo "false";
        }

    } else {
        echo "";//返回空
    }


}

add_action('wp_ajax_nopriv_selectDictData', 'selectDictData');
add_action('wp_ajax_selectDictData', 'selectDictData');


function selectDictDataTest()
{
    $word = $_POST['word'];//要查询的单词
    $res = get_dict_data($word);
    if ($res) {
        if ($res == 2) {
            echo false;//输出false,代表没有释义
        } else {
            echo $res;//输出结果,代表正常
        }
    } else {
        echo "";//输出空代表没有查到这个词

    }
}//ajax查找字典查询
add_action('wp_ajax_nopriv_selectDictDataTest', 'selectDictDataTest');//动作钩子
add_action('wp_ajax_selectDictDataTest', 'selectDictDataTest');//动作钩子

function selectDictDataFromLocal()
{
    $word = $_POST['word'];//要查询的单词
    $res = get_dict_data_only_local($word);
    echo $res;
}//ajax查找字典查询
add_action('wp_ajax_nopriv_selectDictDataFromLocal', 'selectDictDataFromLocal');//动作钩子
add_action('wp_ajax_selectDictDataFromLocal', 'selectDictDataFromLocal');//动作钩子


function insertDictData()
{
    global $wpdb;//全局数据库变量
    $word = $_POST['word'];
    $phonetic = $_POST['phonetic'];
    $insertExp = $_POST['insertExp'];

    $new_word = array(
        'word' => $word,
        'phonetic' => $phonetic,
        'explains' => $insertExp
    );
    $wpdb->insert($wpdb->prefix . 'dict', $new_word);
}//ajax添加词典
add_action('wp_ajax_nopriv_insertDictData', 'insertDictData');//动作钩子
add_action('wp_ajax_insertDictData', 'insertDictData');//动作钩子


function insertDictDataFromLocal()
{
    global $wpdb;//全局数据库变量
    $word = $_POST['word'];
    $phonetic = $_POST['phonetic'];
    $explains = $_POST['explains'];
    $eetrans = $_POST['eetrans'];
    $sentence = $_POST['sentence'];
    $mostusedkey = $_POST['mostusedkey'];
    $explanation = $_POST['explanation'];

    $new_word = array(
        'word' => $word,
        'phonetic' => $phonetic,
        'explains' => $explains,
        'eetrans' => $eetrans,
        'sentence' => $sentence,
        'mostusedkey' => $mostusedkey,
        'explanation' => $explanation
    );
    if (get_dict_data_only_local($word)) {
        echo 0;//已存在
    } else {
        if ($wpdb->insert($wpdb->prefix . 'dict', $new_word)) {
            echo 1;//成功
        } else {
            echo 0;//失败
        }
    }
}//ajax通过后台添加词典
add_action('wp_ajax_nopriv_insertDictDataFromLocal', 'insertDictDataFromLocal');//动作钩子
add_action('wp_ajax_insertDictDataFromLocal', 'insertDictDataFromLocal');//动作钩子


function updateDictDataFromLocal()
{
    global $wpdb;//全局数据库变量
    $word = $_POST['word'];
    $phonetic = $_POST['phonetic'];
    $explains = $_POST['explains'];
    $eetrans = $_POST['eetrans'];
    $sentence = $_POST['sentence'];
    $mostusedkey = $_POST['mostusedkey'];
    $isModify = $_POST['isModify'];
    $explanation = $_POST['explanation'];

    $resarr = array();
    $isAlter = false;

    $keys = array(
        'phonetic' => $phonetic,
        'explains' => $explains,
        'eetrans' => $eetrans,
        'sentence' => $sentence,
        'mostusedkey' => $mostusedkey,
        'isModify' => $isModify,
        'explanation' => $explanation
    );

    foreach ($keys as $key => $value) {
        array_push($resarr, update_dict_data($word, $key, $value));
    }

    foreach ($resarr as $val) {
        if ($val) {
            $isAlter = true;
            break;
        }
    }
    if ($isAlter) {
        echo "1";
    } else {
        echo "0";
    }


}//ajax通过后台修改单词
add_action('wp_ajax_nopriv_updateDictDataFromLocal', 'updateDictDataFromLocal');//动作钩子
add_action('wp_ajax_updateDictDataFromLocal', 'updateDictDataFromLocal');//动作钩子


/**
 * @功能概述: 生成微信JS-SDK配置所需的签名
 *           获取jsapi_ticket并使用SHA1算法生成签名，用于微信JS-SDK的wx.config配置
 *           确保签名算法符合微信官方规范，支持域名迁移后的正常使用
 *
 * @return {void} 直接输出签名字符串给前端AJAX请求
 *
 * @执行流程:
 *   1. 获取微信服务号access_token
 *   2. 通过access_token获取jsapi_ticket
 *   3. 构造签名字符串并生成SHA1签名
 *   4. 输出签名给前端
 */
function getWxSdkSign()
{
    $log_prefix = '[WxJsSDKSign] ';
    error_log($log_prefix . '[开始] 微信JS-SDK签名生成开始');

    // 步骤 1: 获取微信服务号access_token
    $access = get_option("main_wechat_access_token");
    error_log($log_prefix . '[步骤 1] 获取access_token: ' . substr($access, 0, 20) . '...');

    // 步骤 2: 通过access_token获取jsapi_ticket
    $getUrl = 'https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=' . $access . '&type=jsapi';
    error_log($log_prefix . '[步骤 2] 构造jsapi_ticket请求URL: ' . $getUrl);

    $getJson = file_get_contents($getUrl);
    $obj = json_decode($getJson);
    error_log($log_prefix . '[步骤 2] jsapi_ticket API响应: ' . $getJson);

    if (!$obj || !isset($obj->ticket)) {
        error_log($log_prefix . '[错误] jsapi_ticket获取失败');
        echo 'ERROR: jsapi_ticket获取失败';
        return;
    }

    $jsapi_ticket = $obj->ticket;
    error_log($log_prefix . '[步骤 2] jsapi_ticket获取成功: ' . substr($jsapi_ticket, 0, 20) . '...');

    // 步骤 3: 获取签名参数
    $timestamp = $_POST['timestamp'];
    $nonceStr = 'weiyouxiin';
    $currentUrl = $_POST['currentUrl'];

    error_log($log_prefix . '[步骤 3] 签名参数 - timestamp: ' . $timestamp . ', nonceStr: ' . $nonceStr);
    error_log($log_prefix . '[步骤 3] 当前页面URL: ' . $currentUrl);

    // 步骤 4: 构造签名字符串（按微信官方规范：字典序排列）
    $string1 = 'jsapi_ticket=' . $jsapi_ticket . '&noncestr=' . $nonceStr . '&timestamp=' . $timestamp . '&url=' . $currentUrl;
    error_log($log_prefix . '[步骤 4] 待签名字符串: ' . $string1);

    // 步骤 5: 生成SHA1签名
    $signature = sha1($string1);
    error_log($log_prefix . '[步骤 5] 生成签名: ' . $signature);
    error_log($log_prefix . '[完成] 微信JS-SDK签名生成完成');

    echo $signature;
}
add_action('wp_ajax_nopriv_getWxSdkSign', 'getWxSdkSign');//动作钩子
add_action('wp_ajax_getWxSdkSign', 'getWxSdkSign');//动作钩子


function addFavCat()
{
    $user_id = $_POST['user_id'];
    $cat_id = $_POST['cat_id'];
    $fav_cat_str = get_user_meta($user_id, "user_fav_cat", true);
    if ($fav_cat_str) {
        $fav_cat_arr = array();
        if (strpos($fav_cat_str, "#")) {
            $fav_cat_arr = explode("#", $fav_cat_str);
            $isExist = false;
            foreach ($fav_cat_arr as $fav_cat) {
                if ($fav_cat == $cat_id) {
                    $isExist = true;
                    break;
                }
            }
            if (!$isExist) {
                array_push($fav_cat_arr, $cat_id);
                $new_fav_cat_str = implode("#", $fav_cat_arr);
                if (update_user_meta($user_id, "user_fav_cat", $new_fav_cat_str)) {
                    echo "1";
                    return;
                }
            } else {
                echo "2";
                return;
            }
        } else {
            if ($fav_cat_str == $cat_id) {
                echo "2";
                return;
            }
            $new_fav_cat_str = $fav_cat_str . "#" . $cat_id;
            if (update_user_meta($user_id, "user_fav_cat", $new_fav_cat_str)) {
                echo "1";
                return;
            }
        }
    } else {
        if (update_user_meta($user_id, "user_fav_cat", $cat_id)) {
            echo "1";
            return;
        }
    }
    echo "";
}//添加我喜欢的
add_action('wp_ajax_nopriv_addFavCat', 'addFavCat');//动作钩子
add_action('wp_ajax_addFavCat', 'addFavCat');//动作钩子


function delFavCat()
{
    $user_id = $_POST['user_id'];
    $cat_id = $_POST['cat_id'];

    $fav_cat_str = get_user_meta($user_id, "user_fav_cat", true);
    if (empty($fav_cat_str)) {
        echo "2";
        return;
    }

    if (strpos($fav_cat_str, "#")) {
        $fav_cat_arr = explode("#", $fav_cat_str);
        $key = 0;
        $isExist = false;
        foreach ($fav_cat_arr as $fav_cat) {
            if ($fav_cat == $cat_id) {
                $isExist = true;
                break;
            }
            $key++;
        }
        if ($isExist) {
            array_splice($fav_cat_arr, $key, 1);
            $new_fav_cat_str = implode("#", $fav_cat_arr);
            if (update_user_meta($user_id, "user_fav_cat", $new_fav_cat_str)) {
                echo "1";
                return;
            }
        } else {
            echo "2";
            return;
        }

    } else {
        if ($fav_cat_str == $cat_id) {
            if (update_user_meta($user_id, "user_fav_cat", "")) {
                echo "1";
                return;
            }
        } else {
            echo "2";
            return;
        }
    }
    echo "";
}//删除我喜欢的
add_action('wp_ajax_nopriv_delFavCat', 'delFavCat');//动作钩子
add_action('wp_ajax_delFavCat', 'delFavCat');//动作钩子


function createBook()
{
    /*
     * 正常返回json字符串
     * 格式：{"book_id":11155,"book_title":"新建单词本"}
     * 失败返回空字符串
     * */
    $user_id = $_POST['user_id'];
    $book_title = $_POST['book_title'];
    $isSetDefault = $_POST['isSetDefault'];
    $num = create_word_book($user_id, $book_title);
    if ($num) {
        update_post_meta($num, "custom_post_template", "single-myBook.php");
        if ($isSetDefault == 1) {
            update_user_meta($user_id, "user_default_book", $num);
        }
        $jsonArr = array("book_id" => $num, "post_title" => $book_title);
        $jsonStr = json_encode($jsonArr);
        echo $jsonStr;

    } else {
        echo "";
    }

}//创建单词本

add_action('wp_ajax_nopriv_createBook', 'createBook');//动作钩子
add_action('wp_ajax_createBook', 'createBook');//动作钩子


function createUserBook()
{
    /*
     * 正常返回json字符串
     *
     * 失败返回空字符串
     * */
    $user_id = $_POST['user_id'];
    $book_title = $_POST['book_title'];
    $isSetDefault = $_POST['isSetDefault'];
    $book_id = create_word_book($user_id, $book_title);
    if ($book_id) {
        update_post_meta($book_id, "custom_post_template", "single-myBook.php");
        if ($isSetDefault == 1) {
            update_user_meta($user_id, "user_default_book", $book_id);
        }
        $bookMesArr = array("user_book_id" => $book_id, "user_book_name" => $book_title);
        $wordoriArr = array();
        $wordCtransArr = array();
        $jsonArr = array($bookMesArr, $wordoriArr, $wordCtransArr);
        $jsonStr = json_encode($jsonArr);
        echo $jsonStr;

    } else {
        echo "";
    }

}//创建单词本

add_action('wp_ajax_nopriv_createUserBook', 'createUserBook');//动作钩子
add_action('wp_ajax_createUserBook', 'createUserBook');//动作钩子


function deteleBook()
{
    $user_id = $_POST['user_id'];
    $post_id = $_POST['book_id'];

    if (isInReview($user_id, $post_id)) {
        echo "2";
        return;
    }

    if (wp_delete_post($post_id)) {
        echo "1";
    } else {
        echo "";
    }

}//删除单词本

add_action('wp_ajax_nopriv_deteleBook', 'deteleBook');//动作钩子
add_action('wp_ajax_deteleBook', 'deteleBook');//动作钩子


function alterBookName()
{
    global $wpdb;
    $user_id = $_POST['user_id'];
    $post_id = $_POST['book_id'];
    $post_title = $_POST['book_title'];

    if (isInReview($user_id, $post_id)) {
        echo 2;
        return;
    }

    /*$updatestr = "UPDATE " . $wpdb->prefix . "posts  SET  `post_title` = ".$post_title."  WHERE  ID =" . $post_id . " LIMIT 1";
    $wpdb->query($wpdb->prepare($updatestr));*/

    if ($wpdb->update($wpdb->posts, array('post_title' => $post_title), array('ID' => $post_id))) {
        echo $post_title;
    } else {
        echo "";
    }
}//修改单词本名称

add_action('wp_ajax_nopriv_alterBookName', 'alterBookName');//动作钩子
add_action('wp_ajax_alterBookName', 'alterBookName');//动作钩子


function setUserDefBook()
{
    /*
   * 生词本内有单词返回json字符串
   * 格式：{}
   * 生词本内无单词返回1
   * 失败返回2或空
   * */

    $book_id = $_POST['book_id'];
    $user_id = $_POST['user_id'];

    /*
    * 如果设置的默认单词本在复习计划中，则不能设为默认
    * 返回2
    *
    * */

    if (isInReview($user_id, $book_id)) {
        echo 2;
        return;
    }

    if (update_user_meta($user_id, "user_default_book", $book_id)) {
        $word_str = get_post_meta($book_id, "new_words_value", true);
        $wordoriArr = array();
        $wordCtransArr = array();
        if ($word_str) {
            if (strpos($word_str, "#")) {
                $wordoriArr = explode("#", $word_str);
            } else {
                $wordoriArr = array($word_str);
            }
            for ($i = 0; $i < count($wordoriArr); $i++) {
                if (strpos($wordoriArr[$i], "*")) {//查找有没有*字符,如果有
                    $wordoriArr[$i] = trim($wordoriArr[$i]);//去掉两边两边空格
                    $newArr = explode("*", $wordoriArr[$i]);//依据*分割为数组
                    $jsonObj = json_decode(get_dict_data_only_local($newArr[0]));//获取单词json对象
                    $mostusedkey = $newArr[1];//获取当前释义


                } else {
                    $wordoriArr[$i] = trim($wordoriArr[$i]);//去掉两边两边空格
                    $jsonObj = json_decode(get_dict_data_only_local($wordoriArr[$i]));//获取单词json对象
                    $mostusedkey = $jsonObj->mostusedkey ? $jsonObj->mostusedkey : 0;//获取当前释义
                }

                $explainsStr = $jsonObj->explains ? $jsonObj->explains : "";//释义

                if (!empty($explainsStr)) {
                    //释义字符串非空
                    if (strpos($explainsStr, "@")) {
                        $explainsArr = explode("@", $explainsStr);//释义字符串分割为数组
                        $explains = ($explainsArr[$mostusedkey]) ? $explainsArr[$mostusedkey] : $explainsArr[0];//用$mostusedkey来确定最常用的那个释义

                    } else {
                        $explains = $explainsStr;
                    }
                    array_push($wordCtransArr, $explains);//压入释义组
                } else {//否则
                    $explains = "";//释义为空
                    array_push($wordCtransArr, $explains);//压入释义组
                }
            }
        }


        $book_title = get_post($book_id)->post_title;

        $bookMesArr = array("user_book_id" => $book_id, "user_book_name" => $book_title);
        $jsonArr = array($bookMesArr, $wordoriArr, $wordCtransArr);//返回的json数组，由两个数组组成，分别为单词数组与释义数组
        $jsonStr = json_encode($jsonArr);
        echo $jsonStr;
        return;

    }
    echo "";


}


add_action('wp_ajax_nopriv_setUserDefBook', 'setUserDefBook');//动作钩子
add_action('wp_ajax_setUserDefBook', 'setUserDefBook');//动作钩子


function setDefBook()
{
    /*
     * 生词本内有单词返回json字符串
     * 格式：{apple,boy,bank}
     * 生词本内无单词返回1
     * 失败返回2或空
     * */

    $post_id = $_POST['book_id'];
    $user_id = $_POST['user_id'];

    /*
     * 如果设置的默认单词本在复习计划中，则不能设为默认
     * 返回2
     *
     * */

    if (isInReview($user_id, $post_id)) {
        echo 2;
        return;
    }


    if (update_user_meta($user_id, "user_default_book", $post_id)) {
        $word_str = get_post_meta($post_id, "new_words_value", true);
        if ($word_str) {
            if (strpos($word_str, "#")) {
                $jsonArr = explode("#", $word_str);
                $json = json_encode($jsonArr);
                echo $json;
                return;

            } else {
                $jsonArr = array($word_str);
                $json = json_encode($jsonArr);
                echo $json;
                return;
            }
        }
        echo 1;
        return;
    }
    echo "";
}//设置默认单词本

add_action('wp_ajax_nopriv_setDefBook', 'setDefBook');//动作钩子
add_action('wp_ajax_setDefBook', 'setDefBook');//动作钩子


function upBook()
{
    /*
    current_time($format,$gmt)
    $format(字符串)(必须)
    时间格式
    可能的值：
    'Y-m-d H:i:s'
    'mysql'
    'timestamp'
    $gmt(整数)(可选)
    所返回时间的时区（GMT或本地）
    可能的值包括：1 ,0 ,默认值：0
    $gmt=1返回的时间为GMT时间
    $gmt=0，返回的时间为浏览器客户端本地时间

    */

    global $wpdb;

    $post_id = $_POST['book_id'];
    $user_id = $_POST['user_id'];

    if (isInReview($user_id, $post_id)) {
        echo 2;
        return;
    }


    $time = current_time("timestamp") - 1;
    $date = date('Y-m-d H:i:s', $time);
    $date_gmt = get_gmt_from_date($date);

    if ($wpdb->update($wpdb->posts, array('post_date' => $date, 'post_date_gmt' => $date_gmt), array('ID' => $post_id))) {
        echo "1";
    } else {
        echo "";
    }
}//置顶单词本

add_action('wp_ajax_nopriv_upBook', 'upBook');//动作钩子
add_action('wp_ajax_upBook', 'upBook');//动作钩子


function delWordInBook()
{
    $book_id = $_POST['book_id'];
    $wordori = $_POST["wordori"];

    $word_str = get_post_meta($book_id, "new_words_value", true);
    if (empty($word_str)) {
        echo "2";
        return;
    }

    if (strpos($word_str, "#")) {
        $word_str_arr = explode("#", $word_str);//将字符串设为数组
        for ($i = 0; $i < count($word_str_arr); $i++) {
            $word_str_arr[$i] = trim($word_str_arr[$i]);

        }//循环去掉数组元素两边空格
        $key = 0;
        $isExist = false;
        foreach ($word_str_arr as $word) {
            if ($word == $wordori) {
                $isExist = true;
                break;
            }
            $key++;
        }
        if ($isExist) {
            array_splice($word_str_arr, $key, 1);
            $new_word_str = implode("#", $word_str_arr);
            if (update_post_meta($book_id, "new_words_value", $new_word_str)) {
                echo "1";
                return;
            }
        } else {
            echo "2";
            return;
        }

    } else {
        if (trim($word_str) == $wordori) {
            if (update_post_meta($book_id, "new_words_value", "")) {
                echo "1";
                return;
            }
        } else {
            echo "2";
            return;
        }
    }
    echo "";
}//删除当前单词本单词
add_action('wp_ajax_nopriv_delWordInBook', 'delWordInBook');//动作钩子
add_action('wp_ajax_delWordInBook', 'delWordInBook');//动作钩子

function addWordInBook()
{
    $book_id = $_POST['book_id'];
    $wordori = $_POST['wordori'];
    $word_str = get_post_meta($book_id, "new_words_value", true);
    if ($word_str) {//有字符串
        if (strpos($word_str, "#")) {//字符串带#
            $word_str_arr = explode("#", $word_str);
            $isExist = false;
            foreach ($word_str_arr as $word) {//比对单词是否存在
                if ($wordori == trim($word)) {
                    $isExist = true;
                    break;
                }
            }
            if (!$isExist) {//如果不存在
                array_push($word_str_arr, $wordori);
                $new_word_str = implode("#", $word_str_arr);
                if (update_post_meta($book_id, "new_words_value", $new_word_str)) {
                    echo "1";
                    return;
                }
            } else {
                echo "2";
                return;
            }
        } else {
            if (trim($word_str) == $wordori) {
                echo "2";
                return;
            }
            $new_word_str = $word_str . "#" . $wordori;
            if (update_post_meta($book_id, "new_words_value", $new_word_str)) {
                echo "1";
                return;
            }
        }
    } else {
        if (update_post_meta($book_id, "new_words_value", $wordori)) {
            echo "1";
            return;
        }
    }
    echo "";
}//添加我喜欢的
add_action('wp_ajax_nopriv_addWordInBook', 'addWordInBook');//动作钩子
add_action('wp_ajax_addWordInBook', 'addWordInBook');//动作钩子


function updateWordInBook()
{
    $book_id = $_POST['book_id'];
    $book_str = $_POST['book_str'];
    $wordori = $_POST['wordori'];
    $isModify = $_POST['isModify'];
    if ($isModify == 0) {
        add_dict_error_data($wordori, "添加单词本", "用户添加的单词未修改");
    }
    if (update_post_meta($book_id, "new_words_value", $book_str)) {
        echo "1";
        return;
    }
    echo "";
}

add_action('wp_ajax_nopriv_updateWordInBook', 'updateWordInBook');//动作钩子
add_action('wp_ajax_updateWordInBook', 'updateWordInBook');//动作钩子


function addWordError()
{
    $wordori = $_POST['wordori'];
    if (add_dict_error_data($wordori, "添加单词本", "用户添加的单词未修改")) {
        echo "1";
        return;
    }
    echo "";

}//当用户在生词本中添加单词，如果该单词没有人工修改过，加入wordError表
add_action('wp_ajax_nopriv_addWordError', 'addWordError');
add_action('wp_ajax_addWordError', 'addWordError');


function getUnDefaultBook()
{
    $user_id = $_POST['user_id'];
    $user_default_book = get_user_meta($user_id, "user_default_book", true);

    $user_undefault_book_arr = array();

    $arg = array(
        "author" => $user_id,
        'posts_per_page' => 100,
        'orderby' => 'date',
        'post_status' => 'publish'
    );
    $bookObjArr = get_posts($arg);
    foreach ($bookObjArr as $bookObj) {
        if ($bookObj->ID == $user_default_book) {
            continue;
        }//单词本等于默认单词本

        if (get_post_meta($bookObj->ID, "custom_post_template", true) != "single-myBook.php") {
            continue;
        }//模板不对

        $book_word_str = get_post_meta($bookObj->ID, "new_words_value", true);
        if ($book_word_str) {
            $word_num = substr_count($book_word_str, '#') + 1;
            if ($word_num >= 50) {
                continue;
            }
        }//词汇量大约50

        //单词本在复习计划中
        if (isInReview($user_id, $bookObj->ID)) {
            continue;
        }


        $arr = array("book_id" => $bookObj->ID, "book_title" => $bookObj->post_title);


        array_push($user_undefault_book_arr, $arr);

    }

    if (!empty($user_undefault_book_arr)) {
        $json = json_encode($user_undefault_book_arr);
        echo $json;

    } else {
        echo "";
    }


}//获取不是默认单词本的单词本
add_action('wp_ajax_nopriv_getUnDefaultBook', 'getUnDefaultBook');//动作钩子
add_action('wp_ajax_getUnDefaultBook', 'getUnDefaultBook');//动作钩子


function saveReviewMes()
{
    $user_id = $_POST['user_id'];
    $book_id = $_POST['book_id'];
    $model = $_POST['model'];
    $todaymorning = strtotime(current_time("Y-m-d"));//当天0点
    $todaynight = $todaymorning + 24 * 3600 - 1;//当天23.59
    /*$tomorrowmorning=strtotime(date("Y-m-d",strtotime("+1 day")));//明天0点*/

    $review_mes_json = get_user_meta($user_id, "review_mes", true);//获取复习信息json值
    if (!$review_mes_json) {//复习信息json值为空的情况
        $beginTime = current_time("timestamp");
        update_user_meta($user_id, "review_begin_time", $beginTime);
        update_user_meta($user_id, "review_now_time", $beginTime);

        $finishTimes = 1;
        $reviewTime = current_time("timestamp") + 24 * 3600 * 1;
        $json_arr = array("post_id" => $book_id, "finishTimes" => $finishTimes, "model" => $model, "reviewTime" => $reviewTime);
        $new_review_mes_arr = array();//总体复习数组
        array_push($new_review_mes_arr, $json_arr);//把单个单词本数组信息压入总体数组
        $new_review_mes_json = json_encode($new_review_mes_arr);//转为json
        echo (update_user_meta($user_id, 'review_mes', $new_review_mes_json)) ? 1 : 0;//保存并返回结果
        return;
    } else {
        $nowTime = current_time("timestamp");
        update_user_meta($user_id, "review_now_time", $nowTime);

        $review_mes_arr = json_decode($review_mes_json);//解析json

        for ($i = 0; $i < count($review_mes_arr); $i++) {
            if ($book_id == $review_mes_arr[$i]->post_id) {
                //找到单词本
                if ($review_mes_arr[$i]->reviewTime <= $todaynight && $review_mes_arr[$i]->reviewTime >= $todaymorning) {//如果计划复习时间>今天早上，小于今天晚上,计入复习
                    $review_mes_arr[$i]->finishTimes += 1;//复习次数+1
                    $review_mes_arr[$i]->model = $model;//模式
                    switch ($review_mes_arr[$i]->finishTimes) {
                        case 2:
                            $review_mes_arr[$i]->reviewTime = current_time("timestamp") + 24 * 3600 * 2;//两天
                            break;
                        case 3:
                            $review_mes_arr[$i]->reviewTime = current_time("timestamp") + 24 * 3600 * 2;//两天
                            break;
                        case 4:
                            $review_mes_arr[$i]->reviewTime = current_time("timestamp") + 24 * 3600 * 4;//四天
                            break;
                        case 5:
                            $review_mes_arr[$i]->reviewTime = current_time("timestamp") + 24 * 3600 * 6;//六天
                            break;
                        default:
                            $review_mes_arr[$i]->reviewTime = 0;
                            break;

                    }//修改下次复习时间
                    $new_review_mes_json = json_encode($review_mes_arr);//转为json
                    echo (update_user_meta($user_id, 'review_mes', $new_review_mes_json)) ? 1 : 0;//保存并返回结果
                    return;

                } else {//不计入复习
                    echo 2;//输出2代表复习时间不正确
                    return;
                }
            }
        }//循环查找单词本，如果存在，则修改值；

        //没有找到该单词本
        $finishTimes = 1;
        $reviewTime = current_time("timestamp") + 24 * 3600 * 1;
        $json_arr = array("post_id" => $book_id, "finishTimes" => $finishTimes, "model" => $model, "reviewTime" => $reviewTime);
        array_push($review_mes_arr, $json_arr);
        $new_review_mes_json = json_encode($review_mes_arr);//转为json
        echo (update_user_meta($user_id, 'review_mes', $new_review_mes_json)) ? 1 : 0;//返回结果
        return;
    }


}//保存用户的复习数据
add_action('wp_ajax_nopriv_saveReviewMes', 'saveReviewMes');//动作钩子
add_action('wp_ajax_saveReviewMes', 'saveReviewMes');//动作钩子

function selectReviewMes()
{
    $user_id = $_POST['user_id'];


    $begin_time = get_user_meta($user_id, "review_begin_time", true);
    $now_time = get_user_meta($user_id, "review_now_time", true);
    if (!$begin_time || !$now_time) {
        $dis_time_ymd = "";
    } else if ($now_time - $begin_time <= 0) {
        $dis_time_ymd = "";
    } else {
        $dis_time = $now_time - $begin_time;
        $dis_time_ymd = vtime($dis_time);
    }


    $user_nickname = get_user_meta($user_id, 'user_nickname', true);
    if (!$user_nickname) {
        $user_nickname = "";
    }
    $review_mes = get_user_meta($user_id, "review_mes", true);
    if (!$review_mes) {
        $review_mes = "";
    }

    $jsonArr = array("user_id" => $user_id, "user_nickname" => $user_nickname, "review_mes" => $review_mes, "dis_time" => $dis_time_ymd);
    $jsonStr = json_encode($jsonArr);


    echo $jsonStr;

}//查询用户复习数据
add_action('wp_ajax_nopriv_selectReviewMes', 'selectReviewMes');//动作钩子
add_action('wp_ajax_selectReviewMes', 'selectReviewMes');//动作钩子


function deleteSingleReviewMes()
{
    $user_id = $_POST['user_id'];
    $post_id = $_POST['book_id'];

    $review_mes_json = get_user_meta($user_id, "review_mes", true);//获取复习信息json值
    $review_mes_arr = json_decode($review_mes_json);
    //myDump($review_mes_arr);
    $isFind = false;
    $i = 0;
    foreach ($review_mes_arr as $review_mes) {
        if ($review_mes->post_id == $post_id) {
            $isFind = true;
            array_splice($review_mes_arr, $i, 1);
            break;
        }
        $i++;
    }

    if (!$isFind) {
        echo 2;
        return;
    } else {
        if (empty($review_mes_arr)) {
            if (update_user_meta($user_id, "review_mes", "")) {
                update_user_meta($user_id, "review_begin_time", "");
                update_user_meta($user_id, "review_now_time", "");
                echo 1;
                return;
            }
        } else {
            $new_review_mes_json = json_encode($review_mes_arr);
            if (update_user_meta($user_id, "review_mes", $new_review_mes_json)) {
                echo 1;
                return;
            }
        }
    }
    echo "";


}//删除单个复习计划
add_action('wp_ajax_nopriv_deleteSingleReviewMes', 'deleteSingleReviewMes');//动作钩子
add_action('wp_ajax_deleteSingleReviewMes', 'deleteSingleReviewMes');//动作钩子


function deleteAllReviewMes()
{
    $user_id = $_POST['user_id'];
    if (update_user_meta($user_id, "review_mes", "")) {
        update_user_meta($user_id, "review_begin_time", "");
        update_user_meta($user_id, "review_now_time", "");
        echo 1;
        return;
    }
    echo "";
}//删除所有复习计划
add_action('wp_ajax_nopriv_deleteAllReviewMes', 'deleteAllReviewMes');//动作钩子
add_action('wp_ajax_deleteAllReviewMes', 'deleteAllReviewMes');//动作钩子


function deleteSurplusExp()
{
    $wordoriStr = $_POST['wordoriStr'];
    if (strpos($wordoriStr, "@")) {
        $wordoriArr = explode("@", $wordoriStr);
    } else {
        $wordoriArr = array($wordoriStr);
    }

    global $wpdb;
    foreach ($wordoriArr as $wordori) {
        $select = "SELECT * FROM  `" . $wpdb->prefix . "postmeta` WHERE meta_value LIKE  '%" . $wordori . "%'";
        $res = $wpdb->get_results($select);
        if ($res) {
            foreach ($res as $resObj) {
                $post_id = $resObj->post_id;
                $value = $resObj->meta_value;
                if (strpos($value, "#")) {
                    $valueArr = explode("#", $value);
                    for ($i = 0; $i < count($valueArr); $i++) {
                        if ($wordori == $valueArr[$i]) {
                            array_splice($valueArr, $i, 1);
                        }
                    }
                    $newValue = implode("#", $valueArr);
                    if (update_post_meta($post_id, "new_words_value", $newValue)) {
                        echo "删除成功：" . $wordori;
                        echo "<br/>";
                    };


                } else {
                    if (update_post_meta($post_id, "new_words_value", "")) {
                        echo "删除成功：" . $wordori;
                        echo "<br/>";
                    }
                }

            }
        } else {
            echo "没找到：" . $wordori;
            echo "<br/>";
        }
    }


}//删除词典多余释义

add_action('wp_ajax_nopriv_deleteSurplusExp', 'deleteSurplusExp');
add_action('wp_ajax_deleteSurplusExp', 'deleteSurplusExp');


function getWxPayData()
{
    $user_id = $_POST['user_id'];//用户id
    $appid = "wx7467a1b318a5f6a2";//appid
    $wxpayUrl = "https://api.mch.weixin.qq.com/pay/unifiedorder";
    $body = "Joco英语-会员开通";//商品详情
    $mch_id = 1336592301;//微信支付商户号
    $nonce_str = 'weiyouxiin';//随机字符串
    $notify_url = home_url() . "/receive.php";//结果通知地址;
    $openid = $_POST['open_id'];
    $out_trade_no = substr(md5(time()), 0, 12);//商户订单号；
    $spbill_create_ip = $_POST['ip'];//用户端ip;
    $total_fee = $_POST['fee'];//标价金额
    $trade_type = "JSAPI";


    $wxpaykey = "p7nJz5Ll81s3pRu0CuKXHEJhesQ0dHQ4";//微信支付平台秘钥


    $stringA = "appid=" . $appid . "&body=" . $body . "&mch_id=" . $mch_id . "&nonce_str=" . $nonce_str;
    $stringA .= "&notify_url=" . $notify_url . "&openid=" . $openid . "&out_trade_no=" . $out_trade_no . "&spbill_create_ip=" . $spbill_create_ip;
    $stringA .= "&total_fee=" . $total_fee . "&trade_type=" . $trade_type;
    $stringSignTemp = $stringA . "&key=" . $wxpaykey;

    $sign = strtoupper(md5($stringSignTemp));

    $xml = "
   <xml>
   <appid>{$appid}</appid>
   <body>{$body}</body>
   <mch_id>{$mch_id}</mch_id>
   <nonce_str>{$nonce_str}</nonce_str>
   <notify_url>{$notify_url}</notify_url>
   <openid>{$openid}</openid>
   <out_trade_no>{$out_trade_no}</out_trade_no>
   <spbill_create_ip>{$spbill_create_ip}</spbill_create_ip>
   <total_fee>{$total_fee}</total_fee>
   <trade_type>{$trade_type}</trade_type>
   <sign>{$sign}</sign>
   </xml>
";


    $ch = curl_init();
    $timeout = 30;
    curl_setopt($ch, CURLOPT_URL, $wxpayUrl);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_HTTPHEADER, Array("Content-Type:text/xml; charset=utf-8"));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $xml);//Post提交的数据包
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_multi_getcontent($ch);
    curl_exec($ch);
    $result = curl_multi_getcontent($ch);
    curl_close($ch);
    $obj = simplexml_load_string($result, 'SimpleXMLElement', LIBXML_NOCDATA);//解析xml字符串
    //myDump($obj);
    if ($obj->return_code == "SUCCESS") {
        //自己系统内创建订单
        create_order($out_trade_no, $user_id, 0, $obj->prepay_id, $body, $total_fee);
        //echo 1;
        $appId = "wx7467a1b318a5f6a2";//appid
        $nonceStr = "weiyouxiin";
        $package = "prepay_id=" . $obj->prepay_id;
        $signType = "MD5";
        $timeStamp = time();


        $str = "appId=" . $appId . "&nonceStr=" . $nonceStr;
        $str .= "&package=" . $package . "&signType=" . $signType . "&timeStamp=" . $timeStamp;
        $strSignTemp = $str . "&key=" . $wxpaykey;
        //echo $strSignTemp;

        $paySign = strtoupper(md5($strSignTemp));
        //echo $paySign;


        $jsonArr = array("appId" => $appId, "nonceStr" => $nonceStr, "package" => $package, "signType" => $signType, "timeStamp" => $timeStamp, "paySign" => $paySign, "order_no" => $out_trade_no);
        $jsonStr = json_encode($jsonArr);
        echo $jsonStr;

    } else {
        echo "";
    }


    //$obj= simplexml_load_string($res, 'SimpleXMLElement', LIBXML_NOCDATA);
    //myDump($res);
}

add_action('wp_ajax_nopriv_getWxPayData', 'getWxPayData');//动作钩子
add_action('wp_ajax_getWxPayData', 'getWxPayData');//动作钩子

/*
 * 增加用户VIP时间
 *
 * */
function addUserVipTime()
{
    $days = $_POST["days"];
    $user_id = $_POST["user_id"];
    $order_no = $_POST['order_no'];
    $special_pay = $_POST['special_pay'];


    if (add_user_viptime($user_id, $days)) {//增加用户vip时间
        echo 1;
    } else {
        echo "";
    }

    update_order_status($order_no);//更新订单状态
    if ($special_pay) {
        update_user_meta($user_id, "first_pay", 1);
    }


}

add_action('wp_ajax_nopriv_addUserVipTime', 'addUserVipTime');//动作钩子
add_action('wp_ajax_addUserVipTime', 'addUserVipTime');//动作钩子

/*
 *
 * 搜索订单
 * */
function searchOrder()
{
    $search_model = $_POST['search_model'];
    if ($search_model == "user") {
        $user_id = $_POST['user_id'];
        $res = search_order_by_user_id($user_id);
        if ($res) {
            $jsonstr = json_encode($res);
            echo $jsonstr;
        } else {
            echo "";
        }
    } else {
        $order_no = $_POST['order_no'];
        $res = search_order_by_order_no($order_no);
        if ($res) {
            $jsonstr = json_encode($res);
            echo $jsonstr;
        } else {
            echo "";
        }
    }

}

add_action('wp_ajax_nopriv_searchOrder', 'searchOrder');//动作钩子
add_action('wp_ajax_searchOrder', 'searchOrder');//动作钩子


/*
 *
 * 搜索有赞订单
 * */
function search_youzan_order()
{
    $order_no = $_POST['order_no'];
    $res = search_youzan_order_by_order_no($order_no);
    if ($res) {
        $jsonstr = json_encode($res);
        echo $jsonstr;
    } else {
        echo "";
    }

}

add_action('wp_ajax_nopriv_search_youzan_order', 'search_youzan_order');//动作钩子
add_action('wp_ajax_search_youzan_order', 'search_youzan_order');//动作钩子


/*
 * 用户签到
 * */

function userSign()
{
    $user_id = $_POST['user_id'];
    $time = current_time("timestamp");
    $lastSign = get_user_meta($user_id, "user_sign_time", true);
    $signNum = get_user_meta($user_id, "user_sign_num", true);
    if (!$signNum) {
        $signNum = 0;
    }
    $newsignNum = get_user_meta($user_id, "sign_num", true);
    if (!$newsignNum) {
        $newsignNum = 0;
    }
    $todaymorning = strtotime(current_time("Y-m-d"));//当天0点
    $todaynight = $todaymorning + 24 * 3600 - 1;//当天23.59
    $lastdaynight = $todaymorning - 1;//昨天晚间23.59
    $lastdaymorning = $lastdaynight - 24 * 3600 + 1;//昨天0点
    $tomorrowmorning = $todaymorning + 24 * 3600;//明天0点
    if (empty($lastSign) || $lastSign < $todaymorning) {
        if (empty($lastSign) || $lastSign < $lastdaymorning) {
            $signNum = 1;
            $newsignNum = 1;
            //$outNum=2-$signNum;2天签到获VIP
            //$outNum = 5-$newsignNum;//5天签到获VIP
        } else {
            $signNum += 1;
            $newsignNum += 1;
            /*连续签到两天以上获半小时VIP*/
            /*  if ($signNum >= 2) {
                  add_user_viptime_hour($user_id);
                  $newsignNum = 0;
                  $outNum = 0;
              }else{
                  $outNum=2-$signNum;
              }*/
        }
        $outNum = 0;
        update_user_meta($user_id, "user_sign_time", $time);
        update_user_meta($user_id, "user_sign_num", $signNum);
        update_user_meta($user_id, "sign_num", $newsignNum);
        echo $outNum;
        return;
    }


    echo 5;
}

add_action('wp_ajax_nopriv_userSign', 'userSign');//动作钩子
add_action('wp_ajax_userSign', 'userSign');//动作钩子


function userSignAct()
{
    $user_id = $_POST['user_id'];
    $time = current_time("timestamp");
    $lastSign = get_user_meta($user_id, "user_sign_time", true);
    $signNum = get_user_meta($user_id, "user_sign_num", true);
    if (!$signNum) {
        $signNum = 0;
    }

    $todaymorning = strtotime(current_time("Y-m-d"));//当天0点
    $todaynight = $todaymorning + 24 * 3600 - 1;//当天23.59
    $lastdaynight = $todaymorning - 1;//昨天晚间23.59
    $lastdaymorning = $lastdaynight - 24 * 3600 + 1;//昨天0点
    $tomorrowmorning = $todaymorning + 24 * 3600;//明天0点

    if (empty($lastSign) || $lastSign < $todaymorning) {
        if (empty($lastSign) || $lastSign < $lastdaymorning) {
            $signNum = 1;
        } else {
            $signNum += 1;
        }
        update_user_meta($user_id, "user_sign_time", $time);
        update_user_meta($user_id, "user_sign_num", $signNum);
        if ($signNum >= 2) {
            echo 2;
        } else {
            echo 1;
        }

        return;
    }


    echo 5;
}

add_action('wp_ajax_nopriv_userSignAct', 'userSignAct');//动作钩子
add_action('wp_ajax_userSignAct', 'userSignAct');//动作钩子


function userSignAct2()
{
    $user_id = $_POST['user_id'];
    $time = current_time("timestamp");
    $lastSign = get_user_meta($user_id, "user_sign_time", true);
    $signNum = get_user_meta($user_id, "user_sign_num", true);
    if (!$signNum) {
        $signNum = 0;
    }

    $daily_aciton = 0;//标识
    $isvipactionopen = get_option("g_daliy_isopen");;
    if ($isvipactionopen == "open") {
        $daily_aciton = 1;
    }

    $todaymorning = strtotime(current_time("Y-m-d"));//当天0点
    $todaynight = $todaymorning + 24 * 3600 - 1;//当天23.59
    $lastdaynight = $todaymorning - 1;//昨天晚间23.59
    $lastdaymorning = $lastdaynight - 24 * 3600 + 1;//昨天0点
    $tomorrowmorning = $todaymorning + 24 * 3600;//明天0点

    if (empty($lastSign) || $lastSign < $todaymorning) {
        if (empty($lastSign) || $lastSign < $lastdaymorning) {
            $signNum = 1;
        } else {
            $signNum += 1;
        }
        update_user_meta($user_id, "user_sign_time", $time);
        update_user_meta($user_id, "user_sign_num", $signNum);
        if ($signNum >= 2) {
            if ($daily_aciton) {
                add_user_viptime_on_minutes($user_id, 20);
                echo 202;//提醒获得VIP
            } else {
                echo 2;//提醒签到成功
            }
            return;
        } else {
            if ($daily_aciton) {
                echo 201;//提醒，再签到一天获得VIP
            } else {
                echo 1;//提醒签到成功
            }

            return;
        }

    }


    echo 5;
}

add_action('wp_ajax_nopriv_userSignAct2', 'userSignAct2');//动作钩子
add_action('wp_ajax_userSignAct2', 'userSignAct2');//动作钩子

function userShareAct()
{
    $user_id = $_POST['user_id'];
    $time = current_time("timestamp");
    $lastShare = get_user_meta($user_id, "user_share_time", true);
    $todaymorning = strtotime(current_time("Y-m-d"));//当天0点

    $daily_aciton = 0;//标识
    $isvipactionopen = get_option("g_daliy_isopen");;
    if ($isvipactionopen == "open") {
        $daily_aciton = 1;
    }

    if (empty($lastShare) || $lastShare <= $todaymorning) {
        update_user_meta($user_id, "user_share_time", $time);
        if ($daily_aciton) {
            add_user_viptime_on_minutes($user_id, 30);
            echo 101;//提醒获得VIP
        } else {
            echo 5;//不提示
        }
        return;
    }
    echo 5;
}

add_action('wp_ajax_nopriv_userShareAct', 'userShareAct');//动作钩子
add_action('wp_ajax_userShareAct', 'userShareAct');//动作钩子

/*
 *
 * 朋友圈打卡
 * */

function sign_in_wechat_timeline()
{

    $user_id = $_POST['user_id'];//用户ID
    $time = current_time("timestamp");//当前时间

    $todaymorning = strtotime(current_time("Y-m-d"));//当天0点
    $lastdaynight = $todaymorning - 1;//昨天晚间23.59
    $lastdaymorning = $lastdaynight - 24 * 3600 + 1;//昨天0点

    $lastSign = get_user_meta($user_id, "user_sign_time", true);//上次签到时间
    $signNum = get_user_meta($user_id, "user_sign_num", true);//连续签到数
    if (!$signNum) {
        $signNum = 0;
    }


    if (empty($lastSign) || $lastSign < $todaymorning) {
        if (empty($lastSign) || $lastSign < $lastdaymorning) {
            $signNum = 1;
        } else {
            $signNum += 1;
        }
        update_user_meta($user_id, "user_sign_time", $time);//更新签到时间
        update_user_meta($user_id, "user_share_time", $time);//更新分享时间
        update_user_meta($user_id, "user_sign_num", $signNum);//更新连续签到数
        if ($signNum >= 2) {
            if (add_user_viptime_on_minutes($user_id, 60)) {
                echo 2;//提醒获得VIP
                return;
            }
        } else {
            echo 1;//提醒还需要签到一天

            return;
        }

    }


    echo 5;//签到错误

}

add_action('wp_ajax_nopriv_sign_in_wechat_timeline', 'sign_in_wechat_timeline');//动作钩子
add_action('wp_ajax_sign_in_wechat_timeline', 'sign_in_wechat_timeline');//动作钩子


function selectUserVipTime()
{



    $user_id = $_POST["user_id"];


    $aux = get_userdata($user_id);

    if ($aux == false) {

        echo "";
        return;

    }


    $heika_json = get_user_meta($user_id, "heika_vip", true);

    $user_vip_save_time = 0;

    $user_vip_type = "HAVE_DATE";


    if ($heika_json) {


        $user_vip_system = "HEIKA";

        $heika_arr = json_decode($heika_json);

        $heika_single_arr = $heika_arr[0];


        $user_vip_type = $heika_single_arr->card_type;

        $user_vip_expiration_date = $heika_single_arr->can_use_expiration_date;


    } else {

        $viptime = get_user_meta($user_id, "user_viptime", true);

        if ($viptime) {

            $user_vip_system = "OLD";

            $user_vip_expiration_date = $viptime;

            $save_viptime = get_user_meta($user_id, "user_save_viptime", true);

            if ($save_viptime) {
                $user_vip_save_time = $save_viptime;
            } else {
                $user_vip_save_time = 0;
            }

            $user_vip_type = "HAVE_DATE";


        } else {
            $user_vip_system = "OLD";
            $user_vip_expiration_date = 0;
            $user_vip_save_time = 0;
        }


    }

    $user_nickname = get_user_meta($user_id, "user_nickname", true);
    if (!$user_nickname) {
        $user_nickname = "没有昵称";
    }

    $jsonArr = array(
        "user_id" => $user_id,
        "user_nickname" => $user_nickname,
        "user_vip_system" => $user_vip_system,
        "user_vip_type" => $user_vip_type,
        "user_vip_expiration_date" => $user_vip_expiration_date,
        "user_vip_save_time" => $user_vip_save_time


    );

    $jsonStr = json_encode($jsonArr);

    echo $jsonStr;


}

add_action('wp_ajax_nopriv_selectUserVipTime', 'selectUserVipTime');//动作钩子
add_action('wp_ajax_selectUserVipTime', 'selectUserVipTime');//动作钩子


function add_and_reduce_old_viptime()
{
    $user_id = $_POST["user_id"];
    $days = $_POST["days"];


    $user_viptime = get_user_meta($user_id, "user_viptime", true);


    if ($days > 0) {

        $totaltime = ($user_viptime > current_time("timestamp")) ? $user_viptime + $days * 24 * 3600 : current_time("timestamp") + $days * 24 * 3600;


        if (update_user_meta($user_id, "user_viptime", $totaltime)) {

            echo 1;
            return;
        }

    } else {

        $days = abs($days);

        $totaltime = ($user_viptime > current_time("timestamp")) ? $user_viptime - $days * 24 * 3600 : current_time("timestamp") - 10000;

        if (update_user_meta($user_id, "user_viptime", $totaltime)) {

            echo 1;
            return;
        }


    }

    echo "";

}

add_action('wp_ajax_nopriv_add_and_reduce_old_viptime', 'add_and_reduce_old_viptime');//动作钩子
add_action('wp_ajax_add_and_reduce_old_viptime', 'add_and_reduce_old_viptime');//动作钩子


function add_and_reduce_heika_viptime()
{


    $user_id = $_POST["user_id"];

    $telephone_number = get_user_meta($user_id, "telephone_number", true);

    if (!$telephone_number) {
        $telephone_number = "暂无记录";
    }


    $heikaHandle = new heikaHandle($user_id);//加载类


    $days = $_POST["days"];


    //myDump($heika_arr);


    if ($days > 0) {

        if ($heikaHandle->add_can_use_expiration_date("HAVE_DATE", $days, "hlyyin", "ALL", $telephone_number)) {
            echo 1;
            return;
        }


    } else {

        $days = abs($days);

        if ($heikaHandle->reduce_can_use_expiration_date($days, "hlyyin", "ALL")) {
            echo 1;
            return;
        }
    }

    echo "";


}

add_action('wp_ajax_nopriv_add_and_reduce_heika_viptime', 'add_and_reduce_heika_viptime');//动作钩子
add_action('wp_ajax_add_and_reduce_heika_viptime', 'add_and_reduce_heika_viptime');//动作钩子


/*
 *
 * 老会员升级为黑卡
 * */
function upgrade_old_to_heika()
{


    $user_id = $_POST["user_id"];

    $heikaHandle = new heikaHandle($user_id);//加载类


    if ($heikaHandle->old_viptime_go_to_heika()) {
        echo 1;
        return;
    }


    echo "";


}

add_action('wp_ajax_nopriv_upgrade_old_to_heika', 'upgrade_old_to_heika');//动作钩子
add_action('wp_ajax_upgrade_old_to_heika', 'upgrade_old_to_heika');//动作钩子



/*
 *
 * 老会员升级为终身黑卡
 * */
function upgrade_to_forever_heika()
{


    $user_id = $_POST["user_id"];



    $heika_single_arr = array(
        "card_type" => "FOREVER",//会员卡类型
        "can_use_expiration_date" => 0,//到期时间
        "training_expiration_date" => 0,//训练营时间，暂时不用
        "big_course_type" => "hlyyin",//大课程分类 暂时不用
        "specific_course" => "ALL",//具体课程，暂时不用
        "cash_back_times" => 0
    );


    $heika_arr=array();


    array_push($heika_arr, $heika_single_arr);


    /*
     * 将总数组 $heika_arr转为 json 数据 $heika_json
     * */
    $heika_json = json_encode($heika_arr);


    /*
    * 将 $heika_json 保存数据库
    * */


    if(update_user_meta($user_id, "heika_vip", $heika_json)){
        echo 1;
        return;

    }




    echo "0";


}

add_action('wp_ajax_nopriv_upgrade_to_forever_heika', 'upgrade_to_forever_heika');//动作钩子
add_action('wp_ajax_upgrade_to_forever_heika', 'upgrade_to_forever_heika');//动作钩子


/*
 *
 * 老会员升级为黑卡
 * */
function upgrade_to_have_date_heika()
{


    $user_id = $_POST["user_id"];

    $user_viptime = get_user_meta($user_id, "user_viptime", true);



    $heika_single_arr = array(
        "card_type" => "HAVE_DATE",//会员卡类型
        "can_use_expiration_date" => $user_viptime,//到期时间
        "training_expiration_date" => 0,//训练营时间，暂时不用
        "big_course_type" => "hlyyin",//大课程分类 暂时不用
        "specific_course" => "ALL",//具体课程，暂时不用
        "cash_back_times" => 0
    );


    $heika_arr=array();


    array_push($heika_arr, $heika_single_arr);


    /*
     * 将总数组 $heika_arr转为 json 数据 $heika_json
     * */
    $heika_json = json_encode($heika_arr);


    /*
    * 将 $heika_json 保存数据库
    * */


    if(update_user_meta($user_id, "heika_vip", $heika_json)&&update_user_meta($user_id, "user_viptime", 0)){
        echo 1;
        return;

    }




    echo "0";


}

add_action('wp_ajax_nopriv_upgrade_to_have_date_heika', 'upgrade_to_have_date_heika');//动作钩子
add_action('wp_ajax_upgrade_to_have_date_heika', 'upgrade_to_have_date_heika');//动作钩子



function delete_heika()
{


    $user_id = $_POST["user_id"];
    if (update_user_meta($user_id, "heika_vip", "")) {
        echo 1;
        return;
    }
    echo "";

}

add_action('wp_ajax_nopriv_delete_heika', 'delete_heika');//动作钩子
add_action('wp_ajax_delete_heika', 'delete_heika');//动作钩子




function upgrade_to_old_vip()
{


    $user_id = $_POST["user_id"];


    $heika_json=get_user_meta($user_id,"heika_vip",true);


    $heika_arr=json_decode($heika_json);





    $card_type=$heika_arr[0]->card_type;



    if($card_type=='FOREVER'){
        $user_viptime=current_time("timestamp")+365*3600;

    }else{
        $user_viptime=$heika_arr[0]->can_use_expiration_date;
    }





   if(update_user_meta($user_id,"user_viptime",$user_viptime)&&update_user_meta($user_id, "heika_vip", "")){
       echo 1;
       return;
   }

    echo "";



}

add_action('wp_ajax_nopriv_upgrade_to_old_vip', 'upgrade_to_old_vip');//动作钩子
add_action('wp_ajax_upgrade_to_old_vip', 'upgrade_to_old_vip');//动作钩子





/*
 * 连续学习增加20天黑卡时间
 *
 * */

function add_heika_viptime_for_continuously_learning_20()
{


    $user_id = $_POST["user_id"];


    $cash_back_times= get_user_meta($user_id,"cash_back_times",true);


    $cash_back_times+=1;
    update_user_meta($user_id,"cash_back_times",$cash_back_times);



    //返现次数+1
    //保存返现次数


    $continuously_learning_days_cash_back_chance= get_user_meta($user_id,"continuously_learning_days_cash_back_chance",true);


    $continuously_learning_days_cash_back_chance-=1;


    update_user_meta($user_id,"continuously_learning_days_cash_back_chance",$continuously_learning_days_cash_back_chance);

    //保存$userData->continuously_learning_days_cash_back_chance




    /*
     *
     * 获取用户手机号
     * */


    $telephone_number = get_user_meta($user_id, "telephone_number", true);

    if (!$telephone_number) {
        $telephone_number = "暂无记录";

    }


    /*
  *
  * 增加黑卡时间，并返回结果
  * */


    $heikaHandle = new heikaHandle($user_id);//黑卡对象



    if ($heikaHandle->add_can_use_expiration_date("HAVE_DATE", 20, "hlyyin", "ALL", $telephone_number)) {

        echo 1;
    }



    echo "";





}

add_action('wp_ajax_nopriv_add_heika_viptime_for_continuously_learning_20', 'add_heika_viptime_for_continuously_learning_20');
add_action('wp_ajax_add_heika_viptime_for_continuously_learning_20', 'add_heika_viptime_for_continuously_learning_20');

/*
 * 连续学习增加100天黑卡时间
 *
 * */

function add_heika_viptime_for_continuously_learning()
{


    $user_id = $_POST["user_id"];


    $cash_back_times= get_user_meta($user_id,"cash_back_times",true);


    $cash_back_times+=1;
    update_user_meta($user_id,"cash_back_times",$cash_back_times);



    //返现次数+1
    //保存返现次数


    $continuously_learning_days_cash_back_chance= get_user_meta($user_id,"continuously_learning_days_cash_back_chance",true);


    $continuously_learning_days_cash_back_chance-=1;


    update_user_meta($user_id,"continuously_learning_days_cash_back_chance",$continuously_learning_days_cash_back_chance);

    //保存$userData->continuously_learning_days_cash_back_chance




    /*
     *
     * 获取用户手机号
     * */


    $telephone_number = get_user_meta($user_id, "telephone_number", true);

    if (!$telephone_number) {
        $telephone_number = "暂无记录";

    }


    /*
  *
  * 增加黑卡时间，并返回结果
  * */


    $heikaHandle = new heikaHandle($user_id);//黑卡对象



    if ($heikaHandle->add_can_use_expiration_date("HAVE_DATE", 110, "hlyyin", "ALL", $telephone_number)) {

        echo 1;
    }



    echo "";





}

add_action('wp_ajax_nopriv_add_heika_viptime_for_continuously_learning', 'add_heika_viptime_for_continuously_learning');
add_action('wp_ajax_add_heika_viptime_for_continuously_learning', 'add_heika_viptime_for_continuously_learning');




function goWeekMind()
{
    $todaymorning = strtotime(current_time("Y-m-d"));//当天0点
    $access_token = get_option("main_wechat_access_token");//微信签名
    $template_id = "RsWhMcqD1c2iKmoXbQXxmBy7jxbaeyGu0Cxi5VKz2D8";//模板消息id
    $postUrl = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=" . $access_token;//微信接口
    $goUrl = home_url() . "/?page_id=15300";//路径

    global $wpdb;
    $selectStr = "SELECT user_id, meta_value FROM  `" . $wpdb->prefix . "usermeta` WHERE meta_key =  'review_mes'";
    $ress = $wpdb->get_results($selectStr);
    $i = 0;
    foreach ($ress as $res) {
        $user_id = $res->user_id;
        $get_json_str = $res->meta_value;
        $get_review_mes_arr = json_decode($get_json_str);
        $week_remind = get_user_meta($user_id, "week_mind", true);//周提醒
        $openid_to_hlyyin = get_user_meta($user_id, "openid_to_hlyyin", true);//openid
        //if(!$week_remind&&$openid_to_hlyyin){
        //在周提醒为空且有openid的情况下,循环分析用户复习数据
        foreach ($get_review_mes_arr as $get_review_mes) {

            $post_id = $get_review_mes->post_id;
            if ($get_review_mes->reviewTime == 0) {
                continue;

            } else {
                $reviewdaymorning = strtotime(date("Y-m-d", $get_review_mes->reviewTime));//复习当天0点
                if ($reviewdaymorning < $todaymorning) {
                    if (!$week_remind && $openid_to_hlyyin) {
                        $post_title = get_post($post_id)->post_title;
                        if ($get_review_mes->model == "myBook") {
                            $catName = "生词本";
                        } else {
                            $catName = get_post_category_id($post_id);
                        }

                        $keyword1_value = "《" . $catName . "》等内容";
                        $keyword2_value = (date("Y.m.d", current_time("timestamp")));
                        $a = array(
                            "first" => array("value" => "您有一篇或多篇课程已超过复习期，恐怕会影响英语记忆", "color" => "#FF0000"),
                            "keyword1" => array("value" => "{$keyword1_value}"),
                            "keyword2" => array("value" => "{$keyword2_value}"),
                            "remark" => array("value" => "坚持是学好英语的唯一途径"));
                        $b = array("touser" => "{$openid_to_hlyyin}", "template_id" => "{$template_id}", "url" => "{$goUrl}", "data" => $a);

                        $post = json_encode($b);
                        $ch = curl_init();
                        curl_setopt($ch, CURLOPT_URL, $postUrl);//url
                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1); //设置有返回值，0，直接显示
                        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0); //禁用证书验证
                        curl_setopt($ch, CURLOPT_POST, 1);  //post
                        curl_setopt($ch, CURLOPT_POSTFIELDS, $post);
                        $data = curl_exec($ch);
                        curl_close($ch);
                        update_user_meta($user_id, "week_mind", 1);
                        echo "用户ID:" . $user_id . "---发送成功";
                        $i++;
                        break;
                    }
                }

            }


        }


        if ($i >= 200) {
            echo "发送数已满200";
            break;
        }
    }
    echo "发送结束";
}

add_action('wp_ajax_nopriv_goWeekMind', 'goWeekMind');//动作钩子
add_action('wp_ajax_goWeekMind', 'goWeekMind');//动作钩子


function getOneLessonVipDaily()
{
    $user_id = $_POST['user_id'];
    $time = current_time("timestamp");
    $lastGetDailyVipTime = get_user_meta($user_id, "user_drawdailyvip_time", true);
    $todaymorning = strtotime(current_time("Y-m-d"));//当天0点
    if ($lastGetDailyVipTime < $todaymorning) {
        add_user_viptime_on_minutes($user_id, 30);
        update_user_meta($user_id, "user_drawdailyvip_time", $time);
        echo 1;
        return;
    }
    echo 0;


}

add_action('wp_ajax_nopriv_getOneLessonVipDaily', 'getOneLessonVipDaily');//动作钩子
add_action('wp_ajax_getOneLessonVipDaily', 'getOneLessonVipDaily');//动作钩子


function getOneLessonVipLearn()
{
    $user_id = $_POST['user_id'];
    $time = current_time("timestamp");
    $lastGetLearnVipTime = get_user_meta($user_id, "user_drawlearnvip_time", true);
    $todaymorning = strtotime(current_time("Y-m-d"));//当天0点
    if ($lastGetLearnVipTime < $todaymorning) {
        add_user_viptime_on_minutes($user_id, 30);
        update_user_meta($user_id, "user_drawlearnvip_time", $time);
        echo 1;
        return;
    }
    echo 0;


}

add_action('wp_ajax_nopriv_getOneLessonVipLearn', 'getOneLessonVipLearn');//动作钩子
add_action('wp_ajax_getOneLessonVipLearn', 'getOneLessonVipLearn');//动作钩子


/*
 * 复习计划测试,获得用户信息
 *
 * */


function admin_review_get_user_data()
{

    $user_id = $_POST['user_id'];


    $userArr = get_user_info($user_id);

    $user_orders=($userArr['user_orders']) ? $userArr['user_orders'] : "";

    $cash_back_times=($userArr['cash_back_times']) ? $userArr['cash_back_times'] : "";


    $total_learning_days_cash_back_chance=($userArr['total_learning_days_cash_back_chance']) ? $userArr['total_learning_days_cash_back_chance'] : 0;
    $continuously_learning_days_cash_back_chance=($userArr['continuously_learning_days_cash_back_chance']) ? $userArr['continuously_learning_days_cash_back_chance'] : 0;

    $alter_review_time = ($userArr['alter_review_time']) ? $userArr['alter_review_time'] : 0;
    $alter_heika_data_time = ($userArr['alter_heika_data_time']) ? $userArr['alter_heika_data_time'] : 0;

    $heika_daily_learning_task_content = ($userArr['heika_daily_learning_task_content']) ? $userArr['heika_daily_learning_task_content'] : "";

    $heika_daily_learning_task_content_total = ($userArr['heika_daily_learning_task_content_total']) ? $userArr['heika_daily_learning_task_content_total'] : "";


    $learn_detail = ($userArr['learn_detail']) ? $userArr['learn_detail'] : "";
    $today_learn_detail = ($userArr['today_learn_detail']) ? $userArr['today_learn_detail'] : "";
    $today_review_over_num = ($userArr['today_review_over_num']) ? $userArr['today_review_over_num'] : 0;

    $heika_check_in_detail = ($userArr['heika_check_in_detail']) ? $userArr['heika_check_in_detail'] : "";


    if ($heika_check_in_detail) {
        $heika_check_in_detail_arr = json_decode($heika_check_in_detail);//签到详情数组
    } else {
        $heika_check_in_detail_arr = array();
    }


    //获得连续签到时间 $continuously_learning_days


    if (count($heika_check_in_detail_arr) > 0) {

        $continuously_learning_days = 0;

        for ($i = count($heika_check_in_detail_arr) - 2; $i >= 0; $i--) {

            if ($heika_check_in_detail_arr[$i]->is_finish_heika_daily_learning_task == 1) {
                $continuously_learning_days++;
            } else {
                break;
            }
        }
    } else {
        $continuously_learning_days = 0;
    }


    $last_sign_obj = end($heika_check_in_detail_arr);//上次签到时间是最后一个元素

    if ($last_sign_obj->is_finish_heika_daily_learning_task == 1) {
        $continuously_learning_days += 1;
    }



    //获得过去110天内累积学习天数



    if (count($heika_check_in_detail_arr) > 0) {

        $total_learning_days_in_110 = 0;

        $start_key=count($heika_check_in_detail_arr) - 1;//遍历开始键位

        if(count($heika_check_in_detail_arr)>110){
            $end_key=count($heika_check_in_detail_arr)-110;
        }else{
            $end_key=0;
        }

        for ($i = $start_key; $i >= $end_key; $i--) {

            if ($heika_check_in_detail_arr[$i]->is_finish_heika_daily_learning_task == 1) {
                $total_learning_days_in_110++;
            }
        }

    } else {
        $total_learning_days_in_110 = 0;
    }





    $jsonArr = array(
        "user_id" => $user_id,
        "user_orders" => $user_orders,
        "cash_back_times"=>$cash_back_times,
        "total_learning_days_cash_back_chance"=>$total_learning_days_cash_back_chance,
        "continuously_learning_days_cash_back_chance"=>$continuously_learning_days_cash_back_chance,
        "alter_review_time" => $alter_review_time,
        "alter_heika_data_time" => $alter_heika_data_time,
        "heika_daily_learning_task_content" => $heika_daily_learning_task_content,
        "heika_daily_learning_task_content_total" => $heika_daily_learning_task_content_total,
        "heika_check_in_detail" => $heika_check_in_detail,
        "continuously_learning_days" => $continuously_learning_days,
        "total_learning_days_in_110" => $total_learning_days_in_110,
        "learn_detail" => $learn_detail,
        "today_learn_detail" => $today_learn_detail,
        "today_review_over_num" => $today_review_over_num
    );

    $jsonStr = json_encode($jsonArr);

    echo $jsonStr;


}

add_action('wp_ajax_nopriv_admin_review_get_user_data', 'admin_review_get_user_data');
add_action('wp_ajax_admin_review_get_user_data', 'admin_review_get_user_data');


function admin_review_get_user_review_mes()
{

    $user_id = $_POST['user_id'];


    $review_mes_json = get_user_meta($user_id, "review_mes", true);//拉取复习计划 json

    if ($review_mes_json) {
        echo $review_mes_json;
        return;
    }

    echo "";


}

add_action('wp_ajax_nopriv_admin_review_get_user_review_mes', 'admin_review_get_user_review_mes');
add_action('wp_ajax_admin_review_get_user_review_mes', 'admin_review_get_user_review_mes');


/*
 * 测试复习计划，生成复习计划
 *
 * */

function admin_review_create_test_review_mes()
{


    $user_id = $_POST['user_id'];

    $today_morning = strtotime(current_time("Y-m-d"));//当天0点

    $today_night = $today_morning + 24 * 3600 - 1;//当天23.59

    $yesterday_night = $today_morning - 1;//昨天晚间23.59

    $tomorrow_morning = $today_morning + 24 * 3600;//明天0点


    $new_review_mes_arr = array();//总体复习数组


    $args = array(
        'numberposts' => 10,
        'category' => 43,
        'orderby' => 'post_date',
        'order' => 'DESC',
        'post_type' => 'post',
        'post_status' => 'publish');


    $postObjectArr = get_posts($args);


    $i = 0;


    foreach ($postObjectArr as $postObject) {


        if ($i >= 0 && $i < 4) {
            $reviewTime = $yesterday_night - 100;
        } else if ($i >= 4 && $i < 8) {
            $reviewTime = $today_morning + 100;
        } else {
            $reviewTime = $tomorrow_morning + 100;
        }


        $arr = array(
            "post_id" => $postObject->ID,
            "finishTimes" => rand(1, 4),
            "model" => 43,
            "reviewTime" => $reviewTime,
            "word_arr" => array(),
            "sentence_arr" => array(),
        );

        array_push($new_review_mes_arr, $arr);


        $i++;

    }


    $args = array(
        'numberposts' => 10,
        'category' => 44,
        'orderby' => 'post_date',
        'order' => 'DESC',
        'post_type' => 'post',
        'post_status' => 'publish');


    $postObjectArr = get_posts($args);


    $i = 0;


    foreach ($postObjectArr as $postObject) {


        if ($i >= 0 && $i < 4) {
            $reviewTime = $yesterday_night - 100;
        } else if ($i >= 4 && $i < 8) {
            $reviewTime = $today_morning + 100;
        } else {
            $reviewTime = $tomorrow_morning + 100;
        }


        $arr = array(
            "post_id" => $postObject->ID,
            "finishTimes" => rand(1, 4),
            "model" => 43,
            "reviewTime" => $reviewTime,
            "word_arr" => array(),
            "sentence_arr" => array(),
        );

        array_push($new_review_mes_arr, $arr);


        $i++;

    }


    $args = array(
        'numberposts' => 10,
        'category' => 46,
        'orderby' => 'post_date',
        'order' => 'DESC',
        'post_type' => 'post',
        'post_status' => 'publish');


    $postObjectArr = get_posts($args);


    $i = 0;


    foreach ($postObjectArr as $postObject) {


        if ($i >= 0 && $i < 4) {
            $reviewTime = $yesterday_night - 100;
        } else if ($i >= 4 && $i < 8) {
            $reviewTime = $today_morning + 100;
        } else {
            $reviewTime = $tomorrow_morning + 100;
        }


        $arr = array(
            "post_id" => $postObject->ID,
            "finishTimes" => rand(1, 4),
            "model" => 46,
            "reviewTime" => $reviewTime,
            "word_arr" => array(),
            "sentence_arr" => array(),
        );

        array_push($new_review_mes_arr, $arr);


        $i++;

    }


    //myDump($new_review_mes_arr);

    $new_review_mes_json = json_encode($new_review_mes_arr);//转为json


    if (update_user_meta($user_id, "review_mes", $new_review_mes_json)) {
        echo "1";
        return;

    }

    echo "";

    //echo "通讯成功";


}

add_action('wp_ajax_nopriv_admin_review_create_test_review_mes', 'admin_review_create_test_review_mes');//动作钩子
add_action('wp_ajax_admin_review_create_test_review_mes', 'admin_review_create_test_review_mes');//动作钩子


/*
 * 清理复习计划
 *
 * */


function admin_review_clear_review_mes()
{

    $user_id = $_POST['user_id'];


    if (update_user_meta($user_id, 'review_mes', "")) {
        echo 1;
        return;
    }

    echo "";

}

add_action('wp_ajax_nopriv_admin_review_clear_review_mes', 'admin_review_clear_review_mes');//动作钩子
add_action('wp_ajax_admin_review_clear_review_mes', 'admin_review_clear_review_mes');//动作钩子


/*
 *
 * 模拟登录
 * */


function admin_review_simulated_user_login()
{

    $user_id = $_POST['user_id'];
    $simulated_time = $_POST['simulated_time'];
    $isHeikaVip=$_POST['isHeikaVip'];

    $reviewMesHandle=new reviewMesHandleTest($user_id, $simulated_time, $isHeikaVip);





}

add_action('wp_ajax_nopriv_admin_review_simulated_user_login', 'admin_review_simulated_user_login');//动作钩子
add_action('wp_ajax_admin_review_simulated_user_login', 'admin_review_simulated_user_login');//动作钩子




function admin_review_clean_user_all_data()
{




    $user_id = $_POST['user_id'];





    update_user_meta($user_id, "heika_daily_learning_task_content", "");

    update_user_meta($user_id, "heika_daily_learning_task_content_total", "");


    update_user_meta($user_id, "heika_check_in_detail", "");



    update_user_meta($user_id, "learn_detail", "");//保存数据库


    update_user_meta($user_id, "today_learn_detail", "");//清空今日学习行为
    update_user_meta($user_id, "today_review_over_num", "");//清空今日学习行为


    update_user_meta($user_id, "alter_review_time", 0);//复习计划更新时间清0

    update_user_meta($user_id, "alter_heika_data_time", 0);//黑卡数据更新时间清0

    update_user_meta($user_id, "review_mes", "");//清理复习计划

    update_user_meta($user_id, "course_rate_of_progress", "");//清理课程进度


    echo 1;






}

add_action('wp_ajax_nopriv_admin_review_clean_user_all_data', 'admin_review_clean_user_all_data');//动作钩子
add_action('wp_ajax_admin_review_clean_user_all_data', 'admin_review_clean_user_all_data');//动作钩子




function admin_review_budaka()
{




    $user_id = $_POST['user_id'];

    $day_timestamp=$_POST['day_timestamp'];


    $heika_check_in_detail = get_user_meta($user_id, "heika_check_in_detail", true);//打卡详情json

    $heika_check_in_detail_arr = array();//打卡详情arr

    if ($heika_check_in_detail && $heika_check_in_detail != "null") {

        $heika_check_in_detail_arr = json_decode($heika_check_in_detail);

    }


    //遍历打卡详情arr

    //若数组元素大于0


    if (count($heika_check_in_detail_arr) > 0) {




        $is_have=false;


        foreach($heika_check_in_detail_arr as $k=>$v){

            if($v->day_timestamp==$day_timestamp){

                $is_have=true;

                $heika_check_in_detail_arr[$k]->is_finish_heika_daily_learning_task=1;
                break;

            }

        };


        if($is_have){
            if(update_user_meta($user_id, "heika_check_in_detail", json_encode($heika_check_in_detail_arr))){
                echo 1;
                return;
            }
        }





    }










    echo "";






}

add_action('wp_ajax_nopriv_admin_review_budaka', 'admin_review_budaka');//动作钩子
add_action('wp_ajax_admin_review_budaka', 'admin_review_budaka');//动作钩子



function admin_review_go_continuously_learning_days_cash_back()
{




    $user_id = $_POST['user_id'];


    $continuously_learning_days_cash_back_chance=get_user_meta($user_id,"continuously_learning_days_cash_back_chance",true);

    if(!$continuously_learning_days_cash_back_chance){
        $continuously_learning_days_cash_back_chance=0;
    }

    $cash_back_times=get_user_meta($user_id,"cash_back_times",true);

    if(!$cash_back_times){
        $cash_back_times=0;

    }


    if($continuously_learning_days_cash_back_chance>0&&$cash_back_times<6){
        //有连续返现机会，并且返现次数小于6



        $continuously_learning_days_cash_back_chance-=1;
        $cash_back_times+=1;


        if(update_user_meta($user_id,"continuously_learning_days_cash_back_chance",$continuously_learning_days_cash_back_chance)&&update_user_meta($user_id,"cash_back_times",$cash_back_times)){
            echo 1;

            return;
        }

    }


    echo "";



}

add_action('wp_ajax_nopriv_admin_review_go_continuously_learning_days_cash_back', 'admin_review_go_continuously_learning_days_cash_back');//动作钩子
add_action('wp_ajax_admin_review_go_continuously_learning_days_cash_back', 'admin_review_go_continuously_learning_days_cash_back');//动作钩子



function admin_review_go_total_learning_days_cash_back()
{



    $user_id = $_POST['user_id'];


    $total_learning_days_cash_back_chance=get_user_meta($user_id,"total_learning_days_cash_back_chance",true);

    if(!$total_learning_days_cash_back_chance){
        $total_learning_days_cash_back_chance=0;
    }

    $cash_back_times=get_user_meta($user_id,"cash_back_times",true);

    if(!$cash_back_times){
        $cash_back_times=0;

    }


    if($total_learning_days_cash_back_chance>0&&$cash_back_times<6){
        //有连续返现机会，并且返现次数小于6



        $total_learning_days_cash_back_chance-=1;
        $cash_back_times+=1;


        if(update_user_meta($user_id,"total_learning_days_cash_back_chance",$total_learning_days_cash_back_chance)&&update_user_meta($user_id,"cash_back_times",$cash_back_times)){

            echo 1;

            return;
        }

    }


    echo "";



}

add_action('wp_ajax_nopriv_admin_review_go_total_learning_days_cash_back', 'admin_review_go_total_learning_days_cash_back');//动作钩子
add_action('wp_ajax_admin_review_go_total_learning_days_cash_back', 'admin_review_go_total_learning_days_cash_back');//动作钩子






function reset_alter_review_time()
{

    $user_id = $_POST['user_id'];

    if (update_user_meta($user_id, "alter_review_time", 0)) {
        echo 1;
        return;
    }
    echo 0;


}

add_action('wp_ajax_nopriv_reset_alter_review_time', 'reset_alter_review_time');//动作钩子
add_action('wp_ajax_reset_alter_review_time', 'reset_alter_review_time');//动作钩子


function reset_alter_heika_data_time()
{

    $user_id = $_POST['user_id'];

    if (update_user_meta($user_id, "alter_heika_data_time", 0)) {
        echo 1;
        return;
    }
    echo 0;


}

add_action('wp_ajax_nopriv_reset_alter_heika_data_time', 'reset_alter_heika_data_time');//动作钩子
add_action('wp_ajax_reset_alter_heika_data_time', 'reset_alter_heika_data_time');//动作钩子


function reset_heika_daily_learning_task_altertime()
{

    $user_id = $_POST['user_id'];

    if (update_user_meta($user_id, "heika_daily_learning_task_altertime", "")) {
        echo 1;
        return;
    }
    echo 0;


}

add_action('wp_ajax_nopriv_reset_heika_daily_learning_task_altertime', 'reset_heika_daily_learning_task_altertime');//动作钩子
add_action('wp_ajax_reset_heika_daily_learning_task_altertime', 'reset_heika_daily_learning_task_altertime');//动作钩子

function reset_current_user_learn_detail()
{


    $user_id = $_POST['user_id'];

    if (update_user_meta($user_id, "learn_detail", "")) {
        echo 1;
        return;
    }
    echo 0;

}

add_action('wp_ajax_nopriv_reset_current_user_learn_detail', 'reset_current_user_learn_detail');//动作钩子
add_action('wp_ajax_reset_current_user_learn_detail', 'reset_current_user_learn_detail');//动作钩子


function reset_current_user_today_learn_detail()
{


    $user_id = $_POST['user_id'];

    if (update_user_meta($user_id, "today_learn_detail", "")) {
        echo 1;
        return;
    }
    echo 0;

}

add_action('wp_ajax_nopriv_reset_current_user_today_learn_detail', 'reset_current_user_today_learn_detail');//动作钩子
add_action('wp_ajax_reset_current_user_today_learn_detail', 'reset_current_user_today_learn_detail');//动作钩子


function reset_current_user_heika_check_in_detail()
{
    $user_id = $_POST['user_id'];

    if (update_user_meta($user_id, "heika_check_in_detail", "")) {
        echo 1;
        return;
    }
    echo 0;
}

add_action('wp_ajax_nopriv_reset_current_user_heika_check_in_detail', 'reset_current_user_heika_check_in_detail');//动作钩子
add_action('wp_ajax_reset_current_user_heika_check_in_detail', 'reset_current_user_heika_check_in_detail');//动作钩子

function reset_current_user_heika_daily_learning_task_content()
{

    $user_id = $_POST['user_id'];

    if (update_user_meta($user_id, "heika_daily_learning_task_content", "")) {
        echo 1;
        return;
    }
    echo 0;

}

add_action('wp_ajax_nopriv_reset_current_user_heika_daily_learning_task_content', 'reset_current_user_heika_daily_learning_task_content');//动作钩子
add_action('wp_ajax_reset_current_user_heika_daily_learning_task_content', 'reset_current_user_heika_daily_learning_task_content');//动作钩子



function reset_current_user_heika_daily_learning_task_content_total()
{

    $user_id = $_POST['user_id'];

    if (update_user_meta($user_id, "heika_daily_learning_task_content_total", "")) {
        echo 1;
        return;
    }
    echo 0;

}

add_action('wp_ajax_nopriv_reset_current_user_heika_daily_learning_task_content_total', 'reset_current_user_heika_daily_learning_task_content_total');//动作钩子
add_action('wp_ajax_reset_current_user_heika_daily_learning_task_content_total', 'reset_current_user_heika_daily_learning_task_content_total');//动作钩子


function knowFinalTest()
{
    $user_id = $_POST['user_id'];
    update_user_meta($user_id, "is_user_know_final_test", 1);

}

add_action('wp_ajax_nopriv_knowFinalTest', 'knowFinalTest');//动作钩子
add_action('wp_ajax_knowFinalTest', 'knowFinalTest');//动作钩子


function mistake_submit()
{
    global $wpdb;
    $user_id = $_POST['user_id'];
    $wrong_catname = $_POST['wrong_catname'];
    $wrong_type = $_POST['wrong_type'];
    $wrong_content = $_POST['wrong_content'];
    $wrong_title = $_POST['wrong_title'];

    $new_mistake = array(
        'user_id' => $user_id,
        'wrong_catname' => $wrong_catname,
        'wrong_type' => $wrong_type,
        'wrong_content' => $wrong_content,
        'wrong_title' => $wrong_title,
        'submit_timestamp' => current_time("timestamp")
    );
    $wpdb->insert($wpdb->prefix . 'wrong_list', $new_mistake);
    echo "";

}

add_action('wp_ajax_nopriv_mistake_submit', 'mistake_submit');//动作钩子
add_action('wp_ajax_mistake_submit', 'mistake_submit');//动作钩子


/*
 * record_learning_mes
 * 用来记录某些用户进入了课程，却没有生成复习计划，届时提醒
 *
 * */

function record_learning_mes()
{

    $user_id = $_POST['user_id'];
    $post_id = $_POST['post_id'];
    add_user_learning_mes($user_id, $post_id);//记录课程信息


}

add_action('wp_ajax_nopriv_record_learning_mes', 'record_learning_mes');//动作钩子
add_action('wp_ajax_record_learning_mes', 'record_learning_mes');//动作钩子


/*
 *
 *接收群组创建信息
 *
 *
 * */


function createGroupAjax()
{

}


add_action('wp_ajax_nopriv_createGroupAjax', 'createGroupAjax');//动作钩子
add_action('wp_ajax_createGroupAjax', 'createGroupAjax');//动作钩子


/*
 *
 *
 * 修改/更新 课程学习关卡
 *
 * */


function update_course_learning_stage_design()
{

    /*课程ID*/
    $post_id = $_POST['post_id'];

    /*课程关卡类型*/
    $stageType = $_POST['stageType'];
    $stageType = urlencode($stageType);


    /*课程大分类*/
    $classifyType = $_POST['classifyType'];
    $classifyType = urlencode($classifyType);


    /*时间戳*/
    $timestamp = ($_POST['timestamp']) ? ($_POST['timestamp']) : "";//时间戳
    $timestamp = urlencode($timestamp);

    /*标题和主旨*/
    $headline = ($_POST['headline']) ? ($_POST['headline']) : "";//标题


    $purport = $headline . "_" . current_time("timestamp");
    $purport = urlencode($purport);

    $headline = urlencode($headline);


    /*文本1*/

    $showtext_1 = ($_POST['showtext_1']) ? ($_POST['showtext_1']) : "";
    $showtext_1 = urlencode($showtext_1);

    /*文本2*/

    $showtext_2 = ($_POST['showtext_2']) ? ($_POST['showtext_2']) : "";
    $showtext_2 = urlencode($showtext_2);

    /*课程名*/

    $cat_name = ($_POST['cat_name']) ? ($_POST['cat_name']) : "";
    $cat_name = urlencode($cat_name);


    /*品牌名*/

    $brand_name = ($_POST['brand_name']) ? ($_POST['brand_name']) : "";
    $brand_name = urlencode($brand_name);

    /*begin subhead*/
    $subhead = ($_POST['subhead']) ? ($_POST['subhead']) : "";
    $subhead = urlencode($subhead);


    /*图片地址*/
    $img = ($_POST['img']) ? ($_POST['img']) : "";//图片地址
    $img = urlencode($img);


    /*英文句子*/
    $english_sentence = ($_POST['english_sentence']) ? ($_POST['english_sentence']) : "";
    $english_sentence = urlencode($english_sentence);


    /*有标点且格式化英文句子*/
    $english_sentence_has_dot = "";


    /*无标点英文句子*/
    $english_sentence_no_dot = "";

    /*中文句子*/
    $chinese_sentence = ($_POST['chinese_sentence']) ? ($_POST['chinese_sentence']) : "";//图片地址
    $chinese_sentence = urlencode($chinese_sentence);


    /*讲解1*/
    $explain_1 = ($_POST['explain_1']) ? ($_POST['explain_1']) : "";//讲解1
    $explain_1 = urlencode($explain_1);

    /*讲解2*/
    $explain_2 = ($_POST['explain_2']) ? ($_POST['explain_2']) : "";//讲解2
    $explain_2 = urlencode($explain_2);

    /*讲解3*/
    $explain_3 = ($_POST['explain_3']) ? ($_POST['explain_3']) : "";//讲解3
    $explain_3 = urlencode($explain_3);

    /*讲解4*/
    $explain_4 = ($_POST['explain_4']) ? ($_POST['explain_4']) : "";//讲解4
    $explain_4 = urlencode($explain_4);

    /*学习单词*/
    $learn_word = ($_POST['learn_word']) ? ($_POST['learn_word']) : "";
    $learn_word = urlencode($learn_word);


    /*单词释义*/
    $word_explain = ($_POST['word_explain']) ? ($_POST['word_explain']) : "";
    $word_explain = urlencode($word_explain);

    /*单词音标*/
    $word_phonetic = ($_POST['word_phonetic']) ? ($_POST['word_phonetic']) : "";
    $word_phonetic = urlencode($word_phonetic);


    /*题干1*/
    $question_stem_1 = ($_POST['question_stem_1']) ? ($_POST['question_stem_1']) : "";
    $question_stem_1 = urlencode($question_stem_1);


    /*题干2*/
    $question_stem_2 = ($_POST['question_stem_2']) ? ($_POST['question_stem_2']) : "";
    $question_stem_2 = urlencode($question_stem_2);

    /*选项1*/
    $option_1 = ($_POST['option_1']) ? ($_POST['option_1']) : "";
    $option_1 = urlencode($option_1);

    /*选项2*/
    $option_2 = ($_POST['option_2']) ? ($_POST['option_2']) : "";
    $option_2 = urlencode($option_2);


    /*选项3*/
    $option_3 = ($_POST['option_3']) ? ($_POST['option_3']) : "";
    $option_3 = urlencode($option_3);


    /*正确选项*/
    $right_option = ($_POST['right_option']) ? ($_POST['right_option']) : "";
    $right_option = urlencode($right_option);


    /*绑定知识点*/
    $bindKnowledge = ($_POST['bindKnowledge']) ? ($_POST['bindKnowledge']) : "";//绑定知识点
    $bindKnowledge = urlencode($bindKnowledge);//绑定知识点


    /*行走步数*/
    $review_go_num = ($_POST['review_go_num']) ? ($_POST['review_go_num']) : 0;//行走步数
    $review_go_num = urlencode($review_go_num);


    /*插入位置*/
    $insertLocation = $_POST['insertLocation'];//插入位置
    $insertLocation = urlencode($insertLocation);


    $newArr = array(
        "stageType" => $stageType,
        "classifyType" => $classifyType,
        "timestamp" => $timestamp,
        "headline" => $headline,
        "showtext_1" => $showtext_1,
        "showtext_2" => $showtext_2,
        "cat_name" => $cat_name,
        "brand_name" => $brand_name,
        "subhead" => $subhead,

        "purport" => $purport,
        "img" => $img,

        "english_sentence" => $english_sentence,
        "english_sentence_has_dot" => $english_sentence_has_dot,
        "english_sentence_no_dot" => $english_sentence_no_dot,
        "chinese_sentence" => $chinese_sentence,

        "explain_1" => $explain_1,
        "explain_2" => $explain_2,
        "explain_3" => $explain_3,
        "explain_4" => $explain_4,

        "question_stem_1" => $question_stem_1,
        "question_stem_2" => $question_stem_2,
        "option_1" => $option_1,
        "option_2" => $option_2,
        "option_3" => $option_3,
        "right_option" => $right_option,


        "learn_word" => $learn_word,
        "word_explain" => $word_explain,
        "word_phonetic" => $word_phonetic,


        "bindKnowledge" => $bindKnowledge,
        "review_go_num" => $review_go_num
    );


    $course_learning_stage_json = get_post_meta($post_id, "course_learning_stage", true);


    /*
     *
     * 保存newArr 到$course_learning_stage_arr
     * */


    if ($course_learning_stage_json) {

        $course_learning_stage_arr = json_decode($course_learning_stage_json);

        if ($insertLocation == 0) {
            array_push($course_learning_stage_arr, $newArr);//压入总数组
        } else {
            $key = $insertLocation - 1;//插入位置
            $new_course_learning_stage_arr = array();//临时数组
            $i = 0;//临时数组键位
            for ($j = 0; $j < count($course_learning_stage_arr); $j++) {

                if ($i == $key) {//临时数组键位和插入位置相同
                    array_push($new_course_learning_stage_arr, $newArr);

                    $j--;
                } else {

                    array_push($new_course_learning_stage_arr, $course_learning_stage_arr[$j]);

                }
                $i++;

            }

            $course_learning_stage_arr = $new_course_learning_stage_arr;


        }

    } else {
        $course_learning_stage_arr = array();
        array_push($course_learning_stage_arr, $newArr);//压入总数组

    }


    /*
     * 转换成新的json数据 $new_course_learning_stage_json
     * */


    $new_course_learning_stage_json = json_encode($course_learning_stage_arr);

    //$new_course_stage_json=urldecode(json_encode(urlencode($course_stage_arr)));

    if (update_post_meta($post_id, "course_learning_stage", $new_course_learning_stage_json)) {
        echo $new_course_learning_stage_json;
        return;

    }


    echo 0;

}


add_action('wp_ajax_nopriv_update_course_learning_stage_design', 'update_course_learning_stage_design');//动作钩子
add_action('wp_ajax_update_course_learning_stage_design', 'update_course_learning_stage_design');//动作钩子


function delete_course_learning_stage_design()
{


    $post_id = $_POST['post_id'];//课程ID

    $key = $_POST['key'];//关卡类型

    $course_stage_json = get_post_meta($post_id, "course_learning_stage", true);

    if ($course_stage_json) {
        $course_stage_arr = json_decode($course_stage_json);

        array_splice($course_stage_arr, $key, 1);

        $new_course_stage_json = json_encode($course_stage_arr);
        if (update_post_meta($post_id, "course_learning_stage", $new_course_stage_json)) {
            echo 1;
            return;

        }
    }


    echo 0;

}


add_action('wp_ajax_nopriv_delete_course_learning_stage_design', 'delete_course_learning_stage_design');//动作钩子
add_action('wp_ajax_delete_course_learning_stage_design', 'delete_course_learning_stage_design');//动作钩子


function alter_course_learning_stage_design()
{


    $post_id = $_POST['post_id'];//课程ID

    $key = $_POST['key'];//关卡类型
    $objArr = $_POST['obj'];//关卡对象
    $newArr = array();

    /*处理对象内中文*/


    foreach ($objArr as $k => $o) {
        if ($k == "purport") {
            $newArr[$k] = urlencode($objArr["headline"] . "_" . current_time("timestamp"));
        } else {
            $newArr[$k] = urlencode($o);
        }
    }


    //myDump($newArr);

    $course_stage_json = get_post_meta($post_id, "course_learning_stage", true);

    if ($course_stage_json) {
        $course_stage_arr = json_decode($course_stage_json);


        for ($i = 0; $i < count($course_stage_arr); $i++) {

            if ($i == $key) {

                $course_stage_arr[$i] = array();
                $course_stage_arr[$i] = $newArr;
                break;
            }

        }


        $new_course_stage_json = json_encode($course_stage_arr);
        if (update_post_meta($post_id, "course_learning_stage", $new_course_stage_json)) {
            echo 1;
            return;

        }
    }


    echo 0;


}


add_action('wp_ajax_nopriv_alter_course_learning_stage_design', 'alter_course_learning_stage_design');//动作钩子
add_action('wp_ajax_alter_course_learning_stage_design', 'alter_course_learning_stage_design');//动作钩子


function save_audio_src()
{


    $post_id = $_POST['post_id'];//课程ID

    $course_audio_src = $_POST['course_audio_src'];//关卡类型

    if (update_post_meta($post_id, "course_audio_src", $course_audio_src)) {
        echo 1;
        return;

    }

    echo 0;

}


add_action('wp_ajax_nopriv_save_audio_src', 'save_audio_src');//动作钩子
add_action('wp_ajax_save_audio_src', 'save_audio_src');//动作钩子



function save_audio_src_two()
{


    $post_id = $_POST['post_id'];//课程ID

    $course_audio_src = $_POST['course_audio_src'];//关卡类型

    if (update_post_meta($post_id, "course_audio_src_two", $course_audio_src)) {
        echo 1;
        return;

    }

    echo 0;

}


add_action('wp_ajax_nopriv_save_audio_src_two', 'save_audio_src_two');//动作钩子
add_action('wp_ajax_save_audio_src_two', 'save_audio_src_two');//动作钩子


function input_course_json()
{


    $post_id = $_POST['post_id'];//课程ID

    $obj = $_POST['obj'];//关卡类型


    //myDump($objArr);

    $obj = stripslashes($obj);

    //$objArr=json_decode($obj);


    //myDump($objArr);

    /* foreach($objArr as $o){

         foreach($o as &$v){

             $v=urlencode($v);
         }
     }*/

    //$new_course_stage_json=json_encode($objArr);
    if (update_post_meta($post_id, "course_learning_stage", $obj)) {
        echo 1;
        return;

    }

    echo 0;


}


add_action('wp_ajax_nopriv_input_course_json', 'input_course_json');//动作钩子
add_action('wp_ajax_input_course_json', 'input_course_json');//动作钩子



/*
 *
 * 卡片库增删改差
 * */

function alter_course_card_base(){

    $post_id = $_POST['post_id'];//课程ID


    $content=$_POST['content'];



    if (update_post_meta($post_id, "course_card_base", $content)) {
        echo $content;
        return;

    }


    echo 0;







    //echo $content;
}

add_action('wp_ajax_nopriv_alter_course_card_base', 'alter_course_card_base');//动作钩子
add_action('wp_ajax_alter_course_card_base', 'alter_course_card_base');//动作钩子




function alter_course_learning_stage_json(){

    $post_id = $_POST['post_id'];//课程ID


    $content=$_POST['content'];



    if (update_post_meta($post_id, "course_learning_stage", $content)) {
        echo $content;
        return;

    }


    echo 0;







    //echo $content;
}

add_action('wp_ajax_nopriv_alter_course_learning_stage_json', 'alter_course_learning_stage_json');
add_action('wp_ajax_alter_course_learning_stage_json', 'alter_course_learning_stage_json');


/*
 *
 * 后台存储课程关卡json
 *
 * */


function save_course_learning_stage_json(){

    $post_id = $_POST['post_id'];//课程ID


    $content=$_POST['content'];



    if (update_post_meta($post_id, "course_learning_stage_json", $content)) {
        echo $content;
        return;

    }


    echo 0;







    //echo $content;
}

add_action('wp_ajax_nopriv_save_course_learning_stage_json', 'save_course_learning_stage_json');
add_action('wp_ajax_save_course_learning_stage_json', 'save_course_learning_stage_json');








function set_user_reject_review_mes()
{


    $user_id = $_POST['user_id'];//用户ID
    $k = $_POST['k'];

    if (update_user_meta($user_id, "is_reject_reiview_mes", $k)) {
        echo 1;
        return;
    }
    echo 0;


}


add_action('wp_ajax_nopriv_set_user_reject_review_mes', 'set_user_reject_review_mes');//动作钩子
add_action('wp_ajax_set_user_reject_review_mes', 'set_user_reject_review_mes');//动作钩子


function clean_user_all_review_mes()
{

    $user_id = $_POST['user_id'];


    $now_time = current_time("timestamp");
    $today_morning = strtotime(date('Y-m-d 00:00:00', $now_time));//当天0点
    $today_night = $today_morning + 24 * 3600 - 1;//当天23.59


    $stand_day_time = $today_morning + 20;

    /*
     * 清理今天的签到
     * */


    $heika_check_in_detail = get_user_meta($user_id, "heika_check_in_detail", true);//签到详情

    if ($heika_check_in_detail) {

        $heika_check_in_detail_arr = json_decode($heika_check_in_detail);

        if (count($heika_check_in_detail_arr) > 0) {


            $last_sign_obj = end($heika_check_in_detail_arr);//上次签到时间是最后一个元素

            if($last_sign_obj->day_timestamp==$stand_day_time){


                if ($last_sign_obj->is_finish_heika_daily_learning_task == 1) {
                    $last_sign_obj->is_finish_heika_daily_learning_task = 0;
                }

                array_pop($heika_check_in_detail_arr);

                array_push($heika_check_in_detail_arr, $last_sign_obj);

                $new_heika_check_in_detail = json_encode($heika_check_in_detail_arr);
                update_user_meta($user_id, "heika_check_in_detail", $new_heika_check_in_detail);

            }



        }
    }


    /*
    * 清理今天学习行为
    * */


    $learn_detail = get_user_meta($user_id, "learn_detail", true);//学习行为


    if ($learn_detail) {
        $learn_detail_arr = json_decode($learn_detail);

        //myDump($learn_detail_arr);

        for ($i = 0; $i < count($learn_detail_arr); $i++) {
            if ($learn_detail_arr[$i]->timestamp >= $today_morning) {
                //echo "位置：".$i;
                array_splice($learn_detail_arr, $i, 1);
                $i--;
            }
        }


        if (count($learn_detail_arr) > 0) {
            $new_learn_detail = json_encode($learn_detail_arr);
        } else {
            $new_learn_detail = "";

        }

        //myDump($new_learn_detail);
        update_user_meta($user_id, "learn_detail", $new_learn_detail);//保存数据库


    }


    update_user_meta($user_id, "today_learn_detail", "");//清空今日学习行为


    update_user_meta($user_id, "alter_review_time", 0);//复习计划更新时间清0

    update_user_meta($user_id, "alter_heika_data_time", 0);//黑卡数据更新时间清0

    update_user_meta($user_id, "review_mes", "");//清理复习计划

    update_user_meta($user_id, "course_rate_of_progress", "");//清理课程进度


    echo 1;


}


add_action('wp_ajax_nopriv_clean_user_all_review_mes', 'clean_user_all_review_mes');//动作钩子
add_action('wp_ajax_clean_user_all_review_mes', 'clean_user_all_review_mes');//动作钩子


function select_wrong_data()
{


    $wrong_catname = $_POST['wrong_catname'];//用户ID
    $wrong_title = $_POST['wrong_title'];//用户ID
    $arr = array();
    global $wpdb;


    $selectStr = "SELECT wrong_content FROM `" . $wpdb->prefix . "wrong_list` WHERE wrong_catname='" . $wrong_catname . "' and wrong_title='" . $wrong_title . "'";
    $ress = $wpdb->get_results($selectStr);
    //myDump($selectStr);
    // myDump($ress);
    foreach ($ress as $res) {
        array_push($arr, $res->wrong_content);

    }

    //myDump($arr);
    $arr = array_unique($arr);
    $arr = array_values($arr);
    myDump($arr);


}


add_action('wp_ajax_nopriv_select_wrong_data', 'select_wrong_data');//动作钩子
add_action('wp_ajax_select_wrong_data', 'select_wrong_data');//动作钩子


/*
 * 课程创建后台检索推送文章数据
 * */


function select_push_post_mes(){

    $post_id = $_POST['post_id'];//课程ID

    if(get_post($post_id)){
        //myDump(get_post($post_id));

        $title=get_post($post_id)->post_title;


        echo "推送课程ID：".$post_id;

        echo "<br/>";


        echo "课程名：".$title;

        return;
    }


    echo "";



}

add_action('wp_ajax_nopriv_select_push_post_mes', 'select_push_post_mes');
add_action('wp_ajax_select_push_post_mes', 'select_push_post_mes');








