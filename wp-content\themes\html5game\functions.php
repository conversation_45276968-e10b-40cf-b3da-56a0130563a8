<?php
/**
 * @功能概述: HTML5Game WordPress主题核心函数文件
 *           包含主题的所有核心功能、类加载、工具函数和WordPress钩子
 *           支持英语学习游戏、用户管理、VIP系统、微信集成等功能
 */

$log_prefix = '[ThemeFunctions] '; // 全局日志前缀
error_log($log_prefix . '[初始化] 开始加载HTML5Game主题functions.php');

/**
 * @功能概述: 有赞商城配置 - 定义有赞店铺的URL链接
 *           用于集成有赞商城的商品展示和购买功能
 */
// 有赞店铺主页链接
$youzan_url = "https://h5.youzan.com/v2/showcase/homepage?kdt_id=17925206&reft=1526356079623_1527846855945&spm=f44266831_ag17925206";
// 有赞所有产品页面链接
$youzan_all_pro_url = "https://h5.youzan.com/v2/feature/MPmiiPyj1m";
error_log($log_prefix . '[配置] 有赞商城URL配置完成');

/**
 * @功能概述: 核心类文件加载模块
 *           按依赖顺序加载主题所需的所有核心类文件
 *           包括微信集成、用户管理、课程数据、文章处理等功能模块
 */
error_log($log_prefix . '[类加载] 开始加载核心类文件');

// 加载微信类 - 处理微信API集成、用户认证、支付等功能
require_once 'class/wechat.class.php';
error_log($log_prefix . '[类加载] wechat.class.php 加载完成');

// 加载课程类 - 处理英语学习课程的数据管理和逻辑
require_once 'class/courseData.class.php';
error_log($log_prefix . '[类加载] courseData.class.php 加载完成');

// 加载文章类 - 处理WordPress文章的扩展功能和数据操作
require_once 'class/postData.class.php';
error_log($log_prefix . '[类加载] postData.class.php 加载完成');

// 加载用户类 - 处理用户数据、VIP状态、学习进度等核心用户功能
require_once 'class/userData.class.php';
error_log($log_prefix . '[类加载] userData.class.php 加载完成');

// 加载用户测试类 - 处理用户测试环境的特殊逻辑
require_once 'class/userDataTest.class.php';
error_log($log_prefix . '[类加载] userDataTest.class.php 加载完成');

// 加载用户总数据类 - 处理用户的统计和汇总数据
require_once 'class/userTotalData.class.php';
error_log($log_prefix . '[类加载] userTotalData.class.php 加载完成');

// 加载复习消息处理类 - 处理学习复习相关的消息和通知
require_once 'class/reviewMesHandle.class.php';
error_log($log_prefix . '[类加载] reviewMesHandle.class.php 加载完成');

// 加载复习消息测试处理类 - 测试环境的复习消息处理
require_once 'class/reviewMesHandleTest.class.php';
error_log($log_prefix . '[类加载] reviewMesHandleTest.class.php 加载完成');

// 加载黑卡处理类 - 处理特殊会员卡相关功能
require_once 'class/heikaHandle.class.php';
error_log($log_prefix . '[类加载] heikaHandle.class.php 加载完成');

// 加载目录类 - 处理WordPress分类目录的扩展功能
require_once 'class/catData.class.php';
error_log($log_prefix . '[类加载] catData.class.php 加载完成');

/**
 * @功能概述: 主题配置和扩展功能模块加载
 *           加载主题的后台设置、自定义字段、AJAX处理等扩展功能
 */
error_log($log_prefix . '[扩展加载] 开始加载主题扩展功能');

// 引入后台设置 - WordPress后台主题选项页面
require_once("includes/theme_options.php");
error_log($log_prefix . '[扩展加载] theme_options.php 加载完成');

// 引入目录页函数 - 分类目录页面的自定义功能
require_once("includes/simple-term-meta.php");
error_log($log_prefix . '[扩展加载] simple-term-meta.php 加载完成');

// 引入自定义文章字段 - 文章的自定义字段管理
require_once("includes/fieldFun/post_field_fun.php");
error_log($log_prefix . '[扩展加载] post_field_fun.php 加载完成');

// 引入自定义分类字段 - 分类的自定义字段管理
require_once("includes/fieldFun/cat_field_fun.php");
error_log($log_prefix . '[扩展加载] cat_field_fun.php 加载完成');

// 接收ajax - AJAX请求处理模块
require_once 'includes/ajax/ajax.php';
error_log($log_prefix . '[扩展加载] ajax.php 加载完成');

/**
 * @功能概述: 第三方API集成模块
 *           集成百度语音API等外部服务
 */
// 百度语音API - 用于语音识别和语音合成功能（当前文件缺失，已注释）
// require_once 'includes/yuyinApi/AipSpeech.php';
error_log($log_prefix . '[API加载] AipSpeech.php (百度语音API) 文件缺失，已跳过加载');

/**
 * @功能概述: 定时任务模块
 *           用于处理定期执行的后台任务，包括微信access_token自动刷新、数据清理、统计更新等
 *           每10分钟自动刷新微信服务号access_token，确保微信功能正常运行
 */
// 定时任务模块 - 包含微信access_token自动刷新功能
require_once 'includes/timerTask.php';
error_log($log_prefix . '[定时任务] 定时任务模块已启用，微信access_token将每10分钟自动刷新');

error_log($log_prefix . '[初始化] 所有核心文件加载完成，开始定义工具函数');

/**
 * @功能概述: 调试输出函数 - 格式化显示变量内容
 *           用于开发调试时美化输出变量的结构和内容
 *
 * @param {mixed} $obj - 需要输出的变量，可以是任意类型
 *
 * @return {void} 无返回值，直接输出到页面
 */
function myDump($obj)
{
    $log_prefix = '[DebugDump] ';
    error_log($log_prefix . '[调试输出] 开始格式化输出变量');

    echo "<pre>"; // 开始预格式化标签
    var_dump($obj); // 输出变量详细信息
    echo "</pre>"; // 结束预格式化标签

    error_log($log_prefix . '[调试输出] 变量输出完成');
}

/**
 * @功能概述: 获取当前文章的第一张图片URL
 *           从当前全局$post对象的内容中提取第一张图片
 *           如果没有图片则返回默认图片
 *
 * @return {string} 图片URL地址，如果没有图片则返回默认图片路径
 *
 * @执行流程:
 *   1. 使用正则表达式匹配文章内容中的img标签
 *   2. 提取第一张图片的src属性
 *   3. 如果没有图片，返回主题默认图片
 */
function catch_that_image()
{
    $log_prefix = '[ImageExtractor] ';
    global $post;

    error_log($log_prefix . '[步骤 1] 开始从当前文章提取第一张图片');

    // 步骤 1: 使用正则表达式匹配所有img标签
    preg_match_all('/<img.+src=[\'"]([^\'"]+)[\'"].*>/i', $post->post_content, $matches);
    error_log($log_prefix . '[步骤 1.1] 正则匹配完成，找到图片数量: ' . count($matches[1]));

    // 步骤 2: 获取第一张图片URL
    $first_img = $matches[1][0];
    if (!$first_img) {
        // 步骤 3: 如果没有图片，使用默认图片
        $first_img = get_bloginfo('template_directory') . "/images/default.jpg";
        error_log($log_prefix . '[步骤 3] 未找到图片，使用默认图片: ' . $first_img);
    } else {
        error_log($log_prefix . '[步骤 2] 找到第一张图片: ' . $first_img);
    }

    return $first_img;
}

/**
 * @功能概述: 根据文章ID获取指定文章的第一张图片URL
 *           从指定文章的内容中提取第一张图片，支持任意文章ID
 *
 * @param {int} $post_id - 文章ID
 *
 * @return {string|false} 成功返回图片URL，失败返回false
 *
 * @执行流程:
 *   1. 根据文章ID获取文章内容
 *   2. 验证文章内容是否存在
 *   3. 提取第一张图片URL
 *   4. 如果没有图片则返回默认图片
 */
function catch_the_image($post_id)
{
    $log_prefix = '[ImageExtractorByID] ';
    error_log($log_prefix . '[步骤 1] 开始获取文章ID: ' . $post_id . ' 的第一张图片');

    // 步骤 1: 获取指定文章的内容
    $post_content = get_post($post_id)->post_content;

    if ($post_content) {
        error_log($log_prefix . '[步骤 2] 文章内容获取成功，开始提取图片');

        // 步骤 2: 使用正则表达式匹配图片
        preg_match_all('/<img.+src=[\'"]([^\'"]+)[\'"].*>/i', $post_content, $matches);
        $first_img = $matches[1][0];

        if (!$first_img) {
            // 步骤 3: 没有图片时使用默认图片
            $first_img = get_bloginfo('template_directory') . "/images/default.jpg";
            error_log($log_prefix . '[步骤 3] 未找到图片，使用默认图片: ' . $first_img);
        } else {
            error_log($log_prefix . '[步骤 2.1] 找到第一张图片: ' . $first_img);
        }

        return $first_img;
    }

    error_log($log_prefix . '[错误] 文章ID: ' . $post_id . ' 的内容为空或不存在');
    return false;
}

/**
 * @功能概述: 获取文章图片数组函数（函数开始）
 *           此函数用于提取文章中的多张图片并返回数组
 */
/**
 * @功能概述: 获取当前文章的所有图片URL数组（除第一张外）
 *           从当前全局$post对象的内容中提取除第一张图片外的所有图片URL
 *
 * @return {array|false} 成功返回图片URL数组，没有图片返回false
 *
 * @执行流程:
 *   1. 使用正则表达式匹配文章内容中的所有img标签
 *   2. 跳过第一张图片，提取其余图片的src属性
 *   3. 返回图片URL数组或false
 */
function catch_image_arr()
{
    $log_prefix = '[ImageArrayExtractor] ';
    global $post;

    error_log($log_prefix . '[步骤 1] 开始提取当前文章的图片数组（除第一张外）');

    // 步骤 1: 使用正则表达式匹配所有img标签
    preg_match_all('/<img.+src=[\'"]([^\'"]+)[\'"].*>/i', $post->post_content, $matches);
    $img_arr = array();

    // 步骤 2: 从第二张图片开始添加到数组（跳过第一张）
    for ($i = 1; $i < count($matches[1]); $i++) {
        $img_arr[] = $matches[1][$i];
        error_log($log_prefix . '[步骤 2.' . $i . '] 添加图片: ' . $matches[1][$i]);
    }

    if (!$img_arr) {
        error_log($log_prefix . '[结果] 没有找到额外图片（除第一张外）');
        return false;
    }

    error_log($log_prefix . '[结果] 成功提取 ' . count($img_arr) . ' 张图片（除第一张外）');
    return $img_arr;
}

/**
 * @功能概述: WordPress管理栏隐藏设置
 *           在前端隐藏WordPress顶部管理工具栏
 */
add_filter('show_admin_bar', '__return_false'); // 去掉顶部adminbar
error_log($log_prefix . '[WordPress设置] 已隐藏前端管理工具栏');

/**
 * @功能概述: 移动设备检测函数
 *           通过User-Agent检测用户是否使用移动设备访问
 *
 * @return {bool} 移动设备返回true，PC返回false
 *
 * @执行流程:
 *   1. 获取并转换User-Agent为小写
 *   2. 检测各种设备类型标识
 *   3. 根据设备类型返回相应结果
 */
function isMobile()
{
    $log_prefix = '[MobileDetector] ';

    // 步骤 1: 获取User-Agent并转为小写
    $agent = strtolower($_SERVER['HTTP_USER_AGENT']);
    error_log($log_prefix . '[步骤 1] 检测User-Agent: ' . substr($agent, 0, 100) . '...');

    // 步骤 2: 检测各种设备类型
    $is_pc = (strpos($agent, 'windows nt')) ? true : false;
    $is_mac = (strpos($agent, 'mac os')) ? true : false;
    $is_iphone = (strpos($agent, 'iphone')) ? true : false;
    $is_android = (strpos($agent, 'android')) ? true : false;
    $is_ipad = (strpos($agent, 'ipad')) ? true : false;

    error_log($log_prefix . '[步骤 2] 设备检测结果 - PC:' . ($is_pc ? 'Y' : 'N') .
              ' Mac:' . ($is_mac ? 'Y' : 'N') . ' iPhone:' . ($is_iphone ? 'Y' : 'N') .
              ' Android:' . ($is_android ? 'Y' : 'N') . ' iPad:' . ($is_ipad ? 'Y' : 'N'));

    // 步骤 3: 根据设备类型返回结果
    if ($is_pc) {
        error_log($log_prefix . '[结果] 检测为PC设备');
        return false;
    }

    if ($is_mac) {
        error_log($log_prefix . '[结果] 检测为Mac设备（视为移动）');
        return true;
    }

    if ($is_iphone) {
        error_log($log_prefix . '[结果] 检测为iPhone设备');
        return true;
    }

    if ($is_android) {
        error_log($log_prefix . '[结果] 检测为Android设备');
        return true;
    }

    if ($is_ipad) {
        error_log($log_prefix . '[结果] 检测为iPad设备');
        return true;
    }

    error_log($log_prefix . '[结果] 未知设备类型，默认为移动设备');
    return true; // 默认视为移动设备
}

/**
 * @功能概述: 微信浏览器检测函数
 *           检测用户是否在微信内置浏览器中访问
 *
 * @return {bool} 微信浏览器返回true，其他返回false
 */
function is_weixin()
{
    $log_prefix = '[WeixinDetector] ';

    // 检测User-Agent中是否包含微信标识
    if (strpos($_SERVER['HTTP_USER_AGENT'], 'MicroMessenger') !== false) {
        error_log($log_prefix . '[检测] 用户使用微信浏览器访问');
        return true;
    }

    error_log($log_prefix . '[检测] 用户未使用微信浏览器');
    return false;
}

/**
 * @功能概述: PC访问处理函数
 *           非移动设备且非编辑权限用户访问时自动跳转到提示页面
 *
 * @return {void} 无返回值，符合条件时直接跳转并终止执行
 *
 * @执行流程:
 *   1. 检测是否为移动设备
 *   2. 检测用户是否有编辑权限
 *   3. 如果是PC且无编辑权限，跳转到提示页
 */
function pcVisitHandle()
{
    $log_prefix = '[PCVisitHandler] ';

    // 步骤 1: 检测设备类型和用户权限
    $is_mobile = isMobile();
    $can_edit = current_user_can('edit_posts');

    error_log($log_prefix . '[步骤 1] 访问检测 - 移动设备:' . ($is_mobile ? 'Y' : 'N') .
              ' 编辑权限:' . ($can_edit ? 'Y' : 'N'));

    // 步骤 2: 如果是PC用户且无编辑权限，执行跳转
    if (!$is_mobile && !$can_edit) {
        $redirect_url = home_url() . "/?page_id=1547";
        error_log($log_prefix . '[步骤 2] PC用户无编辑权限，跳转到: ' . $redirect_url);

        header("Location:" . $redirect_url);
        exit();
    }

    error_log($log_prefix . '[结果] 访问检查通过，允许继续访问');
}

/**
 * @功能概述: JavaScript文件注册函数
 *           注册主题JavaScript文件到WordPress脚本队列
 *
 * @param {string} $name - 脚本名称标识
 * @param {string} $src - 脚本文件相对路径
 *
 * @return {void} 无返回值，直接注册脚本到WordPress
 */
function register_enqueue_script($name, $src)
{
    $log_prefix = '[ScriptRegister] ';
    $full_path = get_bloginfo('template_directory') . $src;

    error_log($log_prefix . '[注册] 脚本名称: ' . $name . ' 路径: ' . $full_path);
    wp_register_script($name, $full_path);
}

/**
 * @功能概述: 滚动条事件终止函数
 *           当文章数量少于每页显示数量时，禁用滚动加载功能
 *
 * @param {int} $count_num - 当前文章数量
 * @param {int} $posts_per_page - 每页显示的文章数量
 *
 * @return {void} 无返回值，直接输出JavaScript代码
 */
function stopScroll($count_num, $posts_per_page)
{
    $log_prefix = '[ScrollController] ';
    error_log($log_prefix . '[检查] 文章数量: ' . $count_num . ' 每页数量: ' . $posts_per_page);

    if ($count_num < $posts_per_page) {
        error_log($log_prefix . '[执行] 文章数量不足，禁用滚动加载');
        echo '<script type="text/javascript">window.onscroll="";</script>';
    } else {
        error_log($log_prefix . '[跳过] 文章数量充足，保持滚动加载');
    }
}

/**
 * @功能概述: 访问计数统计函数
 *           记录文章和分类页面的访问次数，用于统计热门内容
 *
 * @return {void} 无返回值，直接更新数据库中的访问计数
 *
 * @执行流程:
 *   1. 检测当前页面类型（文章页或分类页）
 *   2. 获取当前访问计数
 *   3. 增加计数并更新到数据库
 */
function record_visitors()
{
    $log_prefix = '[VisitorCounter] ';

    // 步骤 1: 处理文章页面访问计数
    if (is_single()) {
        global $post;
        $post_ID = $post->ID;
        error_log($log_prefix . '[文章访问] 文章ID: ' . $post_ID);

        if ($post_ID) {
            // 步骤 2: 获取当前访问次数
            $post_views = (int)get_post_meta($post_ID, 'views', true);
            error_log($log_prefix . '[文章访问] 当前访问次数: ' . $post_views);

            // 步骤 3: 更新访问计数
            if (!update_post_meta($post_ID, 'views', ($post_views + 1))) {
                add_post_meta($post_ID, 'views', 1, true);
                error_log($log_prefix . '[文章访问] 首次访问，创建计数记录');
            } else {
                error_log($log_prefix . '[文章访问] 访问计数更新为: ' . ($post_views + 1));
            }
        }
    }

    // 步骤 4: 处理分类页面访问计数
    if (is_category()) {
        $cat_object = get_the_category();
        $cat_id = $cat_object[0]->term_id;
        error_log($log_prefix . '[分类访问] 分类ID: ' . $cat_id);

        // 步骤 5: 获取分类访问次数
        $cat_views = get_term_meta($cat_id, "views", true);
        if (!$cat_views) {
            add_term_meta($cat_id, "views", 1, true);
            error_log($log_prefix . '[分类访问] 首次访问，创建计数记录');
        } else {
            $cat_views += 1;
            update_term_meta($cat_id, "views", $cat_views);
            error_log($log_prefix . '[分类访问] 访问计数更新为: ' . $cat_views);
        }
    }
}

// 将访问计数函数挂载到wp_head钩子
add_action('wp_head', 'record_visitors');
error_log($log_prefix . '[WordPress钩子] 访问计数功能已注册到wp_head');

/**
 * @功能概述: 登录提示框加载函数
 *           加载用户登录相关的JavaScript、CSS和HTML模板
 *
 * @return {void} 无返回值，直接输出资源文件和包含模板
 */
function add_login_box()
{
    $log_prefix = '[LoginBox] ';
    $template_dir = get_bloginfo('template_directory');

    error_log($log_prefix . '[加载] 开始加载登录提示框资源');

    // 加载JavaScript文件
    echo '<script type="text/javascript" src="';
    echo $template_dir . '/js/login_box.js"></script>';
    error_log($log_prefix . '[加载] login_box.js 已输出');

    // 加载CSS样式文件
    echo '<link rel="stylesheet" type="text/css" href="';
    echo $template_dir . '/css/login_box.css"/>';
    error_log($log_prefix . '[加载] login_box.css 已输出');

    // 包含HTML模板文件
    require_once('includes/login_box.php');
    error_log($log_prefix . '[加载] login_box.php 模板已包含');
}

/**
 * @功能概述: 根据用户登录名获取用户ID
 *           通过用户登录名查询数据库获取对应的用户ID
 *
 * @param {string} $user_login - 用户登录名
 *
 * @return {int|false} 成功返回用户ID，失败返回false
 *
 * @执行流程:
 *   1. 执行SQL查询获取用户ID
 *   2. 验证查询结果
 *   3. 返回用户ID或false
 */
function get_user_id($user_login)
{
    $log_prefix = '[UserIDQuery] ';
    global $wpdb;

    error_log($log_prefix . '[查询] 用户登录名: ' . $user_login);

    // 步骤 1: 执行SQL查询
    $user_id = $wpdb->get_var("SELECT ID FROM $wpdb->users WHERE user_login = '$user_login' LIMIT 0,1");

    // 步骤 2: 验证查询结果
    if (!$user_id) {
        error_log($log_prefix . '[失败] 未找到用户: ' . $user_login);
        return false;
    }

    error_log($log_prefix . '[成功] 用户ID: ' . $user_id);
    return $user_id;

}

/**
 * @功能概述: 根据用户登录名获取用户密码
 *           通过用户登录名查询数据库获取对应的加密密码
 *
 * @param {string} $user_login - 用户登录名
 *
 * @return {string|false} 成功返回加密密码，失败返回false
 */
function get_user_password($user_login)
{
    $log_prefix = '[UserPasswordQuery] ';
    global $wpdb;

    error_log($log_prefix . '[查询] 用户登录名: ' . $user_login);

    $user_pass = $wpdb->get_var("SELECT user_pass FROM $wpdb->users WHERE user_login = '$user_login' LIMIT 0,1");
    if (!$user_pass) {
        error_log($log_prefix . '[失败] 未找到用户密码: ' . $user_login);
        return false;
    }

    error_log($log_prefix . '[成功] 已获取用户密码（已加密）');
    return $user_pass;
}

/**
 * @功能概述: 根据用户ID获取用户详细信息
 *           从usermeta表中获取用户的所有元数据信息
 *
 * @param {int} $user_id - 用户ID
 *
 * @return {array} 用户元数据数组，键为meta_key，值为meta_value
 *
 * @执行流程:
 *   1. 查询用户元数据表
 *   2. 将结果转换为关联数组
 *   3. 返回用户信息数组
 */
function get_user_info($user_id)
{
    $log_prefix = '[UserInfoQuery] ';
    global $wpdb;

    error_log($log_prefix . '[查询] 用户ID: ' . $user_id);

    // 步骤 1: 查询用户元数据
    $select_str = "SELECT meta_key,meta_value FROM  `" . $wpdb->prefix . "usermeta` WHERE user_id =" . $user_id;
    $ress = $wpdb->get_results($select_str);

    // 步骤 2: 转换为关联数组
    $user_info = array();
    foreach ($ress as $res) {
        $user_info[$res->meta_key] = $res->meta_value;
    }

    error_log($log_prefix . '[成功] 获取到 ' . count($user_info) . ' 条用户元数据');
    return $user_info;
}

/**
 * @功能概述: 用户自动登录函数
 *           通过用户登录名和ID实现用户的自动登录
 *
 * @param {string} $user_login - 用户登录名
 * @param {int} $user_id - 用户ID
 *
 * @return {void} 无返回值，直接设置用户登录状态
 */
function auto_signon($user_login, $user_id)
{
    $log_prefix = '[AutoLogin] ';

    error_log($log_prefix . '[执行] 自动登录用户: ' . $user_login . ' (ID: ' . $user_id . ')');

    wp_set_current_user($user_id);
    wp_set_auth_cookie($user_id);
    do_action('wp_login', $user_login);

    error_log($log_prefix . '[完成] 用户自动登录成功');
}

/**
 * @功能概述: 微信用户自动注册函数
 *           通过微信返回的用户信息JSON对象自动创建WordPress用户
 *
 * @param {object} $obj - 微信用户信息对象，包含unionid、openid、nickname等字段
 *
 * @return {int} 返回新创建的用户ID
 *
 * @执行流程:
 *   1. 提取微信用户信息
 *   2. 生成随机密码
 *   3. 过滤昵称中的表情符号
 *   4. 创建WordPress用户
 *   5. 保存微信相关信息到用户元数据
 */
function auto_register_user($obj)
{
    $log_prefix = '[WechatUserRegister] ';

    // 步骤 1: 提取微信用户信息
    $user_login = $obj->unionid; // 使用unionid作为登录名
    $user_openid = $obj->openid;
    $user_password = substr(md5(uniqid(microtime())), 0, 10); // 生成10位随机密码

    error_log($log_prefix . '[步骤 1] 开始注册微信用户 - UnionID: ' . $user_login);

    // 步骤 2: 昵称过滤表情符号
    $user_nickname = $obj->nickname;
    $user_nickname = preg_replace('~\xEE[\x80-\xBF][\x80-\xBF]|\xEF[\x81-\x83][\x80-\xBF]~', '', $user_nickname);
    error_log($log_prefix . '[步骤 2] 昵称过滤完成: ' . $user_nickname);


    // 步骤 3: 获取用户头像和其他信息
    $user_avatar = $obj->headimgurl;
    // 注释掉的地理位置信息（如需要可启用）
    //$user_country = $obj->country;
    //$user_province = $obj->province;
    //$user_city = $obj->city;

    // 步骤 4: 初始化用户默认数据
    $user_fav_cat = ""; // 用户喜爱分类（暂时未使用）
    $user_viptime = 0; // VIP时间初始值

    error_log($log_prefix . '[步骤 3-4] 用户信息准备完成，开始创建WordPress用户');

    // 步骤 5: 创建WordPress用户
    $user_id = wp_create_user($user_login, $user_password);
    if ($user_id) {
        error_log($log_prefix . '[步骤 5] WordPress用户创建成功，用户ID: ' . $user_id);

        // 步骤 6: 保存微信相关元数据
        add_user_meta($user_id, "openid_to_hlyyin", $user_openid); // 微信OpenID
        error_log($log_prefix . '[步骤 6.1] OpenID已保存');

        add_user_meta($user_id, 'user_nickname', $user_nickname); // 用户昵称
        error_log($log_prefix . '[步骤 6.2] 昵称已保存: ' . $user_nickname);

        add_user_meta($user_id, 'user_avatar', $user_avatar); // 头像地址
        error_log($log_prefix . '[步骤 6.3] 头像地址已保存');

        add_user_meta($user_id, 'user_viptime', $user_viptime); // 用户VIP时间
        error_log($log_prefix . '[步骤 6.4] VIP时间已初始化');

        add_user_meta($user_id, 'week_mind', ""); // 周学习计划（空值初始化）
        error_log($log_prefix . '[步骤 6.5] 周学习计划已初始化');

        add_user_meta($user_id, 'user_last_login_time', current_time("timestamp")); // 最后登录时间
        error_log($log_prefix . '[步骤 6.6] 最后登录时间已设置');

        // 步骤 7: 为新用户赠送VIP时间
        inform_new_user_become_vip(2, $user_id);
        error_log($log_prefix . '[步骤 7] 新用户VIP赠送完成');

        error_log($log_prefix . '[完成] 微信用户注册成功，用户ID: ' . $user_id);
        return $user_id;
    }

    error_log($log_prefix . '[失败] WordPress用户创建失败');
    return false;
}

/**
 * @功能概述: 创建用户单词本函数
 *           为指定用户创建一个新的单词本文章
 *
 * @param {int} $user_id - 用户ID
 * @param {string} $book_title - 单词本标题
 *
 * @return {int|false} 成功返回文章ID，失败返回false
 */
function create_word_book($user_id, $book_title)
{
    $log_prefix = '[WordBookCreator] ';

    error_log($log_prefix . '[创建] 用户ID: ' . $user_id . ' 单词本标题: ' . $book_title);

    $my_word_book = array(
        'post_title' => $book_title,
        'post_content' => '',
        'post_status' => 'publish',
        'post_author' => $user_id,
        'post_category' => array(1)
    );

    $post_id = wp_insert_post($my_word_book);

    if ($post_id) {
        error_log($log_prefix . '[成功] 单词本创建成功，文章ID: ' . $post_id);
    } else {
        error_log($log_prefix . '[失败] 单词本创建失败');
    }

    return $post_id;
}

/**
 * @功能概述: 自定义Cookie过期时间函数
 *           延长WordPress认证Cookie的有效期，提升用户体验
 *
 * @param {int} $expiration - 默认过期时间（秒）
 * @param {bool} $remember - 是否记住登录状态
 *
 * @return {int} 修改后的过期时间（秒）
 */
function custom_cookie_expiration($expiration, $remember = true)
{
    $log_prefix = '[CookieExpiration] ';

    if ($remember) {
        $expiration = 31536000; // 设置为1年（365天 * 24小时 * 3600秒）
        error_log($log_prefix . '[设置] Cookie过期时间延长至1年');
    } else {
        error_log($log_prefix . '[保持] 使用默认Cookie过期时间');
    }

    return $expiration;
}

// 将自定义Cookie过期时间函数挂载到WordPress过滤器
add_filter('auth_cookie_expiration', 'custom_cookie_expiration', 99, 3);
error_log($log_prefix . '[WordPress过滤器] Cookie过期时间自定义功能已注册');

// 临时调试：检查WordPress域名选项
debug_wordpress_domain_options();


/*检查用户名是否非法*/

function checkNicename()
{
    if (is_user_logged_in()) {
        global $current_user;
        $oldNicename = get_user_meta($current_user->ID, 'user_nicename', true);
        if (!$oldNicename || strlen($oldNicename) > 21) {
            $img = get_user_meta($current_user->ID, 'user_avatar', true);

            echo '<div id="cn">';
            echo '<div class="cn_text">';
            echo '<span class="cn_text_span">设置昵称</span>';
            echo '</div>';

            echo '<div class="cn_img">';
            echo '<img src="' . $img . '">';
            echo '</div>';
            echo '<div class="cn_input">';
            echo '<span>昵称：</span>';
            echo '<input type="text" placeholder="在此输入昵称" class="cn_input_area" maxlength="7"/>';
            echo '<span id="strnum">0/7</span>';
            echo '</div>';
            echo '<div class="cn_button">';
            echo '<button id="cn_submit">确定</button>';
            echo '</div>';
            echo '</div>';


            register_enqueue_script('changeNicenameJs', '/js/changeNicenameJs.js?v1.4');
            $ajax_url = admin_url("admin-ajax.php");
            $ajax_array = array(
                'url' => $ajax_url,
                'user_id' => $current_user->ID
            );
            wp_enqueue_script('changeNicenameJs');
            wp_localize_script('changeNicenameJs', 'object', $ajax_array);
        }
    }
}


/*
 *
 *
 * 2016.8.30
 * 词典操作的增、删、改、查
 * */

//增加一条词典

function add_dict_data($word, $phonetic = "", $explains = "", $sentence = "", $eetrans = "")
{
    global $wpdb;

    $new_word = array(
        'word' => $word,
        'phonetic' => $phonetic,
        'explains' => $explains,
        'sentence' => $sentence,
        'eetrans' => $eetrans
    );
    $wpdb->insert($wpdb->prefix . 'dict', $new_word);

}

function get_dict_data($word)
{
    global $wpdb;

    $word = addslashes($word);//单词先转义

    $queryStr = 'SELECT * FROM ' . $wpdb->prefix . 'dict WHERE word="' . $word . '"';//在数据查询单词
    $res = $wpdb->get_results($queryStr);//获得结果

    //如果结果集为true
    if ($res) {
        $wordObj = $res[0]; //获取单词对象

        //过滤单词中的反斜杠,两次
        $word = stripslashes($word);
        $word = stripslashes($word);


        /*
         * 获取音标
         * */
        $phonetic = $wordObj->phonetic;//音标

        //音标处理
        if (!$phonetic) {//无音标处理
            $phonetic = "";//音标为空
        } else {
            $phonetic = stripslashes($phonetic);//去除反斜杠
        }

        /*
         *
         * 获取单词的英英释义
         * */


        $eetrans = $wordObj->eetrans;//英英
        if (!$eetrans) {//如果英英为false
            $eetrans = getEetrans($word);//去网页抓取数据，返回false或json字符串
            if (!$eetrans) {//如果结果还是false
                $eetrans = 1;//eetrans设为1
            }
            update_dict_data($word, "eetrans", $eetrans);//保存数据库
        }

        $eetrans = stripslashes($eetrans);
        $eetrans = stripslashes($eetrans);//两次反转义


        /*
        *
        * 获取单词的例句
        * */


        $sentence = $wordObj->sentence;//例句
        if (!$sentence) {//如果句子为false
            $sentence = getSentence($word);//去网页抓取数据，返回false或json字符串
            if (!$sentence) {//如果结果还是false
                $sentence = 1;//sentence设为1
            }
            update_dict_data($word, "sentence", $sentence);//保存数据库
        }

        $sentence = stripslashes($sentence);
        $sentence = stripslashes($sentence);//两次反转义


        /*
         * 获取该单词最常用释义键值
         *
         * */

        $mostusedkey = $wordObj->mostusedkey;
        if (!is_numeric($mostusedkey)) {
            $mostusedkey = "";
        }

        /*
         *
         * 获取单词中文释义
         * */
        $explains = $wordObj->explains;//释义

        $isModify = $wordObj->isModify;//是否人工修改过

        if (!empty($explains)) {//如果释义非空,把所有数据进行返回
            $jsonArr = array("word" => $word, "phonetic" => $phonetic, "explains" => $explains, "eetrans" => $eetrans, "sentence" => $sentence, "mostusedkey" => $mostusedkey, "isModify" => $isModify);
            $jsonStr = json_encode($jsonArr);
            return $jsonStr;
        } else {//如果中文释义为空，则全部不返回
            return 2;//返回2代表，词典里有，但没有释义
        }

    } //如果单词库里没有相关单词,则去API和网页抓取
    else {
        $res = getDictDataOnNet($word);//网页抓取,返回值同样为0,2,str
        return $res;
    }


}//查询词典

function get_dict_data_only_local($word)
{
    global $wpdb;

    $word = addslashes($word);//单词先转义

    $queryStr = 'SELECT * FROM ' . $wpdb->prefix . 'dict WHERE word="' . $word . '" LIMIT 0,1';//在数据查询单词
    $res = $wpdb->get_results($queryStr);//获得结果

    //如果结果集为true
    if ($res) {
        $wordObj = $res[0]; //获取单词对象

        //过滤单词中的反斜杠,两次
        $word = stripslashes($word);
        $word = stripslashes($word);


        /*
         * 获取音标
         * */
        $phonetic = $wordObj->phonetic;//音标

        //音标处理
        if (!$phonetic) {//无音标处理
            $phonetic = "";//音标为空
        } else {
            $phonetic = stripslashes($phonetic);//去除反斜杠
        }

        /*
         *
         * 获取单词的英英释义
         * */


        $eetrans = $wordObj->eetrans;//英英
        $eetrans = stripslashes($eetrans);
        $eetrans = stripslashes($eetrans);//两次反转义


        /*
        *
        * 获取单词的例句
        * */


        $sentence = $wordObj->sentence;//例句
        $sentence = stripslashes($sentence);
        $sentence = stripslashes($sentence);//两次反转义


        /*
         * 获取该单词最常用释义键值
         *
         * */

        $mostusedkey = $wordObj->mostusedkey;

        /*
         *
         * 获取单词中文释义
         * */
        $explains = $wordObj->explains;

        $isModify = $wordObj->isModify;//是否人工修改过

        $explanation = $wordObj->explanation;//是否人工修改过


        /*
         *
         * 音频地址
         * */


        $jsonArr = array("word" => $word, "phonetic" => $phonetic, "explains" => $explains, "eetrans" => $eetrans, "sentence" => $sentence, "mostusedkey" => $mostusedkey, "isModify" => $isModify, "explanation" => $explanation);
        $jsonStr = json_encode($jsonArr);
        return $jsonStr;


    } //如果单词库里没有相关单词,则去API和网页抓取
    else {
        return 0;
    }


}//从本地数据库查询词典

function update_dict_data($word, $key, $value)
{
    global $wpdb;
    $updateStr = 'UPDATE ' . $wpdb->prefix . 'dict SET ' . $key . '="' . $value . '" WHERE word="' . $word . '" LIMIT 1';
    return $wpdb->query($updateStr); //执行数据库更新
}//更新词典

function update_dict_isModify($word)
{
    global $wpdb;
    $updateStr = 'UPDATE ' . $wpdb->prefix . 'dict SET isModify=1 WHERE word="' . $word . '" LIMIT 1';
    return $wpdb->query($updateStr); //执行数据库更新

}

function getEetrans($word)
{
    //$ru="/<div\sclass=\"trans-container tab-content\">\s*<a\shref=\"\/book\/\d+\">(.*)\s*<\/a>\s*<\/div>/";
    //$ru="/<div\sid=\"tEETrans\"\sclass=\"trans-container tab-content\"\sstyle=\"display: block;\"><\/div>/";
    //$ru="/<h4>(.*)<\/h4>/";
    //$ru='/<div id="tEETrans" class="trans-container tab-content" style="display: block;"><div class="trans-container"><ul><li><ul class="ol"><li><span class="def">(.*)<\/span><\/li><\/ul><\/li><\/ul><\/div><\/div>/';
    // $ru="/<span class=\"def\">(.*)<\/span>/";


    global $wpdb;


    $url = "http://dict.youdao.com/w/eng/" . $word;//查词网页
    /*curl*/
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    $res = curl_exec($ch);//获取结果
    curl_close($ch);//关闭


    $res = preg_replace("/[\t\n\r]+/", "", $res);//去掉结果集中的多余富豪
    $arr = array();//临时数组1
    $ul_rule = "/<ul class=\"ol\".*?>.*?<\/ul>/ism";//ul正则
    preg_match_all($ul_rule, $res, $arr);//匹配

    if (!empty($arr)) {//如果第一次匹配有值则继续
        $ulArr = $arr[0];//ul匹配后的数组
        $str = array();//临时字符串
        //preg_match_all($ru2,$ulArr[0],$arr2);
        //print_r($ulArr);

        $span_rule = "/<span class=\"def\".*?>.*?<\/span>/ism";//span正则

        //找出特征字符串
        for ($i = 0; $i < count($ulArr); $i++) {
            if (preg_match($span_rule, $ulArr[$i])) {
                $str = $ulArr[$i];
            }
        }

        $arr2 = array();//临时数组2
        preg_match_all($span_rule, $str, $arr2);//再次匹配

        if (!empty($arr2)) {
            $spanArr = $arr2[0];//span匹配后的数组

            //去掉html标签
            for ($j = 0; $j < count($spanArr); $j++) {

                $spanArr[$j] = strip_tags($spanArr[$j]);//去掉每个数组元素html标签
                $spanArr[$j] = preg_replace("/\"/i", "#", $spanArr[$j]);//去掉双引号
                $spanArr[$j] = preg_replace("/\'/i", "*", $spanArr[$j]);//去掉单引号
                $spanArr[$j] = addslashes($spanArr[$j]);//把其中的标点进行转意

            }

            $updateValue = implode("@", $spanArr);//数据库更新字符串

            return $updateValue;
        }
    }
    return false;
}//从外网获取英英释义

function getSentence($word)
{
    global $wpdb;


    $url = "http://dict.youdao.com/w/eng/" . $word;//查词网页
    /*curl*/
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    $res = curl_exec($ch);//获取结果
    curl_close($ch);//关闭

    $res = preg_replace("/[\t\n\r]+/", "", $res);//去掉结果集中的多余富豪
    $arr = array();//临时数组1


    $div_rule = "/<div id=\"examplesToggle\".*?>.*?<\/div>/ism";//div正则
    preg_match_all($div_rule, $res, $arr);//匹配
    if (!empty($arr)) {
        $divStr = $arr[0][0];
        $arr2 = array();//临时数组2
        $p_rule = "/<p>.*?<\/p>/ism";
        preg_match_all($p_rule, $divStr, $arr2);
        if (!empty($arr2)) {
            $pArr = $arr2[0];


            for ($j = 0; $j < count($pArr); $j++) {
                $pArr[$j] = strip_tags($pArr[$j]);//去掉每个数组元素html标签
                $pArr[$j] = preg_replace("/\"/i", " ", $pArr[$j]);//去掉双引号
                $pArr[$j] = preg_replace("/\'/i", " ", $pArr[$j]);//去掉单引号
                $pArr[$j] = addslashes($pArr[$j]);//把其中的标点进行转意
            }


            $updateValue = implode("@", $pArr);
            return $updateValue;


            //print_r($updateValue);
        }
    }

    return false;

}//从外网获取单词例句

function getDictDataOnNet($word)
{

    $url = 'http://fanyi.youdao.com/openapi.do?keyfrom=weiyouxiin&key=1835243706&type=data&doctype=json&version=1.1&q=' . $word . '&only=dict';
    $res = file_get_contents($url);
    $jsonObj = json_decode($res);
    if ($jsonObj->errorCode != 0) {
        return false;//返回状态为false,查询错误
    } else {
        if (!$jsonObj->basic) {
            add_dict_data($word);//还是保存了数据库
            return 2;//词典里有，但没有释义
        } else {
            $phonetic = $jsonObj->basic->phonetic;
            if (!$phonetic) {
                $phonetic = "";
            }


            $explains = $jsonObj->basic->explains;
            $explainStr = implode('@', $explains);


            $sentence = getSentence($word);//网页抓取例句数据
            if (!$sentence) {//如果是false
                $sentence = 1;//设为1
            }
            $sentence = stripslashes($sentence);
            $sentence = stripslashes($sentence);//两次反转义


            /* $eetrans = getEetrans($word);//网页抓取例句数据
             if (!$eetrans) {//如果是false
                 $eetrans = 1;//设为1
             }*/
            $eetrans = "";
            $eetrans = stripslashes($eetrans);
            $eetrans = stripslashes($eetrans);//两次反转义

            //var_dump($res);
            add_dict_data($word, $phonetic, $explainStr, $sentence, $eetrans);//将刚获取的数据保存数据库

            $isModify = 0;


            $jsonArr = array("word" => $word, "phonetic" => $phonetic, "explains" => $explainStr, "eetrans" => $eetrans, "sentence" => $sentence, "isModify" => $isModify);
            $jsonStr = json_encode($jsonArr);
            return $jsonStr;

        }
    }


}//从外网获取词典数据

/*
 *
 *
 * 2017.5.30
 * 单词报错表，增删
 * */

function add_dict_error_data($wordori, $title, $content)
{
    global $wpdb;
    if (get_dict_error_data($wordori)) {
        return false;
    }

    $new_error = array(
        'wordori' => $wordori,
        'title' => $title,
        'content' => $content
    );
    return $wpdb->insert($wpdb->prefix . 'dict_error', $new_error);

}

function delete_dict_error_data($ID)
{

}


function get_dict_error_data($wordori)
{
    global $wpdb;
    $str = 'SELECT * FROM ' . $wpdb->prefix . 'dict_error WHERE wordori="' . $wordori . '"';
    $res = $wpdb->get_results($str);
    if (!empty($res)) {
        return true;
    }
    return false;
}


/*
 *
 *
 * 2017.5.30
 * 从父目录中获取子目录数组
 * */


function get_child_cat($nowCat)
{
    $child_cat_arr = array();
    $arg = array(
        'child_of' => $nowCat,
    );
    $cat_arr = get_categories($arg);
    foreach ($cat_arr as $cat) {
        //var_dump($cat);
        if (get_term_meta($cat->term_id, "isHid_value", true) != "是") {
            array_push($child_cat_arr, $cat->term_id);
        }
    }
    return $child_cat_arr;
}

function get_child_cat_without_fav($nowCat, $user_fav_arr)
{
    $child_cat_arr = array();
    $arg = array(
        'child_of' => $nowCat,
    );
    $cat_arr = get_categories($arg);
    foreach ($cat_arr as $cat) {
        //var_dump($cat);
        if (get_term_meta($cat->term_id, "isHid_value", true) != "是") {
            array_push($child_cat_arr, $cat->term_id);
        }
    }
    //myDump($user_fav_arr);
    //myDump($child_cat_arr);
    for ($i = 0; $i < count($child_cat_arr); $i++) {
        foreach ($user_fav_arr as $user_fav) {
            if ($child_cat_arr[$i] == $user_fav) {
                array_splice($child_cat_arr, $i, 1);
                $i--;
            }
        }
    }
    return $child_cat_arr;
}

/*
 *
 * 分类列表排序
 * */

function get_cat_rank_arr($child_cat_arr, $user_level = 0, $user_tags = array())
{
    $catDataArr = array();//原始分类对象数组
    $tagsArr = array();
    $newCatDataArr = array();//排序后分类对象数组
    //myDump(current($user_tags));
    //myDump(next($user_tags));
    //myDump(next($user_tags));
    /*
     * 获取原始对象数组
     * */
    foreach ($child_cat_arr as $child_cat) {
        $cat_object = get_category($child_cat);
        $catData = new catData(array($cat_object));
        array_push($catDataArr, $catData);
    }

    /*
     * 获得用户所有的兴趣标签
     * */

    if (count($user_tags) > 0) {
        foreach ($user_tags as $key => $value) {
            array_push($tagsArr, $key);
        }
    }

    if ($user_level) {

        /*
        * 兴趣标签
        * 将用户第一兴趣相同的兴趣目录往上推
        *
        * */
        if (count($tagsArr) > 0) {
            for ($i = 0; $i < count($catDataArr); $i++) {
                if ($catDataArr[$i]->catTag == $tagsArr[0]) {
                    array_push($newCatDataArr, $catDataArr[$i]);
                    array_splice($catDataArr, $i, 1);
                    $i--;
                }
            }
        }

        /*
       * 第二兴趣
       * */

        if (count($tagsArr) > 1) {
            for ($i = 0; $i < count($catDataArr); $i++) {
                if ($catDataArr[$i]->catTag == $tagsArr[1]) {
                    array_push($newCatDataArr, $catDataArr[$i]);
                    array_splice($catDataArr, $i, 1);
                    $i--;
                }
            }
        }

        /*
         * 相同能力
         * */

        for ($i = 0; $i < count($catDataArr); $i++) {
            if ($catDataArr[$i]->catDiffNum == $user_level) {
                array_push($newCatDataArr, $catDataArr[$i]);
                array_splice($catDataArr, $i, 1);
                $i--;
            }
        }

        /*
         * 第三兴趣
         * */

        if (count($tagsArr) > 2) {
            for ($i = 0; $i < count($catDataArr); $i++) {
                if ($catDataArr[$i]->catTag == $tagsArr[2]) {
                    array_push($newCatDataArr, $catDataArr[$i]);
                    array_splice($catDataArr, $i, 1);
                    $i--;
                }
            }
        }


        /*
         * 相同能力低一档
         *
         * */

        for ($i = 0; $i < count($catDataArr); $i++) {
            if ($catDataArr[$i]->catDiffNum == $user_level - 1) {
                array_push($newCatDataArr, $catDataArr[$i]);
                array_splice($catDataArr, $i, 1);
                $i--;
            }
        }

        /*
        * 相同能力高一档
        *
        * */

        for ($i = 0; $i < count($catDataArr); $i++) {
            if ($catDataArr[$i]->catDiffNum == $user_level + 1) {
                array_push($newCatDataArr, $catDataArr[$i]);
                array_splice($catDataArr, $i, 1);
                $i--;
            }
        }

        $newCatDataArr = array_merge($newCatDataArr, $catDataArr);
    } else {
        $newCatDataArr = $catDataArr;
        $sort = array(
            'direction' => 'SORT_DESC', //排序顺序标志 SORT_DESC 降序；SORT_ASC 升序
            'field' => 'catView',       //排序字段
        );
        $arrSort = array();
        foreach ($newCatDataArr AS $uniqid => $row) {
            foreach ($row AS $key => $value) {
                $arrSort[$key][$uniqid] = $value;
            }
        }
        if ($sort['direction']) {
            array_multisort($arrSort[$sort['field']], constant($sort['direction']), $newCatDataArr);
        }
    }

    return $newCatDataArr;

}

function https_post($url, $data_string = null)
{
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json',
        'Content-Length: ' . strlen($data_string)
    ));

    $result = curl_exec($ch);
    if (curl_errno($ch)) {
        print curl_error($ch);
    }
    curl_close($ch);
    return $result;

}//httpspost请求


function get_page_qrcode($scene_first_str, $scene_id)
{
    $wechat_access_token = get_option("main_wechat_access_token");
    $get_qr_code_url = "https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token=";
    $get_qr_code_url .= $wechat_access_token;//获取二维码链接地址
    $get_qr_code_scene_str = $scene_first_str . "-" . $scene_id;//场景值
    $qrcodeStr = '{"action_name": "QR_LIMIT_STR_SCENE", "action_info": {"scene": {"scene_str": "' . $get_qr_code_scene_str . '"}}}';//转为json字符串
    $res = https_post($get_qr_code_url, $qrcodeStr);//post获取ticket
    $qr_img_obj = json_decode($res);
    $qrcode_tic = $qr_img_obj->ticket;//获得ticket

    $qrcode_tic = urlencode($qrcode_tic);
    $page_we_qr_code = "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=" . $qrcode_tic;
    return $page_we_qr_code;

}//从微信服务器获取二维码图片

function page_qrcode_and_collect()
{
    /*
 *
 * 获取永久二维码
 *
 * */

    if (is_single()) {
        //文章页
        global $post;
        $scene_id = $post->ID;
        $scene_first_str = "post";

        $page_we_qr_code = get_post_meta($post->ID, "wechat_qr_code", true);
        if (!$page_we_qr_code) {
            $page_we_qr_code = get_page_qrcode($scene_first_str, $scene_id);
            add_post_meta($post->ID, "wechat_qr_code", $page_we_qr_code);
        }

    } else if (is_page()) {
        global $post;
        $scene_id = $post->ID;
        $scene_first_str = "page";
        $page_we_qr_code = get_post_meta($post->ID, "wechat_qr_code", true);
        if (!$page_we_qr_code) {
            $page_we_qr_code = get_page_qrcode($scene_first_str, $scene_id);
            add_post_meta($post->ID, "wechat_qr_code", $page_we_qr_code);
        }

    } else if (is_category()) {
        //目录页
        $cat_object = get_the_category();
        $scene_id = $cat_object[0]->term_id;
        $scene_first_str = 'cat';
        $page_we_qr_code = get_term_meta($cat_object[0]->term_id, "wechat_qr_code", true);
        if (!$page_we_qr_code) {
            $page_we_qr_code = get_page_qrcode($scene_first_str, $scene_id);
            add_term_meta($cat_object[0]->term_id, "wechat_qr_code", $page_we_qr_code);
        }

    } else {
        //主页
        $scene_id = 0;
        $scene_first_str = "home";
        $page_we_qr_code = get_option("wechat_qr_code");
        if (!$page_we_qr_code) {
            $page_we_qr_code = get_page_qrcode($scene_first_str, $scene_id);
            add_option("wechat_qr_code", $page_we_qr_code);
        }
    }


    if (is_single()) {

        $qrcode_array = array(
            'page_type' => "single",//页面类型是single
            'page_we_qr_code' => $page_we_qr_code//二维码图片，没有则是空
        );
    } else {
        $qrcode_array = array(
            'page_type' => "cat",//页面累心是cat
            'page_we_qr_code' => $page_we_qr_code//二维码图片，没有则是空
        );

    }
    register_enqueue_script('single_qrcode', '/js/single_qrcode.js?v0.1');
    wp_enqueue_script('single_qrcode');
    wp_localize_script('single_qrcode', 'single_qrcode', $qrcode_array);


}//生成页面永久二维码


function page_qrcode_and_collect_new()
{
    /*
 *
 * 获取永久二维码
 *
 * */

    if (is_single()) {
        //文章页
        global $post;
        $scene_id = $post->ID;
        $scene_first_str = "post";

        $page_we_qr_code = get_post_meta($post->ID, "wechat_qr_code", true);
        if (!$page_we_qr_code) {
            $page_we_qr_code = get_page_qrcode($scene_first_str, $scene_id);
            add_post_meta($post->ID, "wechat_qr_code", $page_we_qr_code);
        }

    } else if (is_page()) {
        global $post;
        $scene_id = $post->ID;
        $scene_first_str = "page";
        $page_we_qr_code = get_post_meta($post->ID, "wechat_qr_code", true);
        if (!$page_we_qr_code) {
            $page_we_qr_code = get_page_qrcode($scene_first_str, $scene_id);
            add_post_meta($post->ID, "wechat_qr_code", $page_we_qr_code);
        }

    } else if (is_category()) {
        //目录页
        $cat_object = get_the_category();
        $scene_id = $cat_object[0]->term_id;
        $scene_first_str = 'cat';
        $page_we_qr_code = get_term_meta($cat_object[0]->term_id, "wechat_qr_code", true);
        if (!$page_we_qr_code) {
            $page_we_qr_code = get_page_qrcode($scene_first_str, $scene_id);
            add_term_meta($cat_object[0]->term_id, "wechat_qr_code", $page_we_qr_code);
        }

    } else {
        //主页
        $scene_id = 0;
        $scene_first_str = "home";
        $page_we_qr_code = get_option("wechat_qr_code");
        if (!$page_we_qr_code) {
            $page_we_qr_code = get_page_qrcode($scene_first_str, $scene_id);
            add_option("wechat_qr_code", $page_we_qr_code);
        }
    }


    return $page_we_qr_code;

}//生成页面永久二维码

function get_app_header()
{
    require_once "includes/element/appheader.php";
}


function get_top_nav()
{
    require_once 'includes/element/apptop.php';

}//获取顶部导航

function get_nce_top()
{
    require_once 'includes/element/ncetop.php';

}//获取顶部导航

function get_tran_page()
{
    require_once 'includes/element/translate.php';
}//获取页面翻译模块

function get_bottom_nav()
{
    require_once 'includes/element/bottom_nav.php';
}

function get_login_page()
{
    require_once 'includes/element/loginpage.php';

}

function get_word_book_head()
{
    require_once 'includes/element/wordBookHead.php';

}

function get_dark_header()
{
    require_once 'includes/element/darkheader.php';

}

/*
 * 是否为生词本页面
 * */
function is_wordbook_page()
{
    global $post;
    $pageStr = get_option("g_wordbook_page", true);
    $pageArr = explode("#", $pageStr);
    $isWordBookPage = false;
    foreach ($pageArr as $page) {
        if ($page == $post->ID) {
            $isWordBookPage = true;
            break;
        }
    }
    return $isWordBookPage;

}

/*
 * 是否复习页面
 * */


function add_user_nickname_column($columns)
{
    $columns['user_nickname'] = '昵称';
    unset($columns['name']);
    unset($columns['email']);
    unset($columns['role']);
    return $columns;
}

add_filter('manage_users_columns', 'add_user_nickname_column');


function add_user_id_column($columns)
{
    $columns['ID'] = 'ID';
    var_dump($columns);
    return $columns;
}

add_filter('manage_users_columns', 'add_user_id_column');

// 显示登录时间到新增栏目
function add_new_column_value($value, $column_name, $user_id)
{
    if ('user_nickname' == $column_name) {
        $value = get_user_meta($user_id, 'user_nickname', true);
        if (!$value) {
            $value = "";
        }
    }
    if ('ID' == $column_name) {
        $value = $user_id;
    }
    return $value;
}

add_action('manage_users_custom_column', 'add_new_column_value', 10, 3);


/*
 * 后台文章列表只显示作者自身
 *
 * */
/*function mypo_parse_query_useronly($wp_query)
{
    if (strpos($_SERVER['REQUEST_URI'], '/wp-admin/edit.php') !== false) {
        global $current_user;
        $wp_query->set('author', $current_user->id);

    }
}

add_filter('parse_query', 'mypo_parse_query_useronly');*/


function insert_last_login($user_login, $user = null)
{
    $user_id = get_user_id($user_login);
    update_user_meta($user_id, 'last_login', current_time('mysql'));
}

add_action('wp_login', 'insert_last_login', 10, 2);


/*
 * 用户的生词本是否在复习计划中
 *
 * */

function isInReview($user_id, $post_id)
{

    $review_mes = get_user_meta($user_id, "review_mes", true);
    if ($review_mes) {
        $review_mes_arr = json_decode($review_mes);//解析用户的json信息
        for ($i = 0; $i < count($review_mes_arr); $i++) {
            if ($review_mes_arr[$i]->post_id == $post_id) {
                return true;
                break;
            }
        }
    }
    return false;
}


function findNum($str = '')
{
    $str = trim($str);
    if (empty($str)) {
        return '';
    }
    $temp = array('1', '2', '3', '4', '5', '6', '7', '8', '9', '0');
    $result = '';
    for ($i = 0; $i < strlen($str); $i++) {
        if (in_array($str[$i], $temp)) {
            $result .= $str[$i];
        }
    }
    return $result;
}


function vtime($time)
{
    $output = '';
    foreach (array(86400 => '天', 3600 => '小时', 60 => '分', 1 => '秒') as $key => $value) {
        if ($time >= $key) $output .= floor($time / $key) . $value;
        $time %= $key;
    }
    return $output;
}

function get_post_category_id($post_ID)
{

    global $wpdb;
    $sql = "SELECT term_id FROM " . $wpdb->term_relationships . " LEFT JOIN " . $wpdb->term_taxonomy;
    $sql .= " ON " . $wpdb->term_relationships . ".term_taxonomy_id=" . $wpdb->term_taxonomy . ".term_taxonomy_id";
    $sql .= " WHERE object_id=" . $post_ID . " AND taxonomy='category'";
    $cat_id = $wpdb->get_results($sql);
    $myCatId = (int)$cat_id[0]->term_id;//这里就获得当前文章所属分类的分类ID
    $term = get_category($myCatId);//taxonomy_name为自己定义的或者默认的
    return $term->name;
}


/*
 * 订单支付
 *
 * */


function  create_order($order_no, $order_user_id, $order_prepay_id, $order_body, $order_price, $order_status = 0)
{
    global $wpdb;


    $new_order = array(
        'order_no' => $order_no,
        'order_user_id' => $order_user_id,
        'order_status' => $order_status,
        'order_prepay_id' => $order_prepay_id,
        'order_body' => $order_body,
        'order_price' => $order_price

    );
    return $wpdb->insert($wpdb->prefix . 'order', $new_order);

}

function update_order_status($order_no)
{
    global $wpdb;
    $time = current_time("timestamp");
    return $wpdb->update($wpdb->prefix . "order", array('order_status' => 1, 'order_time_stp' => $time), array('order_no' => $order_no));

}


function search_order_by_user_id($order_user_id)
{
    global $wpdb;

    $selectStr = "SELECT * FROM  `" . $wpdb->prefix . "order` WHERE order_user_id =" . $order_user_id . " ORDER BY  `" . $wpdb->prefix . "order`.`order_time_stp` DESC ";
    $ress = $wpdb->get_results($selectStr);
    return $ress;

}

function search_order_by_order_no($order_no)
{
    global $wpdb;

    $selectStr = "SELECT * FROM  `" . $wpdb->prefix . "order` WHERE order_no =  '" . $order_no . "'";
    $ress = $wpdb->get_results($selectStr);
    return $ress;

}

/*
 * 增加用户VIP时间(按天)
 *
 * */


function add_user_viptime($user_id, $days)
{
    $user_viptime = get_user_meta($user_id, "user_viptime", true);
    if ($user_viptime) {
        if ($user_viptime > current_time("timestamp")) {
            $totaltime = $user_viptime + $days * 24 * 3600;
        } else {
            $totaltime = current_time("timestamp") + $days * 24 * 3600;
        }
    } else {
        $totaltime = current_time("timestamp") + $days * 24 * 3600;
    }

    return update_user_meta($user_id, "user_viptime", $totaltime);
}

/*
 *
 * 增加用户VIP时间(1小时)
 * */


function add_user_viptime_hour($user_id)
{
    $user_viptime = get_user_meta($user_id, "user_viptime", true);
    if ($user_viptime) {
        if ($user_viptime > current_time("timestamp")) {
            $totaltime = $user_viptime + 3600;
        } else {
            $totaltime = current_time("timestamp") + 3600;
        }
    } else {
        $totaltime = current_time("timestamp") + 3600;
    }

    return update_user_meta($user_id, "user_viptime", $totaltime);
}

/*
 *
 * 增加用户VIP时间(45分钟)
 * */


function add_user_viptime_one_lesson($user_id)
{//45分钟
    $user_viptime = get_user_meta($user_id, "user_viptime", true);
    if ($user_viptime) {
        if ($user_viptime > current_time("timestamp")) {
            $totaltime = $user_viptime + 2400;
        } else {
            $totaltime = current_time("timestamp") + 2400;
        }
    } else {
        $totaltime = current_time("timestamp") + 2400;
    }

    return update_user_meta($user_id, "user_viptime", $totaltime);
}

/*
 *
 * 增加用户VIP时间，按分钟
 * */

function add_user_viptime_on_minutes($user_id, $minute)
{
    $user_viptime = get_user_meta($user_id, "user_viptime", true);
    $add_time = $minute * 60;
    if ($user_viptime) {
        if ($user_viptime > current_time("timestamp")) {
            $totaltime = $user_viptime + $add_time;
        } else {
            $totaltime = current_time("timestamp") + $add_time;
        }
    } else {
        $totaltime = current_time("timestamp") + $add_time;
    }

    return update_user_meta($user_id, "user_viptime", $totaltime);
}

/*
 *
 * 减少用户VIP时间
 * */


function reduce_user_viptime($user_id, $days)
{
    $user_viptime = get_user_meta($user_id, "user_viptime", true);
    if ($user_viptime) {
        $totaltime = $user_viptime - $days * 24 * 3600;
        return update_user_meta($user_id, "user_viptime", $totaltime);

    }
    return 0;
}

/*
 *
 * 开通个别课程权限
 *
 * 参数 user_id 用户id
 * 时长 days
 * $cat_arr 课程数组
 * */


function add_user_single_course_viptime($user_id, $days, $cat_arr)
{
    $my_course_limit_json = get_user_meta($user_id, "my_course_limit", true);//获取用户课程权限的json
    $add_time = $days * 24 * 3600;//增加的时长
    if ($my_course_limit_json) {//如果json有值
        //解析json
        $my_course_limit_arr = json_decode($my_course_limit_json);


        foreach ($my_course_limit_arr as $my_course_limit) {
            //遍历目前的课程权限
            foreach ($cat_arr as $key => $cat) {
                //遍历新增课程ID
                if ($my_course_limit->cat_id == $cat) {
                    //原有课程ID与新增课程ID相同,则增加时间
                    if ($my_course_limit->viptime > current_time("timestamp")) {
                        $my_course_limit->viptime += $add_time;
                    } else {
                        $my_course_limit->viptime = current_time("timestamp") + $add_time;
                    }
                    //在新增课程数组中，去掉该课程ID
                    array_splice($cat_arr, $key, 1);

                }
            }
        }

        if (count($cat_arr) > 0) {
            //若是新课程数组中还有值,则需要新增写入课程权限

            foreach ($cat_arr as $cat) {
                $total_time = current_time("timestamp") + $add_time;

                $arr = array("cat_id" => $cat, "viptime" => $total_time);
                array_push($my_course_limit_arr, $arr);//总数组

            }

        };


        $new_my_course_limit_json = json_encode($my_course_limit_arr);//课程权限json

        if (update_user_meta($user_id, "my_course_limit", $new_my_course_limit_json)) {
            return true;
        }


    } else {
        //如果json没有值
        $new_my_course_limit_arr = array();//新建一个课程权限数组

        foreach ($cat_arr as $cat) {

            $total_time = current_time("timestamp") + $add_time;

            $arr = array("cat_id" => $cat, "viptime" => $total_time);
            array_push($new_my_course_limit_arr, $arr);//总数组

        }

        $new_my_course_limit_json = json_encode($new_my_course_limit_arr);//课程权限json

        if (update_user_meta($user_id, "my_course_limit", $new_my_course_limit_json)) {
            return true;
        }

    }

}

/*
 *
 * 删除个别课程权限
 *
 * 参数 user_id 用户id
 *
 * $cat_arr 课程数组
 * */


function delete_user_single_course_viptime($user_id, $cat_arr)
{


}


/**
 * @功能概述: 动态替换课程JSON数据中的硬编码域名
 *           处理复杂编码的JSON数据，先解码再替换域名，最后重新编码
 *
 * @param {string} $json_string - 包含可能硬编码域名的JSON字符串（可能多层编码）
 *
 * @return {string} 替换域名后的JSON字符串
 *
 * @执行流程:
 *   1. 尝试解码JSON数据（处理多层URL编码）
 *   2. 在解码后的数组中递归替换域名
 *   3. 重新编码为JSON字符串返回
 *   4. 如果解码失败，回退到字符串替换方式
 *
 * @使用场景: 课程数据迁移时动态更新图片和资源URL
 */
function replace_course_domain_urls($json_string)
{
    error_log("[DomainReplace] [开始] 开始处理课程域名替换");

    // 获取当前WordPress站点域名
    $current_domain = home_url();
    error_log("[DomainReplace] [配置] 当前域名: {$current_domain}");

    // 定义需要替换的旧域名列表
    $old_domains = [
        'https://www.shuimitao.online',
        'https://shuimitao.online',
        'http://www.shuimitao.online',
        'http://shuimitao.online',
        'https://joco15.com',
        'http://joco15.com',
        'https://www.joco15.com',
        'http://www.joco15.com'
    ];

    // 如果输入为空，直接返回
    if (empty($json_string) || $json_string === '[]') {
        error_log("[DomainReplace] [跳过] 输入数据为空");
        return $json_string;
    }

    // 添加详细的输入数据日志
    error_log("[DomainReplace] [输入数据] JSON字符串长度: " . strlen($json_string));
    error_log("[DomainReplace] [输入数据] 前200字符: " . substr($json_string, 0, 200));

    // 检查原始字符串中是否包含旧域名（包括URL编码的域名）
    $found_domains = [];
    foreach ($old_domains as $domain) {
        // 检查原始域名
        if (strpos($json_string, $domain) !== false) {
            $found_domains[] = $domain;
        }
        // 检查URL编码的域名
        $encoded_domain = urlencode($domain);
        if (strpos($json_string, $encoded_domain) !== false) {
            $found_domains[] = $domain . ' (URL编码)';
        }
    }
    if (!empty($found_domains)) {
        error_log("[DomainReplace] [发现] 原始字符串中发现域名: " . implode(', ', $found_domains));
    } else {
        error_log("[DomainReplace] [检查] 原始字符串中未发现任何旧域名（包括URL编码）");
    }

    // 尝试解码JSON数据（处理复杂编码）
    $decoded_data = decode_complex_json($json_string);

    if ($decoded_data !== null && is_array($decoded_data)) {
        error_log("[DomainReplace] [解码] JSON解码成功，开始递归替换域名");

        // 在解码后的数组中递归替换域名
        $replacement_count = 0;
        replace_domains_in_array($decoded_data, $old_domains, $current_domain, $replacement_count);

        // 重新编码为JSON字符串
        $result = json_encode($decoded_data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

        error_log("[DomainReplace] [完成] 数组模式替换 {$replacement_count} 个域名");
        return $result;

    } else {
        error_log("[DomainReplace] [回退] JSON解码失败，使用字符串替换模式");

        // 回退到字符串替换方式
        $replacement_count = 0;
        foreach($old_domains as $old_domain) {
            $before_count = substr_count($json_string, $old_domain);
            if($before_count > 0) {
                $json_string = str_replace($old_domain, $current_domain, $json_string);
                $replacement_count += $before_count;
                error_log("[DomainReplace] [替换] {$old_domain} -> {$current_domain} (替换{$before_count}次)");
            }
        }

        error_log("[DomainReplace] [完成] 字符串模式替换 {$replacement_count} 个域名");
        return $json_string;
    }
}

/**
 * 临时调试函数：检查WordPress选项中的域名设置
 * 用于排查硬编码域名问题
 */
function debug_wordpress_domain_options() {
    $options_to_check = ['siteurl', 'home', 'upload_url_path', 'upload_path'];

    error_log("[DomainDebug] ========== WordPress域名选项检查 ==========");
    foreach ($options_to_check as $option) {
        $value = get_option($option);
        error_log("[DomainDebug] {$option}: " . ($value ? $value : '(空值)'));
    }
    error_log("[DomainDebug] ========================================");
}

/**
 * ========================================================================
 * 自定义分类模板功能 (Custom Category Templates)
 * ========================================================================
 *
 * 功能说明：
 * - 原为独立插件 "Custom Category Templates" v0.2.1
 * - 作者：Hassan Derakhshandeh
 * - 整合日期：2025-07-03
 * - 整合原因：避免安装额外插件，直接集成到主题中
 *
 * 核心功能：
 * 1. 在WordPress后台分类编辑页面添加模板选择下拉框
 * 2. 保存分类与模板文件的关联关系到数据库
 * 3. 前端根据分类ID自动加载对应的自定义模板
 * 4. 为body标签添加模板相关的CSS类名
 *
 * 使用方法：
 * 1. 在主题目录创建模板文件，文件头部添加：[* Category Template: 模板名称 *]
 * 2. 在WordPress后台 > 文章 > 分类目录 中编辑分类
 * 3. 选择对应的模板文件
 * 4. 前端访问该分类页面时会自动使用选定的模板
 *
 * 数据存储：
 * - 使用wp_options表的'category_templates'选项
 * - 格式：array(分类ID => 模板文件名)
 * ========================================================================
 */

/**
 * 自定义分类模板管理类
 *
 * 负责处理分类模板的选择、保存和前端应用
 *
 * @since 2025-07-03 (整合自插件)
 */
class Custom_Category_Templates {

    /**
     * 当前使用的模板文件名
     * 用于body_class过滤器
     *
     * @var string
     */
    var $template;

    /**
     * 构造函数 - 注册WordPress钩子
     *
     * 根据是否为后台环境注册不同的钩子：
     * - 后台：分类表单字段、保存/删除操作
     * - 前台：模板过滤器
     *
     * @since 2025-07-03
     */
    function __construct() {
        $log_prefix = '[CustomCategoryTemplates]';

        if( is_admin() ) {
            // 后台环境 - 注册分类管理相关钩子
            error_log($log_prefix . ' [初始化] 后台环境，注册分类管理钩子');

            add_action( 'category_add_form_fields', array( $this, 'add_template_option') );
            add_action( 'category_edit_form_fields', array( $this, 'edit_template_option') );
            add_action( 'created_category', array( $this, 'save_option' ), 10, 2 );
            add_action( 'edited_category', array( $this, 'save_option' ), 10, 2 );
            add_action( 'delete_category', array( $this, 'delete_option' ) );

            error_log($log_prefix . ' [钩子注册] 分类表单字段和保存操作钩子已注册');
        } else {
            // 前台环境 - 注册模板过滤器
            error_log($log_prefix . ' [初始化] 前台环境，注册模板过滤器');
            add_filter( 'category_template', array( $this, 'category_template' ) );
        }
    }

    /**
     * 分类模板过滤器 - 前台核心功能
     *
     * 在前台分类页面加载时，检查该分类是否设置了自定义模板
     * 如果有，则使用自定义模板替换默认模板
     *
     * @param string $template 默认的分类模板路径
     * @return string 最终使用的模板路径
     * @since 2025-07-03
     */
    function category_template( $template ) {
        $log_prefix = '[CustomCategoryTemplates]';

        // 获取所有分类模板配置
        $category_templates = get_option( 'category_templates', array() );

        // 获取当前查询的分类对象
        $category = get_queried_object();

        if (!$category || !isset($category->term_id)) {
            error_log($log_prefix . ' [模板过滤] 无法获取当前分类对象，使用默认模板');
            return $template;
        }

        $id = $category->term_id;
        error_log($log_prefix . ' [模板过滤] 当前分类ID: ' . $id);

        // 检查该分类是否设置了自定义模板
        if( isset( $category_templates[$id] ) ) {
            $custom_template = $category_templates[$id];
            error_log($log_prefix . ' [模板检查] 分类ID ' . $id . ' 设置的模板: ' . $custom_template);

            // 尝试定位模板文件
            $tmpl = locate_template( $custom_template );

            if( 'default' !== $custom_template && '' !== $tmpl ) {
                // 找到自定义模板文件，准备使用
                $this->template = $custom_template;

                // 添加body_class过滤器，为页面添加模板相关的CSS类
                add_filter( 'body_class', array( $this, 'body_class' ) );

                error_log($log_prefix . ' [模板应用] 使用自定义模板: ' . $tmpl);
                return $tmpl;
            } else {
                error_log($log_prefix . ' [模板警告] 自定义模板文件不存在或为默认值: ' . $custom_template);
            }
        } else {
            error_log($log_prefix . ' [模板检查] 分类ID ' . $id . ' 未设置自定义模板，使用默认模板');
        }

        return $template;
    }

    /**
     * 为body标签添加模板相关的CSS类
     *
     * 当使用自定义模板时，为body添加特定的CSS类名
     * 格式：category-template-{模板文件名}
     *
     * @param array $classes 现有的body CSS类数组
     * @return array 添加了模板类的CSS类数组
     * @since 2025-07-03
     */
    function body_class( $classes ) {
        $log_prefix = '[CustomCategoryTemplates]';

        if (!empty($this->template)) {
            // 清理模板文件名，生成安全的CSS类名
            $template = sanitize_html_class( str_replace( '.', '-', $this->template ) );
            $css_class = 'category-template-' . $template;
            $classes[] = $css_class;

            error_log($log_prefix . ' [CSS类] 为body添加模板CSS类: ' . $css_class);
        }

        return $classes;
    }

    /**
     * 保存分类模板选择
     *
     * 当创建或编辑分类时，保存用户选择的模板到数据库
     *
     * @param int $term_id 分类ID
     * @since 2025-07-03
     */
    function save_option( $term_id ) {
        $log_prefix = '[CustomCategoryTemplates]';

        // 检查是否提交了模板选择
        if( isset( $_POST['custom-category-template'] ) ) {
            $template = trim( $_POST['custom-category-template'] );

            error_log($log_prefix . ' [保存] 分类ID ' . $term_id . ' 选择的模板: ' . $template);

            // 获取现有的分类模板配置
            $category_templates = get_option( 'category_templates', array() );

            if( 'default' == $template ) {
                // 选择了默认模板，从配置中移除该分类
                if (isset($category_templates[$term_id])) {
                    unset( $category_templates[$term_id] );
                    error_log($log_prefix . ' [保存] 分类ID ' . $term_id . ' 重置为默认模板');
                }
            } else {
                // 选择了自定义模板，保存到配置中
                $category_templates[$term_id] = $template;
                error_log($log_prefix . ' [保存] 分类ID ' . $term_id . ' 设置自定义模板: ' . $template);
            }

            // 更新数据库中的配置
            $update_result = update_option( 'category_templates', $category_templates );

            if ($update_result) {
                error_log($log_prefix . ' [保存成功] 分类模板配置已更新到数据库');
            } else {
                error_log($log_prefix . ' [保存警告] 分类模板配置更新可能失败或无变化');
            }
        } else {
            error_log($log_prefix . ' [保存跳过] 未检测到模板选择数据');
        }
    }

    /**
     * 删除分类时清理模板配置
     *
     * 当删除分类时，同时从数据库中移除该分类的模板配置
     *
     * @param int $term_id 被删除的分类ID
     * @since 2025-07-03
     */
    function delete_option( $term_id ) {
        $log_prefix = '[CustomCategoryTemplates]';

        // 获取现有的分类模板配置
        $category_templates = get_option( 'category_templates', array() );

        // 检查该分类是否有模板配置
        if( isset( $category_templates[$term_id] ) ) {
            $old_template = $category_templates[$term_id];
            unset( $category_templates[$term_id] );

            // 更新数据库
            update_option( 'category_templates', $category_templates );

            error_log($log_prefix . ' [删除] 分类ID ' . $term_id . ' 的模板配置已清理 (原模板: ' . $old_template . ')');
        } else {
            error_log($log_prefix . ' [删除] 分类ID ' . $term_id . ' 无模板配置需要清理');
        }
    }

    /**
     * 在添加分类页面显示模板选择字段
     *
     * 在WordPress后台"添加新分类"表单中添加模板选择下拉框
     *
     * @since 2025-07-03
     */
    function add_template_option() {
        $log_prefix = '[CustomCategoryTemplates]';

        // 获取可用的分类模板
        $category_templates = $this->get_category_templates();

        if( empty( $category_templates ) ) {
            error_log($log_prefix . ' [表单字段] 未找到可用的分类模板，跳过显示模板选择字段');
            return;
        }

        error_log($log_prefix . ' [表单字段] 在添加分类页面显示模板选择字段，可用模板数: ' . count($category_templates));

        ?>
        <div class="form-field">
            <label for="custom-category-template"><?php echo __( 'Template', 'html5game-theme' ); ?></label>
            <select name="custom-category-template" id="custom-category-template" class="postform">
                <option value="default"><?php echo __( 'Default Template', 'html5game-theme' ); ?></option>
                <?php $this->category_templates_dropdown() ?>
            </select>
            <p class="description"><?php echo __( 'Choose a custom template for this category.', 'html5game-theme' ); ?></p>
        </div>
        <?php
    }

    /**
     * 在编辑分类页面显示模板选择字段
     *
     * 在WordPress后台"编辑分类"表单中添加模板选择下拉框
     * 并显示当前分类已选择的模板
     *
     * @since 2025-07-03
     */
    function edit_template_option() {
        $log_prefix = '[CustomCategoryTemplates]';

        // 获取可用的分类模板
        $category_templates = $this->get_category_templates();

        if( empty( $category_templates ) ) {
            error_log($log_prefix . ' [表单字段] 未找到可用的分类模板，跳过显示模板选择字段');
            return;
        }

        // 获取当前分类ID和已选择的模板
        $id = $_REQUEST['tag_ID'];
        $templates = get_option( 'category_templates', array() );
        $template = isset( $templates[$id] ) ? $templates[$id] : null;

        error_log($log_prefix . ' [表单字段] 在编辑分类页面显示模板选择字段，分类ID: ' . $id . '，当前模板: ' . ($template ? $template : '默认'));

        ?>
        <tr class="form-field">
            <th scope="row" valign="top">
                <label for="custom-category-template"><?php echo __( 'Template', 'html5game-theme' ); ?></label>
            </th>
            <td>
                <select name="custom-category-template" id="custom-category-template" class="postform">
                    <option value='default'><?php echo __( 'Default Template', 'html5game-theme' ); ?></option>
                    <?php $this->category_templates_dropdown( $template ) ?>
                </select>
                <p class="description"><?php echo __( 'Choose a custom template for this category.', 'html5game-theme' ); ?></p>
            </td>
        </tr>
        <?php
    }

    /**
     * 生成分类模板下拉框选项
     *
     * 为模板选择下拉框生成option标签
     *
     * @param string $default 当前选中的模板文件名
     * @since 2025-07-03
     */
    function category_templates_dropdown( $default = null ) {
        $log_prefix = '[CustomCategoryTemplates]';

        // 获取可用模板并翻转数组（文件名作为键，模板名作为值）
        $templates = array_flip( $this->get_category_templates() );
        ksort( $templates ); // 按模板名称排序

        error_log($log_prefix . ' [下拉框] 生成模板选项，默认选中: ' . ($default ? $default : '无'));

        foreach( array_keys( $templates ) as $template ) {
            // 检查是否为当前选中的模板
            $selected = ( $default == $templates[$template] ) ? " selected='selected'" : '';

            // 输出option标签
            echo "\n\t<option value='".$templates[$template]."' $selected>$template</option>";
        }
    }

    /**
     * 扫描主题中的分类模板文件
     *
     * 扫描当前主题（包括父主题）中所有包含"Category Template:"头部注释的PHP文件
     *
     * @param string $template 指定主题模板目录（可选）
     * @return array 模板文件数组，键为文件名，值为模板显示名称
     * @since 2025-07-03
     */
    function get_category_templates( $template = null ) {
        $log_prefix = '[CustomCategoryTemplates]';
        $category_templates = array();

        // 获取主题对象
        $theme = wp_get_theme( $template );

        if (!$theme->exists()) {
            error_log($log_prefix . ' [模板扫描] 主题不存在: ' . ($template ? $template : '当前主题'));
            return $category_templates;
        }

        // 获取主题中的所有PHP文件
        $files = (array) $theme->get_files( 'php', 1 );

        error_log($log_prefix . ' [模板扫描] 开始扫描主题: ' . $theme->get('Name') . '，PHP文件数: ' . count($files));

        foreach ( $files as $file => $full_path ) {
            // 检查文件是否包含分类模板头部注释
            $file_content = file_get_contents( $full_path );

            if ( ! preg_match( '|Category Template:(.*)$|mi', $file_content, $header ) ) {
                continue; // 不是分类模板文件，跳过
            }

            // 提取模板名称并清理
            $template_name = _cleanup_header_comment( $header[1] );
            $category_templates[ $file ] = $template_name;

            error_log($log_prefix . ' [模板发现] 文件: ' . $file . ' -> 模板名: ' . $template_name);
        }

        // 如果是子主题，也扫描父主题
        if ( $theme->parent() ) {
            error_log($log_prefix . ' [模板扫描] 检测到子主题，继续扫描父主题');
            $parent_templates = $this->get_category_templates( $theme->get_template() );
            $category_templates = $category_templates + $parent_templates; // 子主题优先
        }

        error_log($log_prefix . ' [模板扫描] 扫描完成，找到分类模板数: ' . count($category_templates));

        return $category_templates;
    }
}

/**
 * ========================================================================
 * 自定义分类模板功能初始化
 * ========================================================================
 */

// 实例化自定义分类模板类
$custom_category_templates = new Custom_Category_Templates();

// 记录功能启用日志
error_log('[ThemeFunctions] [功能模块] 自定义分类模板功能已启用 (整合自Custom Category Templates插件)');

/**
 * 使用说明：
 *
 * 1. 创建分类模板文件：
 *    在主题根目录创建PHP文件，文件开头添加：
 *    <?php
 *    [*
 *    Category Template: 我的自定义分类模板
 *    *]
 *
 * 2. 设置分类模板：
 *    WordPress后台 > 文章 > 分类目录 > 编辑分类
 *    在"Template"下拉框中选择对应的模板
 *
 * 3. 前端效果：
 *    访问该分类页面时会自动使用选定的模板
 *    body标签会添加CSS类：category-template-{模板文件名}
 *
 * 4. 数据存储：
 *    配置保存在wp_options表的'category_templates'选项中
 *    格式：array(分类ID => 模板文件名)
 */

/**
 * ========================================================================
 * 自定义文章模板功能 (Custom Post Templates)
 * ========================================================================
 *
 * 功能说明：
 * - 原为独立插件 "Custom Post Templates" v1.5
 * - 作者：Simon Wheatley
 * - 整合日期：2025-07-03
 * - 整合原因：避免安装额外插件，直接集成到主题中
 *
 * 核心功能：
 * 1. 在WordPress后台文章编辑页面添加模板选择下拉框
 * 2. 保存文章与模板文件的关联关系到数据库
 * 3. 前端根据文章ID自动加载对应的自定义模板
 * 4. 为body标签添加模板相关的CSS类名
 * 5. 提供模板检测函数 is_post_template()
 *
 * 使用方法：
 * 1. 在主题目录创建模板文件，文件头部添加：[* Template Name Posts: 模板名称 *]
 * 2. 在WordPress后台 > 文章 > 编辑文章 中选择对应的模板文件
 * 3. 前端访问该文章页面时会自动使用选定的模板
 *
 * 数据存储：
 * - 使用wp_postmeta表的'custom_post_template'字段
 * - 格式：文章ID => 模板文件名
 * ========================================================================
 */

/**
 * 自定义文章模板管理类
 *
 * 负责处理文章模板的选择、保存和前端应用
 *
 * @since 2025-07-03 (整合自插件)
 */
class Custom_Post_Templates {

    /**
     * 模板元数据键名
     * 用于在wp_postmeta表中存储模板信息
     *
     * @var string
     */
    private $tpl_meta_key;

    /**
     * 当前处理的文章ID
     * 用于模板操作
     *
     * @var int
     */
    private $post_ID;

    /**
     * 构造函数 - 注册WordPress钩子
     *
     * 注册文章编辑、保存和前端模板过滤相关的钩子
     *
     * @since 2025-07-03
     */
    function __construct() {
        $log_prefix = '[CustomPostTemplates]';

        // 初始化属性
        $this->tpl_meta_key = 'custom_post_template';

        error_log($log_prefix . ' [初始化] 开始注册自定义文章模板钩子');

        // 注册WordPress钩子
        add_action('admin_init', array($this, 'admin_init'));
        add_action('save_post', array($this, 'save_post'));
        add_filter('single_template', array($this, 'filter_single_template'));
        add_filter('body_class', array($this, 'body_class'));

        error_log($log_prefix . ' [钩子注册] 文章模板相关钩子已注册完成');
    }

    /**
     * 后台初始化 - 添加元数据框
     *
     * 在文章编辑页面添加模板选择元数据框
     *
     * @since 2025-07-03
     */
    public function admin_init() {
        $log_prefix = '[CustomPostTemplates]';

        // 获取支持的文章类型（可通过过滤器扩展）
        $post_types = apply_filters('cpt_post_types', array('post'));

        error_log($log_prefix . ' [后台初始化] 支持的文章类型: ' . implode(', ', $post_types));

        foreach ($post_types as $post_type) {
            add_meta_box(
                'select_post_template',
                __('Post Template', 'custom-post-templates'),
                array($this, 'select_post_template'),
                $post_type,
                'side',
                'default'
            );

            error_log($log_prefix . ' [元数据框] 已为文章类型 "' . $post_type . '" 添加模板选择框');
        }
    }

    /**
     * Body类过滤器 - 为使用自定义模板的文章添加CSS类
     *
     * 在前端为使用自定义模板的文章页面添加特定的CSS类名
     *
     * @param array $classes 现有的CSS类数组
     * @return array 修改后的CSS类数组
     * @since 2025-07-03
     */
    public function body_class($classes) {
        $log_prefix = '[CustomPostTemplates]';

        if (!$this->is_post_template()) {
            error_log($log_prefix . ' [Body类] 当前页面未使用自定义文章模板');
            return $classes;
        }

        global $wp_query;
        $post = $wp_query->get_queried_object();
        $post_template = get_post_meta($post->ID, 'custom_post_template', true);

        $classes[] = 'post-template';
        $classes[] = 'post-template-' . str_replace('.php', '-php', $post_template);

        error_log($log_prefix . ' [Body类] 为文章ID ' . $post->ID . ' 添加模板类: ' . $post_template);

        return $classes;
    }

    /**
     * 模板选择元数据框渲染
     *
     * 在文章编辑页面显示模板选择下拉框
     *
     * @param object $post 当前文章对象
     * @since 2025-07-03
     */
    public function select_post_template($post) {
        $log_prefix = '[CustomPostTemplates]';

        error_log($log_prefix . ' [元数据框渲染] 开始渲染文章ID ' . $post->ID . ' 的模板选择框');

        // 获取当前文章的模板设置
        $post_template = get_post_meta($post->ID, $this->tpl_meta_key, true);

        // 获取可用的文章模板
        $templates = $this->get_post_templates();

        error_log($log_prefix . ' [元数据框渲染] 找到 ' . count($templates) . ' 个可用模板');

        // 输出模板选择HTML
        echo '<p>';
        echo '<label for="' . $this->tpl_meta_key . '">' . __('Template', 'custom-post-templates') . ':</label><br />';
        echo '<select name="' . $this->tpl_meta_key . '" id="' . $this->tpl_meta_key . '">';
        echo '<option value="">' . __('Default Template', 'custom-post-templates') . '</option>';

        foreach ($templates as $template_file => $template_name) {
            $selected = ($post_template == $template_file) ? ' selected="selected"' : '';
            echo '<option value="' . $template_file . '"' . $selected . '>' . $template_name . '</option>';

            if ($selected) {
                error_log($log_prefix . ' [元数据框渲染] 当前选中模板: ' . $template_name . ' (' . $template_file . ')');
            }
        }

        echo '</select>';
        echo '</p>';

        // 添加nonce字段用于安全验证
        wp_nonce_field('save_post_template', 'post_template_nonce');

        error_log($log_prefix . ' [元数据框渲染] 模板选择框渲染完成');
    }

    /**
     * 保存文章模板设置
     *
     * 当文章保存时，同时保存模板选择
     *
     * @param int $post_id 文章ID
     * @since 2025-07-03
     */
    public function save_post($post_id) {
        $log_prefix = '[CustomPostTemplates]';

        // 安全检查：验证nonce
        if (!isset($_POST['post_template_nonce']) || !wp_verify_nonce($_POST['post_template_nonce'], 'save_post_template')) {
            error_log($log_prefix . ' [保存] Nonce验证失败，跳过保存');
            return;
        }

        // 检查用户权限
        if (!current_user_can('edit_post', $post_id)) {
            error_log($log_prefix . ' [保存] 用户无权限编辑文章ID ' . $post_id);
            return;
        }

        // 检查是否为自动保存
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            error_log($log_prefix . ' [保存] 自动保存，跳过模板保存');
            return;
        }

        error_log($log_prefix . ' [保存] 开始保存文章ID ' . $post_id . ' 的模板设置');

        // 获取提交的模板值
        $template = isset($_POST[$this->tpl_meta_key]) ? $_POST[$this->tpl_meta_key] : '';

        if ($template) {
            // 保存模板设置
            update_post_meta($post_id, $this->tpl_meta_key, $template);
            error_log($log_prefix . ' [保存] 模板已保存: ' . $template);
        } else {
            // 删除模板设置（使用默认模板）
            delete_post_meta($post_id, $this->tpl_meta_key);
            error_log($log_prefix . ' [保存] 已清除模板设置，使用默认模板');
        }
    }

    /**
     * 单文章模板过滤器
     *
     * 在前端根据文章的模板设置加载相应的模板文件
     *
     * @param string $template 默认模板路径
     * @return string 最终使用的模板路径
     * @since 2025-07-03
     */
    public function filter_single_template($template) {
        $log_prefix = '[CustomPostTemplates]';

        global $wp_query;
        $post = $wp_query->get_queried_object();

        if (!$post) {
            error_log($log_prefix . ' [模板过滤] 未找到当前文章对象');
            return $template;
        }

        // 获取文章的自定义模板设置
        $post_template = get_post_meta($post->ID, $this->tpl_meta_key, true);

        if (!$post_template) {
            error_log($log_prefix . ' [模板过滤] 文章ID ' . $post->ID . ' 未设置自定义模板');
            return $template;
        }

        // 构建模板文件路径
        $template_path = get_template_directory() . '/' . $post_template;

        if (file_exists($template_path)) {
            error_log($log_prefix . ' [模板过滤] 文章ID ' . $post->ID . ' 使用自定义模板: ' . $post_template);
            return $template_path;
        } else {
            error_log($log_prefix . ' [模板过滤] 模板文件不存在: ' . $template_path . '，使用默认模板');
            return $template;
        }
    }

    /**
     * 获取可用的文章模板列表
     *
     * 扫描主题目录，查找包含特定头部注释的模板文件
     *
     * @return array 模板文件数组，格式：array(文件名 => 模板名称)
     * @since 2025-07-03
     */
    public function get_post_templates() {
        $log_prefix = '[CustomPostTemplates]';

        $templates = array();
        $theme_dir = get_template_directory();

        error_log($log_prefix . ' [模板扫描] 开始扫描主题目录: ' . $theme_dir);

        // 获取主题目录下的所有PHP文件
        $files = glob($theme_dir . '/*.php');

        foreach ($files as $file) {
            $file_name = basename($file);

            // 读取文件头部内容（前1000字符）
            $file_content = file_get_contents($file, false, null, 0, 1000);

            // 查找模板名称注释
            if (preg_match('/\[\*\s*Template Name Posts:\s*(.+?)\s*\*\]/i', $file_content, $matches)) {
                $template_name = trim($matches[1]);
                $templates[$file_name] = $template_name;

                error_log($log_prefix . ' [模板扫描] 找到文章模板: ' . $file_name . ' => ' . $template_name);
            }
        }

        error_log($log_prefix . ' [模板扫描] 扫描完成，共找到 ' . count($templates) . ' 个文章模板');

        return $templates;
    }

    /**
     * 检测当前页面是否使用自定义文章模板
     *
     * @return bool 使用自定义模板返回true，否则返回false
     * @since 2025-07-03
     */
    public function is_post_template() {
        if (!is_single()) {
            return false;
        }

        global $wp_query;
        $post = $wp_query->get_queried_object();

        if (!$post) {
            return false;
        }

        $post_template = get_post_meta($post->ID, $this->tpl_meta_key, true);

        return !empty($post_template);
    }
}

/**
 * 模板检测函数 - 供主题开发者使用
 *
 * 检测当前文章是否使用指定的自定义模板
 *
 * @param string $template_name 可选，模板文件名。如果不提供，则检测是否使用任何自定义模板
 * @return bool 使用指定模板返回true，否则返回false
 * @since 2025-07-03
 */
function is_post_template($template_name = '') {
    $log_prefix = '[CustomPostTemplates]';

    if (!is_single()) {
        error_log($log_prefix . ' [模板检测] 当前页面不是文章页');
        return false;
    }

    global $wp_query;
    $post = $wp_query->get_queried_object();

    if (!$post) {
        error_log($log_prefix . ' [模板检测] 未找到当前文章对象');
        return false;
    }

    $post_template = get_post_meta($post->ID, 'custom_post_template', true);

    if (empty($post_template)) {
        error_log($log_prefix . ' [模板检测] 文章ID ' . $post->ID . ' 未使用自定义模板');
        return false;
    }

    if (empty($template_name)) {
        // 检测是否使用任何自定义模板
        error_log($log_prefix . ' [模板检测] 文章ID ' . $post->ID . ' 使用自定义模板: ' . $post_template);
        return true;
    }

    // 检测是否使用指定的模板
    $is_match = ($post_template === $template_name);

    if ($is_match) {
        error_log($log_prefix . ' [模板检测] 文章ID ' . $post->ID . ' 使用指定模板: ' . $template_name);
    } else {
        error_log($log_prefix . ' [模板检测] 文章ID ' . $post->ID . ' 未使用指定模板: ' . $template_name . '，当前使用: ' . $post_template);
    }

    return $is_match;
}

// 实例化自定义文章模板类
$custom_post_templates = new Custom_Post_Templates();

// 记录功能启用日志
error_log('[ThemeFunctions] [功能模块] 自定义文章模板功能已启用 (整合自Custom Post Templates插件)');

/**
 * 使用说明：
 *
 * 1. 创建文章模板文件：
 *    在主题根目录创建PHP文件，文件开头添加：
 *    <?php
 *    [*
 *    Template Name Posts: 我的自定义文章模板示例
 *    *]
 *
 * 2. 设置文章模板：
 *    在WordPress后台 > 文章 > 编辑文章 中选择对应的模板文件
 *    在右侧边栏的"Post Template"下拉框中选择模板
 *
 * 3. 前端效果：
 *    访问该文章页面时会自动使用选定的模板
 *    body标签会添加CSS类：post-template post-template-{模板文件名}
 *
 * 4. 模板检测：
 *    在模板文件中可使用 is_post_template() 函数检测当前文章是否使用自定义模板
 *    is_post_template() - 检测是否使用任何自定义模板
 *    is_post_template('single-custom.php') - 检测是否使用指定模板
 *
 * 5. 数据存储：
 *    配置保存在wp_postmeta表的'custom_post_template'字段中
 *    格式：文章ID => 模板文件名
 *
 * 6. 示例模板：
 *    已创建示例模板文件 single-custom.php，可直接使用或作为参考
 */

/**
 * @功能概述: 解码复杂编码的JSON数据
 *           参考CourseDataHandler的处理逻辑，处理多层URL编码
 *
 * @param {string} $json_string - 可能多层编码的JSON字符串
 *
 * @return {array|null} 解码后的数组，失败返回null
 */
function decode_complex_json($json_string)
{
    error_log("[JSONDecode] [开始] 尝试解码JSON数据");

    // 1. 尝试直接解码
    $decoded_data = json_decode($json_string, true);
    if (json_last_error() === JSON_ERROR_NONE && is_array($decoded_data)) {
        error_log("[JSONDecode] [成功] 直接解码成功");
        return $decoded_data;
    }

    // 2. 尝试先URL解码再JSON解码
    $urldecoded = urldecode($json_string);
    $decoded_data = json_decode($urldecoded, true);
    if (json_last_error() === JSON_ERROR_NONE && is_array($decoded_data)) {
        error_log("[JSONDecode] [成功] URL解码后JSON解码成功");

        // 对解码后的数据进行递归URL解码
        recursive_urldecode_array($decoded_data);
        return $decoded_data;
    }

    // 3. 尝试编码修复
    $fixed_encoding = mb_convert_encoding($json_string, 'UTF-8', 'auto');
    $decoded_data = json_decode($fixed_encoding, true);
    if (json_last_error() === JSON_ERROR_NONE && is_array($decoded_data)) {
        error_log("[JSONDecode] [成功] 编码修复后解码成功");
        return $decoded_data;
    }

    error_log("[JSONDecode] [失败] 所有解码尝试都失败了");
    return null;
}

/**
 * @功能概述: 递归对数组进行URL解码
 *           处理嵌套的URL编码数据
 *
 * @param {array} &$data - 需要解码的数组（引用传递）
 */
function recursive_urldecode_array(&$data)
{
    if (!is_array($data)) {
        return;
    }

    foreach ($data as $key => &$value) {
        if (is_string($value)) {
            // 检查是否包含编码特征，减少不必要的解码尝试
            if (strpos($value, '%') !== false || strpos($value, '+') !== false) {
                $decoded = urldecode($value);
                // 只有当解码成功且值发生改变时才更新
                if ($decoded !== false && $decoded !== $value) {
                    $value = $decoded;
                }
            }
        } elseif (is_array($value)) {
            // 递归处理嵌套数组
            recursive_urldecode_array($value);
        }
    }
    unset($value); // 断开最后一个元素的引用
}

/**
 * @功能概述: 在数组中递归替换域名
 *           查找所有字符串值中的旧域名并替换为新域名
 *
 * @param {array} &$data - 需要处理的数组（引用传递）
 * @param {array} $old_domains - 旧域名列表
 * @param {string} $new_domain - 新域名
 * @param {int} &$count - 替换计数器（引用传递）
 */
function replace_domains_in_array(&$data, $old_domains, $new_domain, &$count)
{
    if (!is_array($data)) {
        return;
    }

    foreach ($data as $key => &$value) {
        if (is_string($value)) {
            // 检查并替换字符串中的域名（包括URL编码的域名）
            foreach ($old_domains as $old_domain) {
                // 替换原始域名
                if (strpos($value, $old_domain) !== false) {
                    $before_count = substr_count($value, $old_domain);
                    $value = str_replace($old_domain, $new_domain, $value);
                    $count += $before_count;
                    if ($before_count > 0) {
                        error_log("[DomainReplace] [字段替换] {$key}: {$old_domain} -> {$new_domain} (替换{$before_count}次)");
                    }
                }

                // 替换URL编码的域名
                $encoded_old_domain = urlencode($old_domain);
                $encoded_new_domain = urlencode($new_domain);
                if (strpos($value, $encoded_old_domain) !== false) {
                    $before_count = substr_count($value, $encoded_old_domain);
                    $value = str_replace($encoded_old_domain, $encoded_new_domain, $value);
                    $count += $before_count;
                    if ($before_count > 0) {
                        error_log("[DomainReplace] [字段替换] {$key}: {$encoded_old_domain} -> {$encoded_new_domain} (URL编码替换{$before_count}次)");
                    }
                }
            }
        } elseif (is_array($value)) {
            // 递归处理嵌套数组
            replace_domains_in_array($value, $old_domains, $new_domain, $count);
        }
    }
    unset($value); // 断开最后一个元素的引用
}

function get_post_info($post_id)
{
    global $wpdb;
    $select_str = "SELECT meta_key,meta_value FROM  `" . $wpdb->prefix . "postmeta` WHERE post_id = " . $post_id;
    $ress = $wpdb->get_results($select_str);
    $post_info = array();
    foreach ($ress as $res) {
        $post_info[$res->meta_key] = $res->meta_value;
    }
    return $post_info;
}

function drop_user($user_id)
{
    return wp_delete_user($user_id);
}


//后台登陆数学验证码
function myplugin_add_login_fields()
{
//获取两个随机数, 范围0~9
    $num1 = rand(0, 9);
    $num2 = rand(0, 9);
//最终网页中的具体内容
    echo "<p><label for='math' class='small'>验证码</label><br /> $num1 + $num2 = ?<input type='text' name='sum' class='input' value='' size='25' tabindex='4'>"
        . "<input type='hidden' name='num1' value='$num1'>"
        . "<input type='hidden' name='num2' value='$num2'></p>";
}

add_action('login_form', 'myplugin_add_login_fields');

function login_val()
{
    $sum = isset($_POST['sum']) ? $_POST['sum'] : 0;//用户提交的计算结果
    $num1 = isset($_POST['num1']) ? $_POST['num1'] : 0;
    $num2 = isset($_POST['num2']) ? $_POST['num2'] : 0;

    switch ($sum) {
//得到正确的计算结果则直接跳出
        case $num1 + $num2:
            break;
//未填写结果时的错误讯息
        case null:
            wp_die('错误: 请输入验证码.');
            break;
//计算错误时的错误讯息
        default:
            wp_die('错误: 验证码错误,请重试.');
    }
}

add_action('login_form_login', 'login_val');


/*
 *
 * http请求
 *
 * */


function http_request($url, $post = '', $timeout = 5)
{
    if (empty($url)) {
        return;
    }
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);

    if ($post != '' && !empty($post)) {
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type：application/x-www-form-urlencoded', 'Content-Length: ' . strlen($post)));
    }
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
    $result = curl_exec($ch);
    curl_close($ch);
    return $result;
}


/**
 * PHP发送Json对象数据
 *
 * @param $url 请求url
 * @param $jsonStr 发送的json字符串
 * @return array
 */
function http_post_json($url, $jsonStr)
{
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonStr);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json; charset=utf-8', 'Content-Length: ' . strlen($jsonStr)));
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    return $response;
}


/*
 *
 * 创建有赞订单数据
 *
 * */
function  create_youzan_order($order_no, $order_user_id, $order_price, $order_time_stp)
{
    global $wpdb;


    $new_order = array(
        'order_no' => $order_no,
        'order_user_id' => $order_user_id,
        'order_price' => $order_price,
        'order_time_stp' => $order_time_stp
    );
    return $wpdb->insert($wpdb->prefix . 'youzan_order', $new_order);

}

/*
 * 查询有赞订单
 *
 * */

function search_youzan_order_by_order_no($order_no)
{
    global $wpdb;

    $selectStr = "SELECT * FROM  `" . $wpdb->prefix . "youzan_order` WHERE order_no =  '" . $order_no . "'";
    $ress = $wpdb->get_results($selectStr);
    return $ress;

}


/*
 *
 * 创建有赞订单数据
 *
 * */
function  create_unuse_youzan_order($order_no, $order_time_stp, $telephone_number, $total_fee, $total_vip_days, $vip_type)
{
    global $wpdb;


    $new_order = array(
        'order_no' => $order_no,
        'order_time_stp' => $order_time_stp,
        'telephone_number' => $telephone_number,
        'total_fee' => $total_fee,
        'total_vip_days' => $total_vip_days,
        'vip_type' => $vip_type
    );
    return $wpdb->insert($wpdb->prefix . 'unuse_order', $new_order);

}

/*
 * 查询有赞订单
 *
 * */

function search_unuse_youzan_order_by_order_no($order_no)
{
    global $wpdb;

    $selectStr = "SELECT * FROM  `" . $wpdb->prefix . "unuse_order` WHERE order_no =  '" . $order_no . "'";
    $ress = $wpdb->get_results($selectStr);
    return $ress;

}


/*
 *
 * 增加用户学课信息
 * */


function add_user_learning_mes($user_id, $post_id)
{
    $old_record_str = get_user_meta($user_id, "record_learning_mes", true);//获得课程信息json
    if ($old_record_str) {
        $old_record_arr = json_decode($old_record_str);//解析课程json
        //myDump($old_record_arr);
        $is_have = false;//课程信息中是否已经有了该课程
        foreach ($old_record_arr as $arr) {//遍历课程，查询是否有该课程
            if ($arr->post_id == $post_id) {
                $is_have = true;
            }
        }

        if ($is_have) {
            //有
            //若已经有了这篇文章
            //这里可以编辑这篇文章的其他信息
            //myDump($old_record_arr);//打印
            myDump($old_record_arr);
            return true;


        } else {//没有
            $new_arr = array("post_id" => $post_id);//将该课程信息变成一个数组
            array_push($old_record_arr, $new_arr);//将该课程数组压入总体数组
            myDump($old_record_arr);
            $record_arr_str = json_encode($old_record_arr);//转为json


        }

    } else {
        $new_arr = array("post_id" => $post_id);//将文章信息变成一个数组
        $record_arr = array();//总体记录数组
        array_push($record_arr, $new_arr);//将单个文章信息数组压入总体数组

        myDump($record_arr);
        $record_arr_str = json_encode($record_arr);//转为json
    }

    if (update_user_meta($user_id, "record_learning_mes", $record_arr_str)) {
        return true;
    }
    return false;
}


/*
 *
 * 删除用户学课信息
 * */


function delete_user_learning_mes($user_id, $post_id)
{

    $old_record_str = get_user_meta($user_id, "record_learning_mes", true);//获得课程信息json

    if ($old_record_str) {

        $old_record_arr = json_decode($old_record_str);//解析课程json

        $is_have = false;//课程信息中是否已经有了该课程
        $i = 0;
        foreach ($old_record_arr as $arr) {//遍历课程，查询是否有该课程
            if ($arr->post_id == $post_id) {
                $is_have = true;
                break;
            }
            $i++;
        }
        // myDump($i);

        if ($is_have) {
            //有
            //删除
            array_splice($old_record_arr, $i, 1);//删掉该文章的信息

            $record_arr_str = json_encode($old_record_arr);//转为json
            //myDump($old_record_arr);
            update_user_meta($user_id, "record_learning_mes", $record_arr_str);//保存数据库
        }

    }

}

/*
 *会员通知模板消息
 *
 * */

function inform_user_become_vip($total_vip_days, $user_id, $type, $cat_id_arr = array())
{
    $access_token = get_option("main_wechat_access_token");//微信签名
    $template_id = "4JifJRqemTf5iFz_JS7gfVhY7dJQETYQy9LF4YZzcu4";//模板消息id
    $postUrl = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=" . $access_token;//微信接口
    $go_url = home_url() . "/?action=1";//路径


    $lessonTitle = "";//课程标题
    $vipTitle = "";

    if ($type == "all") {
        $lessonTitle = "所有课程";
        $vipTitle = "VIP会员";
    } else {
        foreach ($cat_id_arr as $cat_id) {
            $cat_name = "《" . get_category($cat_id)->name . "》";
            $lessonTitle .= $cat_name;
        }

        $vipTitle = "课程会员";

    }


    $mh = curl_multi_init();

// 句柄数组
    $handle = array();
//返回数据集
    $chData = array();

    $openid_to_hlyyin = get_user_meta($user_id, "openid_to_hlyyin", true);//该用户在hlyyin上的openid
    $user_nickname = get_user_meta($user_id, "user_nickname", true);//用户昵称

    if (!$user_nickname) {
        $user_nickname = "匿名用户";
    }

    $telephone_number = get_user_meta($user_id, "telephone_number", true);//用户昵称
    if (!$telephone_number) {
        $telephone_number = "暂无记录";
    }


    if ($openid_to_hlyyin) {
        $first = "您好，" . $lessonTitle . "的听课权限已开通，增加时长为" . $total_vip_days . "天。";
        $keyword1_value = $user_nickname;
        $keyword2_value = $telephone_number;
        $keyword3_value = 0;
        $keyword4_value = $vipTitle;
        $keyword5_value = $user_id;
        $a = array(
            "first" => array("value" => "{$first}", "color" => "#FF0000"),
            "keyword1" => array("value" => "{$keyword1_value}"),
            "keyword2" => array("value" => "{$keyword2_value}"),
            "keyword3" => array("value" => "{$keyword3_value}"),
            "keyword4" => array("value" => "{$keyword4_value}"),
            "keyword5" => array("value" => "{$keyword5_value}"),
            "remark" => array("value" => "点击菜单“我的”，可以看到VIP到期时间。\n点击菜单“新概念”，开始上课吧！", "color" => "#00b9eb")

        );
        $b = array("touser" => "{$openid_to_hlyyin}", "template_id" => "{$template_id}", "data" => $a);

        $post = json_encode($b);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $postUrl);//url
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1); //设置有返回值，0，直接显示
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0); //禁用证书验证
        curl_setopt($ch, CURLOPT_POST, 1);  //post
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post);
        curl_multi_add_handle($mh, $ch);//往批处理中添加句柄
        $handle[0] = $ch;

    }


    $running = null;
// 执行批处理句柄
    do {
        curl_multi_exec($mh, $running);
        usleep(10000);
    } while ($running > 0);
    /* 读取资料 */
    foreach ($handle as $i => $ch) {
        $content = curl_multi_getcontent($ch);
        $chData[$i] = (curl_errno($ch) == 0) ? $content : false;
    }
    /* 移除 handle*/
    foreach ($handle as $ch) {
        curl_multi_remove_handle($mh, $ch);
    }
    curl_multi_close($mh);


}

/*
 *
 * 新会员通知并增加VIP时间
 * */

function inform_new_user_become_vip($total_vip_days, $user_id)
{
    $access_token = get_option("main_wechat_access_token");//access_token
    $template_id = "Cp1_xjRgwmHmGNmBGjCvGpcD4LR9WLN2ra0Fb7oqjuA";//模板消息id
    $postUrl = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=" . $access_token;//微信接口


    /*增加VIP时间*/

    add_user_viptime($user_id, $total_vip_days);


    $mh = curl_multi_init();

// 句柄数组
    $handle = array();
//返回数据集
    $chData = array();

    $openid_to_hlyyin = get_user_meta($user_id, "openid_to_hlyyin", true);//该用户在hlyyin上的openid
    $user_nickname = get_user_meta($user_id, "user_nickname", true);//用户昵称

    if (!$user_nickname) {
        $user_nickname = "匿名用户";
    }

    $telephone_number = get_user_meta($user_id, "telephone_number", true);//用户昵称
    if (!$telephone_number) {
        $telephone_number = "暂无记录";
    }


    if ($openid_to_hlyyin) {


        $first = "您已获得2天VIP会员，畅学哈密瓜英语所有课程。\n\n【学习教材】\n 传承20年的《新概念英语》系列教材，每天30分钟，从零基础学到流利英语！";
        $remark = "\n点击公众号菜单“课程”，立即开始上课。";

        $keyword1_value = $user_nickname;
        $keyword2_value = $telephone_number;
        $keyword3_value = $user_id;

        $a = array(
            "first" => array("value" => "{$first}", "color" => "#016FCE"),
            "keyword1" => array("value" => "{$keyword1_value}"),
            "keyword2" => array("value" => "{$keyword2_value}"),
            "keyword3" => array("value" => "{$keyword3_value}"),
            "remark" => array("value" => "{$remark}", "color" => "#FF0000")
        );
        $b = array("touser" => "{$openid_to_hlyyin}", "template_id" => "{$template_id}", "data" => $a);

        $post = json_encode($b);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $postUrl);//url
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1); //设置有返回值，0，直接显示
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0); //禁用证书验证
        curl_setopt($ch, CURLOPT_POST, 1);  //post
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post);
        curl_multi_add_handle($mh, $ch);//往批处理中添加句柄
        $handle[0] = $ch;

    }


    $running = null;
// 执行批处理句柄
    do {
        curl_multi_exec($mh, $running);
        usleep(10000);
    } while ($running > 0);
    /* 读取资料 */
    foreach ($handle as $i => $ch) {
        $content = curl_multi_getcontent($ch);
        $chData[$i] = (curl_errno($ch) == 0) ? $content : false;
    }
    /* 移除 handle*/
    foreach ($handle as $ch) {
        curl_multi_remove_handle($mh, $ch);
    }
    curl_multi_close($mh);


}


/*
 * 2021.5.26
 *
 * 赠送初创用户体验VIP，并通过微信客服消息发送
 * */

function give_new_user_free_vip_time($user_id, $total_vip_days = 0, $source_of_registered_users = "")
{

    /*
     *
     * 微信客服消息数据准备
     * */

    $access_token = get_option("main_wechat_access_token");//access_token

    $postUrl = "https://api.weixin.qq.com/cgi-bin/message/custom/send?access_token=" . $access_token;//微信客服消息接口


    $openid_to_hlyyin = get_user_meta($user_id, "openid_to_hlyyin", true);//该用户在hlyyin上的openid


    /*
     * 执行开通VIP
     *
     * */


    add_user_viptime($user_id, $total_vip_days);


    /*
     *根据不同来源的人群
     *
     * */


    switch ($source_of_registered_users) {
        case "xmly-xgn":
            $content_first_line = "● 亲爱的小伙伴，恭喜获得" . $total_vip_days . "天新概念英语学习会员";
            $content_second_line = "学英语，搞懂《新概念》就够了！";
            $goUrl = home_url() . "/?cat=43";//新概念一册目录页
            $button_text = ">>点此学习《新概念英语一册》";

            break;
        case "xmly-ljc":
            $content_first_line = "● 亲爱的小伙伴，恭喜获得" . $total_vip_days . "天英语VIP会员";
            $content_second_line = "零基础学英语，从发音开始！";
            $goUrl = home_url() . "/?cat=44";
            $button_text = ">>点此学习《零基础发音课》";

            break;
        case "xmly-czdc":
            $content_first_line = "● 亲爱的小伙伴，恭喜获得" . $total_vip_days . "天英语VIP会员";
            $content_second_line = "补习初中英语，从“新概念”开始。《新概念》教材大纲与中考考纲匹配度90%！";
            $goUrl = home_url() . "/?cat=43";//新概念一册目录页
            $button_text = ">>点此学习《新概念英语一册》";
            break;
        case "xmly-gzdc":
            $content_first_line = "● 亲爱的小伙伴，恭喜获得" . $total_vip_days . "天英语VIP会员";
            $content_second_line = "补习高中英语，从“新概念”开始。《新概念》教材大纲与中高考考纲匹配度90%！";
            $goUrl = home_url() . "/";//所有课程页面
            $button_text = ">>点此开始上课";
            break;
        case "xmly-mstl":
            $content_first_line = "● 亲爱的小伙伴，恭喜获得" . $total_vip_days . "天英语VIP会员";
            $content_second_line = "零基础学英语，从发音开始！";
            $goUrl = home_url() . "/?cat=44";
            $button_text = ">>点此学习《零基础发音课》";
            break;

        default :
            $content_first_line = "● 亲爱的小伙伴，恭喜获得" . $total_vip_days . "天新概念英语学习会员";
            $content_second_line = "学英语，搞懂《新概念》就够了！";
            $goUrl = home_url() . "/?cat=43";//新概念一册目录页
            $button_text = ">>开始学习《新概念英语一册》";
            break;

    }

    $data = '{"touser":"' . $openid_to_hlyyin . '","msgtype":"text","text":{"content":"' . $content_first_line . '\n\n' . $content_second_line . '\n\n<a href=\'' . $goUrl . ' \'>' . $button_text . '</a>"}}';


    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $postUrl);//url
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1); //设置有返回值，0，直接显示
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0); //禁用证书验证
    curl_setopt($ch, CURLOPT_POST, 1);  //post
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    $res = curl_exec($ch);//获取结果

    $arr = json_decode($res);
    $errcode = $arr->errcode;


    $isGood = false;
    if ($errcode == 0) {
        $isGood = true;
    }


    curl_close($ch);//关闭

    return $isGood;

}


function get_english_sentence_no_dot($sentence)
{
    $s = $sentence;
    $s = preg_replace("/\,/i", " ", $s);//英文逗号
    $s = preg_replace("/\./i", " ", $s);//英文句号
    $s = preg_replace("/\"/i", " ", $s);//英文引号
    $s = preg_replace("/\?/i", " ", $s);//英文问号
    $s = preg_replace("/\!/i", " ", $s);//英文感叹
    $s = preg_replace("/\:/i", " ", $s);//英文冒号
    $s = preg_replace("/\;/i", " ", $s);//英文分号
    $s = preg_replace("/\，/i", " ", $s);//中文逗号
    $s = preg_replace("/\。;/i", " ", $s);//中文句号
    $s = preg_replace("/\“/i", " ", $s);//中文前引号
    $s = preg_replace("/\”/i", " ", $s);//中文后引号
    $s = preg_replace("/\‘/i", "'", $s);//中文前引号
    $s = preg_replace("/\’/i", "'", $s);//中文前引号
    $s = preg_replace("/\？/i", " ", $s);//中文问号
    $s = preg_replace("/\！/i", " ", $s);//中文感叹
    $s = preg_replace("/\：/i", " ", $s);//中文冒号
    $s = preg_replace("/\；/i", " ", $s);//中文分号
    $s = preg_replace("/\（/i", " ", $s);//英语括号
    $s = preg_replace("/\）/i", " ", $s);//英语括号
    $s = preg_replace("/\(/i", " ", $s);//英语括号
    $s = preg_replace("/\)/i", " ", $s);//英语括号
    $s = str_replace("---", " ", $s);//四空
    $s = str_replace("--", " ", $s);//四空
    $s = str_replace("    ", " ", $s);//四空
    $s = str_replace("   ", " ", $s);//三空
    $s = str_replace("  ", " ", $s);//两空
    $s = trim($s);//两边空格

    return $s;
}


function get_english_sentence_has_dot($sentence)
{
    $s = $sentence;
    $s = preg_replace("/\,/i", " , ", $s);
    $s = preg_replace("/\./i", " . ", $s);
    $s = preg_replace("/\"/i", " \" ", $s);
    $s = preg_replace("/\?/i", " ? ", $s);
    $s = preg_replace("/\!/i", " ! ", $s);
    $s = preg_replace("/\:/i", " : ", $s);
    $s = preg_replace("/\;/i", " ; ", $s);
    $s = preg_replace("/\，/i", " , ", $s);//中文逗号
    $s = preg_replace("/\。;/i", " . ", $s);//中文句号
    $s = preg_replace("/\“/i", " \" ", $s);//中文前引号
    $s = preg_replace("/\”/i", " \" ", $s);//中文后引号
    $s = preg_replace("/\？/i", " ? ", $s);//中文问号
    $s = preg_replace("/\！/i", " ! ", $s);//中文感叹
    $s = preg_replace("/\：/i", " : ", $s);//中文冒号
    $s = preg_replace("/\；/i", " ; ", $s);//中文分号
    $s = preg_replace("/\（/i", " ( ", $s);//中文分号
    $s = preg_replace("/\）/i", " ) ", $s);//中文分号
    $s = preg_replace("/\(/i", " ( ", $s);//中文分号
    $s = preg_replace("/\)/i", " ) ", $s);//中文分号
    $s = str_replace("---", " --- ", $s);
    $s = str_replace("--", " -- ", $s);
    $s = str_replace("    ", " ", $s);
    $s = str_replace("   ", " ", $s);
    $s = str_replace("  ", " ", $s);
    $s = trim($s);

    return $s;
}


function remove_user($user_id)
{
    // Verify that the user intended to take this action.


    require_once(ABSPATH . 'wp-admin/includes/user.php');

    return wp_delete_user($user_id);
}


/*
 *
 * 有赞，为用户打标签
 * */


function tag_add_in_youzan($user_id, $tag_name)
{

    $openid_to_hlyyin = get_user_meta($user_id, "openid_to_hlyyin", true);//hlyyin的openid

    $myClientId = get_option("g_youzanyun_client_id");//应用的 client_id g_youzan_client_id
    $myClientSecret = get_option("g_youzanyun_client_secret");//应用的 client_secret g_youzan_client_secret

    $grant_type = "silent";//有赞默认参数
    $kdt_id = get_option("g_youzan_kdt_id");//有赞店铺ID g_youzan_kdt_id

    $get_youzanyun_token_url = get_option("g_youzanyun_get_access_token_url");
//$get_youzanyun_token_url = "https://open.youzanyun.com/auth/token";//获得有赞access请求URL

    $jsonStr = json_encode(array('authorize_type' => 'silent', 'client_id' => $myClientId, 'client_secret' => $myClientSecret, 'grant_id' => $kdt_id));

    $arr = json_decode(http_post_json($get_youzanyun_token_url, $jsonStr));

    $youzanyun_token = $arr->data->access_token;//有赞token


    /*
    *
    * 有赞打标签请求地址
    * 接口 https://open.youzanyun.com/api/youzan.scrm.tag.relation.add/4.0.0
    *
    *
    *
    */


    $get_url = get_option("g_youzanyun_scrm_tag_relation_add_url") . "?access_token=" . $youzanyun_token;//请求地址


    $jsonStr = json_encode(
        array(
            'tags' => $tag_name,
            'weixin_openid' => $openid_to_hlyyin
        )
    );


    $res = json_decode(http_post_json($get_url, $jsonStr));//结果


}


/*
 *
 * 给文章列表添加关卡设计按钮
 * */


function post_list_new_actions($actions, $post_object)
{

    global $current_user;


    $url = home_url() . "/?page_id=55093&post_id=" . $post_object->ID . "&model=learning_stage_design";


    if (current_user_can("edit_posts")) {

        $actions['stage'] = '<a href="' . $url . '" target="_blank">老关卡设计</a>';

    }


    return $actions;
}


add_filter('post_row_actions', 'post_list_new_actions', 10, 2);


/*
 *
 * 新关卡设计 in 文章列表
 *
 * */

function post_list_new_actions2($actions, $post_object)
{


    $url = home_url() . "/?page_id=73067&post_id=" . $post_object->ID;


    if (current_user_can("edit_posts")) {

        $actions['new_stage_design'] = '<a href="' . $url . '" target="_blank">新关卡设计</a>';

    }


    return $actions;
}


add_filter('post_row_actions', 'post_list_new_actions2', 10, 2);


/*
 *
 *
 *
 * 文章列表添加自定义列
 * */

add_filter('manage_post_posts_columns', 'add_new_book_columns');

function add_new_book_columns($book_columns)
{


    $new_columns['cb'] = '<input type="checkbox" />';//这个是前面那个选框，不要丢了

    $new_columns['title'] = '课程名';
    $new_columns['author'] = __('作者');

    $new_columns['categories'] = __('目录');

    $new_columns['date'] = _x('Date', 'column name');
    $new_columns['template'] = __('模板');
    $new_columns['id'] = __('ID');


    //直接返回一个新的数组
    return $new_columns;


}


add_action('manage_post_posts_custom_column', 'manage_book_columns', 10, 2);

function manage_book_columns($column_name, $id)
{
    global $wpdb;

  //  myDump($wpdb);

   switch ($column_name) {
        case 'id':
            echo $id;
            break;

        case 'template':


            $selectStr = "SELECT meta_value FROM `" . $wpdb->prefix . "postmeta` WHERE post_id =".$id." AND meta_key = 'custom_post_template'";
            $ress = $wpdb->get_var($selectStr);



            $template=($ress)?$ress:"默认模板";

           echo $template;





            break;
        default:
            break;
    }
}






/*
 *
 * 给富文本编辑器添加字体颜色选项
 * */






function add_editor_buttons($buttons)
{
    $buttons[] = 'fontselect';
    return $buttons;
}

add_filter("mce_buttons_3", "add_editor_buttons");


/*
 *
 * 除了admin以外，其他人不能看到别人文章
 * */

/**
 * @功能概述: 限制非管理员用户在后台编辑页面只能看到自己的文章。
 *           当用户访问 /wp-admin/edit.php 页面时，如果不是管理员，
 *           则自动过滤查询结果只显示当前用户创建的文章。
 *
 * @param {WP_Query} $wp_query - WordPress查询对象，用于修改查询条件。
 *
 * @return {void} 无返回值，直接修改传入的查询对象。
 *
 * @执行流程:
 *   1. 检查当前请求是否为后台文章编辑页面
 *   2. 验证用户权限级别（使用现代WordPress权限系统）
 *   3. 如果非管理员，则限制查询结果为当前用户的文章
 */
function mypo_parse_query_useronly($wp_query)
{
    $log_prefix = '[PostQueryFilter] '; // 日志前缀

    // 步骤 1: 检查是否为后台编辑页面
    if (strpos($_SERVER['REQUEST_URI'], '/wp-admin/edit.php') !== false) {
        error_log($log_prefix . '[步骤 1] 检测到后台编辑页面访问');

        // 步骤 2: 检查用户权限（修复：使用现代WordPress权限系统替代过时的level_10）
        if (!current_user_can('manage_options')) { // 管理员权限检查
            error_log($log_prefix . '[步骤 2] 用户非管理员，应用文章过滤');

            // 步骤 3: 限制查询结果为当前用户的文章
            global $current_user;
            $wp_query->set('author', $current_user->id);
            error_log($log_prefix . '[步骤 3] 已设置查询过滤，用户ID: ' . $current_user->id);
        } else {
            error_log($log_prefix . '[步骤 2] 用户为管理员，无需过滤');
        }
    }
}

add_filter('parse_query', 'mypo_parse_query_useronly');


//add_filter('xmlrpc_enabled', '__return_false');

?>



