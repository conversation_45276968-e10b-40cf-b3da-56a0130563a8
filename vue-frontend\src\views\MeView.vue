<template>
  <div class="w-full bg-white min-h-screen">
    <!-- 头部用户信息区域 (.cat_head) -->
    <div class="w-full h-[150px] bg-[#F74655] text-white">
      <!-- 用户头像区域 (.my_img) -->
      <div class="w-1/4 h-[100px] float-left">
        <img
          :src="user.avatar"
          alt="用户头像"
          class="w-4/5 max-h-20 my-2.5 mx-[10%] rounded-full"
        />
      </div>

      <!-- 用户文字信息区域 (.my_text) -->
      <div class="w-[65%] float-left overflow-hidden text-ellipsis whitespace-nowrap">
        <!-- 用户昵称 -->
        <div class="mt-[15px] w-full h-[25px] text-sm leading-[25px] font-medium">
          {{ user.name }}
        </div>
        <!-- 用户ID -->
        <div class="w-full h-5 text-sm leading-5">
          ID：{{ user.id }}
        </div>
        <!-- VIP状态 -->
        <div class="w-full h-5 text-sm leading-5">
          {{ user.vipStatus }}
        </div>
      </div>
    </div>

    <!-- 黑卡任务区域 (.list_area) - 仅黑卡VIP用户可见 -->
    <div
      v-if="user.isHeikaVip"
      class="w-[94%] float-left ml-[3%] bg-white rounded-lg -mt-10 cursor-pointer"
      @click="handleHeikaTaskClick"
    >
      <!-- 标题区域 (.list_area_title) -->
      <div class="w-full h-10 leading-10">
        <!-- 装饰线 -->
        <span class="w-[5%] float-left block h-5 border-r-[5px] border-orange-600 mt-2"></span>
        <!-- 标题文字 -->
        <span class="w-[26%] block h-9 float-left leading-9 text-center font-bold text-base">今日任务</span>
        <!-- 查看详情链接 -->
        <span class="w-[60%] block h-9 float-right leading-9 text-right text-[#999] text-xs pr-4">
          查看详情&nbsp;>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        </span>
      </div>

      <!-- 任务内容区域 (.list_area_action) -->
      <div class="w-full h-10 text-sm leading-10 text-left">
        <span class="text-[#999] ml-[6%] text-xs">{{ blackCardTask.content }}</span>
        <span class="text-[#999] ml-[1px] text-xs">（{{ blackCardTask.status }}）</span>
      </div>
    </div>

    <!-- 恢复VIP区域 - 老系统用户有暂停VIP时间时显示 -->
    <div
      v-if="user.oldSystemSaveVipTime > 0"
      class="w-[94%] float-left ml-[3%] bg-white rounded-lg -mt-10"
    >
      <div class="w-full h-10 text-sm leading-10 text-left">
        <span class="text-[#999] ml-[6%] text-xs">您有暂停的VIP时间，可立即恢复！</span>
      </div>
      <div class="w-full h-[60px] text-sm leading-10 text-left">
        <a :href="recoverVipUrl">
          <button class="w-[90%] h-9 mt-3 ml-[5%] leading-9 text-base rounded-[10px] border-0 bg-[#FFAA25] text-[#862116]">
            恢复VIP时间
          </button>
        </a>
      </div>
    </div>

    <!-- 老会员升级或开通黑卡区域 -->
    <div
      v-if="user.isOldVip && user.oldSystemSaveVipTime <= 0"
      class="w-[94%] float-left ml-[3%] bg-white rounded-lg -mt-10"
    >
      <div class="w-full h-10 text-sm leading-10 text-left">
        <span class="text-[#999] ml-[6%] text-xs">{{ vipActionText }}</span>
      </div>
      <div class="w-full h-[60px] text-sm leading-10 text-left">
        <a :href="vipActionUrl" v-if="!isOldSystemVipOver100()">
          <button class="w-[90%] h-9 mt-3 ml-[5%] leading-9 text-base rounded-[10px] border-0 bg-[#FFAA25] text-[#862116]">
            开通并升级黑卡VIP
          </button>
        </a>
        <button
          v-else
          @click="oldVipTimeGoToHeika"
          class="w-[90%] h-9 mt-3 ml-[5%] leading-9 text-base rounded-[10px] border-0 bg-[#FFAA25] text-[#862116]"
        >
          免费升级黑卡VIP
        </button>
      </div>
    </div>

    <!-- 非会员开通黑卡区域 -->
    <div
      v-if="!user.isOldVip && !user.isHeikaVip && user.oldSystemSaveVipTime <= 0"
      class="w-[94%] float-left ml-[3%] bg-white rounded-lg -mt-10"
    >
      <div class="w-full h-10 text-sm leading-10 text-left">
        <span class="text-[#999] ml-[6%] text-xs">{{ vipActionText }}</span>
      </div>
      <div class="w-full h-[60px] text-sm leading-10 text-left">
        <a :href="vipActionUrl">
          <button class="w-[90%] h-9 mt-3 ml-[5%] leading-9 text-base rounded-[10px] border-0 bg-[#FFAA25] text-[#862116]">
            开通黑卡VIP
          </button>
        </a>
      </div>
    </div>

    <!-- 学习历史区域 (.list_area_full) -->
    <div v-if="learningHistory.length > 0" class="w-full float-left bg-white mt-5">
      <!-- 标题行 (.daily_area_action_line) -->
      <div class="w-full h-[50px] border-t border-[#e8e8e8] leading-[50px] text-[0.9em]">
        <span class="glyphicon glyphicon-time ml-2.5 text-[#F74655]" aria-hidden="true"></span>
        <span class="ml-1.5">学习历史</span>
      </div>

      <!-- 历史课程展示 (.daily_area_action_history) -->
      <div class="w-full">
        <div
          v-for="(historyCat, key) in learningHistory"
          :key="key"
          class="w-1/3 h-[140px] float-left"
        >
          <div @click="clickCat(historyCat.cat_id)">
            <img
              :src="historyCat.cat_img_url"
              class="w-4/5 block float-left ml-[10%] max-h-[110px]"
            />
            <p class="w-[70%] leading-[30px] text-center text-[8px] float-left h-[30px] overflow-hidden text-ellipsis whitespace-nowrap ml-[15%] m-0">
              {{ historyCat.name }}
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- 其他菜单区域 (.list_area_full) -->
    <div class="w-full float-left bg-white mt-5">
      <!-- 今日复习 -->
      <a :href="todayReviewUrl">
        <div class="w-full h-[50px] border-t border-[#e8e8e8] leading-[50px] text-[0.9em]">
          <span class="glyphicon glyphicon-retweet ml-2.5 text-[#F74655]" aria-hidden="true"></span>
          <span class="ml-1.5">今日复习</span>
          <img
            v-if="todayReviewNum > 0"
            :src="todayReviewRedPointUrl"
            class="w-[7px] h-[7px] mb-2.5 float-right mr-4"
          />
        </div>
      </a>

      <!-- 设置 -->
      <a :href="settingUrl">
        <div class="w-full h-[50px] border-t border-[#e8e8e8] leading-[50px] text-[0.9em]">
          <span class="glyphicon glyphicon-cog ml-2.5 text-[#F74655]" aria-hidden="true"></span>
          <span class="ml-1.5">设置</span>
        </div>
      </a>
    </div>

    <!-- 测试用户标识 -->
    <div
      v-if="isTestUser"
      class="fixed bg-yellow-600 opacity-70 text-white left-2.5 bottom-0 w-[300px] h-[150px] p-4"
    >
      <div class="font-bold">测试用户</div>
      <div>当前为测试环境</div>
    </div>

    <!-- 功能菜单区域 -->
    <div class="w-full bg-white mt-5 mb-20">
      <!-- 今日复习菜单 -->
      <div class="w-full h-[50px] border-t border-[#e8e8e8] leading-[50px] text-[0.9em] flex items-center relative cursor-pointer hover:bg-gray-50 transition-colors" @click="handleTodayReviewClick">
        <!-- 图标 -->
        <div class="ml-2.5 text-[#F74655]">
          <svg class="w-5 h-5 inline-block" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
          </svg>
        </div>
        <span class="ml-2 flex-1">今日复习</span>
        <!-- 红点提示 -->
        <div class="w-[7px] h-[7px] bg-red-500 rounded-full mr-4"></div>
      </div>

      <!-- 设置菜单 -->
      <div class="w-full h-[50px] border-t border-[#e8e8e8] leading-[50px] text-[0.9em] flex items-center cursor-pointer hover:bg-gray-50 transition-colors" @click="handleSettingsClick">
        <!-- 图标 -->
        <div class="ml-2.5 text-[#F74655]">
          <svg class="w-5 h-5 inline-block" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd" />
          </svg>
        </div>
        <span class="ml-2 flex-1">设置</span>
      </div>
    </div>

    <!-- 测试用户标识 (仅测试环境显示) -->
    <div v-if="isTestUser" class="fixed bottom-0 left-0 w-[300px] h-[150px] bg-yellow-600 bg-opacity-70 text-white p-4 text-sm z-50">
      <div class="font-bold">测试用户</div>
      <div>当前为测试环境</div>
    </div>

    <!-- 底部导航 -->
    <BottomNav />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import BottomNav from '../components/BottomNav.vue';

// 用户数据 - 对应原始PHP变量
const user = reactive({
  id: '334973',
  avatar: 'https://www.shuimitao.online/wp-content/uploads/2024/05/cropped-shuimitao_online_logo.png',
  name: '哈密瓜',
  vipStatus: '黑卡VIP会员',
  isOldVip: false,
  isHeikaVip: true,
  oldSystemVipTime: 0,
  oldSystemSaveVipTime: 0,
  oldSystemLastTime: 0
});

// 测试用户标识
const isTestUser = ref(false);

// VIP相关数据
const vipActionText = ref('开通VIP，连续学习返现金！');
const vipActionUrl = ref('#');
const recoverVipUrl = ref('#');

// 黑卡任务数据
const blackCardTask = reactive({
  content: '学习2课',
  status: '已完成',
  url: '#'
});

// 学习历史数据 - 对应原始的history_cat_arr
const learningHistory = ref([
  {
    timestamp: Date.now(),
    cat_id: 1,
    name: '吃透新概念一册 (上)',
    cat_img_url: 'https://www.shuimitao.online/wp-content/uploads/2024/05/cropped-shuimitao_online_logo.png'
  },
  {
    timestamp: Date.now(),
    cat_id: 2,
    name: '零基础发音课',
    cat_img_url: 'https://www.shuimitao.online/wp-content/uploads/2024/05/cropped-shuimitao_online_logo.png'
  },
  {
    timestamp: Date.now(),
    cat_id: 3,
    name: '吃透新概念一册 (中)',
    cat_img_url: 'https://www.shuimitao.online/wp-content/uploads/2024/05/cropped-shuimitao_online_logo.png'
  }
]);

// 今日复习相关
const todayReviewUrl = ref('#');
const todayReviewNum = ref(3);
const todayReviewRedPointUrl = ref('/img/redpoint.png');

// 设置页面URL
const settingUrl = ref('#');

// 网站根URL
const webUrl = ref(window.location.origin);

// 页面加载完成后的处理
onMounted(() => {
  console.log('个人中心页面已加载');
  // 模拟检查是否为测试用户
  isTestUser.value = false;
});

// 方法定义 - 对应原始Vue实例的methods
const handleHeikaTaskClick = () => {
  window.location.href = blackCardTask.url;
};

const clickCat = (catId) => {
  window.location.href = `${webUrl.value}/?cat=${catId}`;
};

const isOldSystemVipOver100 = () => {
  return user.oldSystemVipTime > 100;
};

const oldVipTimeGoToHeika = () => {
  // 老VIP时间转换为黑卡的逻辑
  console.log('老VIP时间转换为黑卡');
};
</script>

<style scoped>
/* 确保浮动布局正常工作 */
.float-left {
  float: left;
}

.clear-both {
  clear: both;
}

/* Bootstrap Glyphicon 图标替代 */
.glyphicon {
  font-family: 'Glyphicons Halflings';
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.glyphicon-time:before {
  content: "🕒";
}

.glyphicon-retweet:before {
  content: "🔄";
}

.glyphicon-cog:before {
  content: "⚙️";
}

/* 确保图片圆形显示 */
.img-circle {
  border-radius: 50%;
}

/* 响应式图片 */
.img-responsive {
  max-width: 100%;
  height: auto;
}

/* 链接样式重置 */
a {
  color: inherit;
  text-decoration: none;
}

a:hover {
  color: inherit;
  text-decoration: none;
}

/* 按钮样式 */
button {
  cursor: pointer;
  font-weight: bold;
}

button:hover {
  opacity: 0.9;
}

/* 确保文字不换行和省略号 */
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 段落样式重置 */
p {
  margin: 0;
}
</style>