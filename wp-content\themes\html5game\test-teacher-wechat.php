<?php
/*
Template Name: Test Teacher WeChat
*/

// 测试老师微信配置功能
$teacher_wechat_qr = get_option('g_teacher_wechat_qr');

echo "<h1>老师微信配置测试</h1>";
echo "<p>配置值: " . ($teacher_wechat_qr ? $teacher_wechat_qr : '未配置') . "</p>";

if ($teacher_wechat_qr) {
    echo "<p>二维码图片:</p>";
    echo "<img src='" . $teacher_wechat_qr . "' style='max-width: 300px;' />";
} else {
    echo "<p>请先在WordPress后台 -> 外观 -> html5game主题设置 中上传老师微信二维码</p>";
}

// 记录调试信息
error_log('[TestTeacherWeChat] 配置测试 - 老师微信二维码: ' . ($teacher_wechat_qr ? $teacher_wechat_qr : '未配置'));
?>
