<?php
/*
Template Name:ajax
*/

/**
 * AJAX处理页面 - 课程数据通讯地址 (page_id=13794)
 *
 * 处理来自single.php的AJAX请求，包括：
 * - selectWord: 查询单词释义
 * - addWord: 添加单词到生词本
 * - saveReviewMes3: 保存复习信息
 * - save_course_rate_of_progress: 保存课程进度
 * - save_ask_teacher_text: 保存问老师文本
 * - user_listening_behavior: 保存用户行为数据
 * - speech_assessment: 语音评测
 * - 其他VIP相关功能
 *
 * <AUTHOR> Theme
 * @version 1.0
 */

// 开启错误日志记录
if (defined('WP_DEBUG') && WP_DEBUG) {
    error_log('[page-ajax.php] AJAX请求开始 - ' . date('Y-m-d H:i:s'));
    error_log('[page-ajax.php] POST数据: ' . print_r($_POST, true));
}

$action = $_POST['action'];
if (!$action) {
    error_log('[page-ajax.php] 错误: 缺少action参数');
    echo "禁止访问 - 缺少action参数";
    exit;
}

error_log('[page-ajax.php] 处理action: ' . $action);


/*下载微信文件*/

function downloadWeixinAudioAndRecognition($url, $media_id, $user_id)
{

    $filename = $user_id . "_" . $media_id;//MP3文件名
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_NOBODY, 0);    //只取body头
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    //$package = curl_exec($ch);
    //$httpinfo = curl_getinfo($ch);

    $audio = curl_exec($ch);//获得音频
    curl_close($ch);
    //myDump($audio);

    file_put_contents("./audio/" . $filename . "amr", $audio);//保存音频


    //开始翻译


    $baiduyun_app_id = '15728439';
    $baiduyun_api_key = '0EAwWdPVlOD3Wwfr45bM8NLt';
    $baiduyun_secret_key = 'LcGpoGrQhSi0xkaiBuGaUhhwajgY5H5S';


    $client = new AipSpeech($baiduyun_app_id, $baiduyun_api_key, $baiduyun_secret_key);

    $res = $client->asr(file_get_contents("./audio/" . $filename . "amr"), 'amr', 8000, array(
        'dev_pid' => 1737,
    ));//识别

    unset($audio);
    unlink("./audio/" . $filename . "amr");
    //myDump($res);

    if ($res["err_no"] == 0) {//获取字符串

        $sencesArr = $res["result"];//识别后的句子串
        $big_arr = array();

        for ($i = 0; $i < count($sencesArr); $i++) {
            $sencesArr[$i] = get_english_sentence_no_dot($sencesArr[$i]);
            $sencesArr[$i] = strtolower($sencesArr[$i]);
            $english_arr = explode(" ", $sencesArr[$i]);//预备信息数组就绪

            array_push($big_arr, $english_arr);
        }

        return $big_arr;


        //语音已识别
    } else {

        return $res["err_no"];

    }


}

/*PHP数组相似度比较*/


/**
 * 查询单词释义
 * 处理单词查询请求
 */
if ($action == "selectWord") {
    error_log('[page-ajax.php] 开始查询单词');

    $word = $_POST['word'];//要查询的单词

    // 参数验证
    if (!$word) {
        error_log('[page-ajax.php] 查询单词失败: 缺少word参数');
        echo "";
        exit;
    }

    error_log('[page-ajax.php] 查询单词: ' . $word);

    $res = get_dict_data($word);

    if ($res) {
        if ($res == 2) {
            error_log('[page-ajax.php] 单词查询结果: 没有释义');
            echo false;//输出false,代表没有释义
        } else {
            error_log('[page-ajax.php] 单词查询成功');
            echo $res;//输出结果,代表正常
        }
    } else {
        error_log('[page-ajax.php] 单词查询结果: 没有查到这个词');
        echo "";//输出空代表没有查到这个词
    }
    exit;
}//查词

/**
 * 添加单词到生词本
 * 处理单词添加请求
 */
if ($action == "addWord") {
    error_log('[page-ajax.php] 开始添加单词到生词本');

    $book_id = $_POST['book_id'];
    $wordori = $_POST['wordori'];

    // 参数验证
    if (!$book_id || !$wordori) {
        error_log('[page-ajax.php] 添加单词失败: 参数不完整 book_id=' . $book_id . ', wordori=' . $wordori);
        echo "0"; // 参数错误
        exit;
    }

    error_log('[page-ajax.php] 添加单词: ' . $wordori . ' 到生词本: ' . $book_id);

    $word_str = get_post_meta($book_id, "new_words_value", true);

    if ($word_str) {//有字符串
        if (strpos($word_str, "#")) {
            //字符串带#，多个单词
            $word_str_arr = explode("#", $word_str);
            $isExist = false;
            foreach ($word_str_arr as $word) {//比对单词是否存在
                if ($wordori == trim($word)) {
                    $isExist = true;
                    break;
                }
            }
            if (!$isExist) {//如果不存在
                array_push($word_str_arr, $wordori);
                $new_word_str = implode("#", $word_str_arr);
                if (update_post_meta($book_id, "new_words_value", $new_word_str)) {
                    error_log('[page-ajax.php] 单词添加成功');
                    echo "1";//成功
                    exit;
                } else {
                    error_log('[page-ajax.php] 单词添加失败: 数据库更新失败');
                }
            } else {
                error_log('[page-ajax.php] 单词已存在');
                echo "2";//已存在
                exit;
            }
        } else {
            //只有一个单词
            if (trim($word_str) == $wordori) {
                error_log('[page-ajax.php] 单词已存在');
                echo "2";//已存在
                exit;
            }
            $new_word_str = $word_str . "#" . $wordori;
            if (update_post_meta($book_id, "new_words_value", $new_word_str)) {
                error_log('[page-ajax.php] 单词添加成功');
                echo "1";//成功
                exit;
            } else {
                error_log('[page-ajax.php] 单词添加失败: 数据库更新失败');
            }
        }
    } else {
        //第一个单词
        if (update_post_meta($book_id, "new_words_value", $wordori)) {
            error_log('[page-ajax.php] 首个单词添加成功');
            echo "1";//成功
            exit;
        } else {
            error_log('[page-ajax.php] 首个单词添加失败: 数据库更新失败');
        }
    }

    error_log('[page-ajax.php] 单词添加失败');
    echo "0"; // 失败
    exit;
}//添加单词


/*
 *
 *
 * 保存复习信息*/


if ($action == "saveReviewMes3") {


    /*用户ID*/
    $user_id = $_POST['user_id'];


    /*课程ID*/
    $post_id = $_POST['post_id'];


    /*课程测试错误次数*/
    $fail_num = $_POST['fail_num'];//综合抽查错误数
    if (!$fail_num) {
        $fail_num = 0;
    }


    /*课程分类ID*/
    $model = $_POST['model'];


    /*日常任务活动标识*/
    $daily_aciton = $_POST['daily_aciton'];//日常活动是否开启，1为开启，0为不开启


    $is_add_vip = 0;//免费VIP是否获赠

    $todaymorning = strtotime(current_time("Y-m-d"));//当天0点

    $todaynight = $todaymorning + 24 * 3600 - 1;//当天23.59


    $review_mes_json = get_user_meta($user_id, "review_mes", true);//获取复习信息json

    if (!$review_mes_json) {//复习信息json值为空的情况


        update_user_meta($user_id, "review_begin_time", current_time("timestamp"));//更新学习开始时间
        update_user_meta($user_id, "review_now_time", current_time("timestamp"));//更新最新学习时间


        if ($daily_aciton) {//日常活动是否开启
            if (add_user_viptime_on_minutes($user_id, 20)) {//添加VIP时长 保存数据
                $is_add_vip = 1;//添加VIP成功标识
            }
        }


        /*完成次数*/
        $finishTimes = 1;

        /*下次复习时间*/
        $reviewTime = current_time("timestamp") + 24 * 3600 * 1;

        /*将课程ID 复习次数 课程分类ID 下次复习时间 测试错误次数 封装成一个 数组*/
        $json_arr = array("post_id" => $post_id, "finishTimes" => $finishTimes, "model" => $model, "reviewTime" => $reviewTime, "fail_num" => $fail_num);

        /*复习信息总数组*/
        $new_review_mes_arr = array();

        /*将单个课程的复习信息数组 合并入 复习信息总数组*/
        array_push($new_review_mes_arr, $json_arr);

        /*复习信息总数组转json*/
        $new_review_mes_json = json_encode($new_review_mes_arr);

        /*保存数据*/

        if ($is_add_vip) {
            if (update_user_meta($user_id, 'review_mes', $new_review_mes_json)) {
                echo 201;//111表达记录修改成功，且VIP时间添加成功
            } else {
                echo 200;//110表示记录修改失败，VIP时间添加成功
            }
        } else {
            echo (update_user_meta($user_id, 'review_mes', $new_review_mes_json)) ? 101 : 100;//101表达记录修改成功，100表达失败
        }


        delete_user_learning_mes($user_id, $post_id);//删除课程访问信息
        return;
    } else {
        $nowTime = current_time("timestamp");//当前时间
        $review_now_time = get_user_meta($user_id, "review_now_time", true);


        if ($review_now_time < $todaymorning) {
            //复习时间在昨天

            //领取时间在昨天
            if ($daily_aciton) {//日常活动是否开启

                if (add_user_viptime_on_minutes($user_id, 20)) {//添加VIP时长是否成功
                    $is_add_vip = 1;//添加VIP成功标识
                }
            }
        }

        update_user_meta($user_id, "review_now_time", $nowTime);


        $review_mes_arr = json_decode($review_mes_json);//解析json

        for ($i = 0; $i < count($review_mes_arr); $i++) {
            if ($post_id == $review_mes_arr[$i]->post_id) {
                //找到单词本
                if ($review_mes_arr[$i]->reviewTime <= $todaynight && $review_mes_arr[$i]->reviewTime >= $todaymorning) {//如果计划复习时间>今天早上，小于今天晚上,计入复习
                    $review_mes_arr[$i]->finishTimes += 1;//复习次数+1
                    $review_mes_arr[$i]->model = $model;//课程分类
                    $review_mes_arr[$i]->fail_num = $fail_num;//错误次数
                    switch ($review_mes_arr[$i]->finishTimes) {
                        case 2:
                            $review_mes_arr[$i]->reviewTime = current_time("timestamp") + 24 * 3600 * 2;//两天
                            break;
                        case 3:
                            $review_mes_arr[$i]->reviewTime = current_time("timestamp") + 24 * 3600 * 2;//两天
                            break;
                        case 4:
                            $review_mes_arr[$i]->reviewTime = current_time("timestamp") + 24 * 3600 * 4;//四天
                            break;
                        case 5:
                            $review_mes_arr[$i]->reviewTime = current_time("timestamp") + 24 * 3600 * 6;//六天
                            break;
                        default:
                            $review_mes_arr[$i]->reviewTime = 0;
                            break;

                    }//修改下次复习时间
                    $new_review_mes_json = json_encode($review_mes_arr);//转为json
                    if ($is_add_vip) {
                        if (update_user_meta($user_id, 'review_mes', $new_review_mes_json)) {
                            echo 201;//111表达记录修改成功，且VIP时间添加成功
                        } else {
                            echo 200;//110表示记录修改失败，VIP时间添加成功
                        }
                    } else {
                        echo (update_user_meta($user_id, 'review_mes', $new_review_mes_json)) ? 101 : 100;//101表达记录修改成功，100表达失败
                    }
                    return;

                } else {//不计入复习
                    echo 0;//输出0代表复习时间不正确
                    return;
                }
            }
        }//循环查找单词本，如果存在，则修改值；

        //没有找到该单词本
        $finishTimes = 1;
        $reviewTime = current_time("timestamp") + 24 * 3600 * 1;
        $json_arr = array("post_id" => $post_id, "finishTimes" => $finishTimes, "model" => $model, "reviewTime" => $reviewTime, "fail_num" => $fail_num);
        array_push($review_mes_arr, $json_arr);
        $new_review_mes_json = json_encode($review_mes_arr);//转为json

        if ($is_add_vip) {
            if (update_user_meta($user_id, 'review_mes', $new_review_mes_json)) {
                echo 201;//111表达记录修改成功，且VIP时间添加成功
            } else {
                echo 200;//110表示记录修改失败，VIP时间添加成功
            }

        } else {
            echo (update_user_meta($user_id, 'review_mes', $new_review_mes_json)) ? 101 : 100;//101表达记录修改成功，100表达失败
        }

        delete_user_learning_mes($user_id, $post_id);//删除课程访问信息
        return;
    }
}

/*
 *
 *
 * 朋友圈打卡*/

if ($action == "sign_in_wechat_timeline") {

    $user_id = $_POST['user_id'];//用户ID
    $time = current_time("timestamp");//当前时间

    $todaymorning = strtotime(current_time("Y-m-d"));//当天0点
    $lastdaynight = $todaymorning - 1;//昨天晚间23.59
    $lastdaymorning = $lastdaynight - 24 * 3600 + 1;//昨天0点

    $lastSign = get_user_meta($user_id, "user_sign_time", true);//上次签到时间

    if (!$lastSign) {
        $lastSign = 0;
    }

    $signNum = get_user_meta($user_id, "user_sign_num", true);//连续签到数
    if (!$signNum) {
        $signNum = 0;
    }


    if ($lastSign < $todaymorning) {
        if ($lastSign < $lastdaymorning) {
            $signNum = 1;
        } else {
            $signNum += 1;
        }
        update_user_meta($user_id, "user_sign_time", $time);//更新签到时间
        update_user_meta($user_id, "user_share_time", $time);//更新分享时间
        update_user_meta($user_id, "user_sign_num", $signNum);//更新连续签到数


        if ($signNum >= 2) {
            if (add_user_viptime_on_minutes($user_id, 60)) {
                echo 2;//提醒获得VIP
            }
        } else {
            echo 1;//提醒还需要签到一天

        }

    } else {
        echo 5;
    }


}


/*
 *
 *
 * 获得语音结果*/


if ($action == "giveAMark") {
    $voice_id = $_POST['voiceId'];
    $access_token = get_option("main_wechat_access_token");


    $postUrl = "http://api.weixin.qq.com/cgi-bin/media/voice/queryrecoresultfortext?access_token=" . $access_token . "&voice_id=" . $voice_id . "&lang=en_US";


    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $postUrl);//url
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1); //设置有返回值，0，直接显示
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0); //禁用证书验证
    curl_setopt($ch, CURLOPT_POST, 1);  //post
    $data = curl_exec($ch);
    echo $data;
    curl_close($ch);


}


/*
 *
 * 有赞订单开通VIP
 *
 * */


if ($action == "youzan_vip_go") {

    require_once 'includes/youzanSdk/lib/YZTokenClient.php';

    $user_id = $_POST['user_id'];
    $tid = $_POST['tid'];


    if (!search_youzan_order_by_order_no($tid)) {//数据库里没有订单

        //检查有赞订单 并开通VIP

        /*
         * 抓取订单数据
         *
         * */

        $youzan_client_id = "a3b0ef40ff671be069";//有赞client_id
        $youzan_client_secret = "86a8f864d6af8ea09bebf57972c560de";//有赞client_secret
        $grant_type = "silent";//有赞默认参数
        $kdt_id = 17925206;//有赞店铺ID
        $url = "https://open.youzan.com/oauth/token";
        $post_string = "client_id={$youzan_client_id}&client_secret={$youzan_client_secret}&grant_type=silent&kdt_id={$kdt_id}";
        $resobj = json_decode(http_request($url, $post_string));
        $youzan_token = $resobj->access_token;//有赞acess_token


        $client = new YZTokenClient($youzan_token);
        $method = 'youzan.trade.get'; //要调用的api名称
        $api_version = '4.0.0'; //要调用的api版本号
        $my_params = array('tid' => $tid);
        $my_files = array();
        $youzan_order_res = $client->post($method, $api_version, $my_params, $my_files);


        /*
         * 订单是否已经支付
         *
         * */
        $is_user_payed = false;//订单状态预设为未支付

        $order_info_obj = $youzan_order_res["response"]["full_order_info"]["order_info"];//订单信息对象
        $order_status = $order_info_obj["status_str"];//获取订单状态
        $is_payed = $order_info_obj["order_tags"]["is_payed"];//订单是否支付
        if ($order_status == "已发货" && $is_payed) {//若订单状态为“已发货”并且订单已支付
            $is_user_payed = true;//确定用户已支付.
        }


        /*
            * 订单已支付的情况下，获取订单信息及用于信息
            * */


        if ($is_user_payed) {


            $order_obj = $youzan_order_res["response"]["full_order_info"]["orders"][0];//订单对象
            $bought_number = (int)$order_obj["num"];//购买数量
            $vip_days = $bought_number * (int)$order_obj["outer_item_id"];//应该开通的VIP天数

            $order_price = (float)$order_obj["total_fee"];//订单金额
            //myDump($order_price);
            $order_time_stp = current_time("timestamp");//时间戳；

            //myDump($order_price);
            create_youzan_order($tid, $user_id, $order_price, $order_time_stp);//在数据库内保存有赞的订单


            if (add_user_viptime($user_id, $vip_days)) {
                //为用户增加VIP时间

                echo $vip_days;//用户增加VIP时间成功；
            } else {
                echo 3;//发生异常，需要联系客服
            }


            $buyer_obj = json_decode($order_obj["buyer_messages"]);//用户对象
            $buyer_number = "";//用户手机号
            foreach ($buyer_obj as $n) {
                $buyer_number = $n;//获得用户手机号
            }

            update_user_meta($user_id, "telephone_number", $buyer_number);//保存用户手机号*/


        } else {
            echo 4;//订单未支付
        }


    } else {
        echo 5;//订单已经使用
    }


}


/*
 * 暂停VIP
 *
 * */


if ($action == "pause_vip") {
    $user_id = $_POST['user_id'];
    $user_viptime = get_user_meta($user_id, "user_viptime", true);//获取用户当前VIP时间

    $rest_vip_time = $user_viptime - current_time("timestamp");//计算剩余VIP时间

    if ($rest_vip_time > 0) {
        $time = current_time("timestamp") - 100;

        if (update_user_meta($user_id, "user_save_viptime", $rest_vip_time) && update_user_meta($user_id, "user_viptime", $time)) {
            echo 1;
        } else {
            echo 0;
        }
    } else {
        echo 0;
    }


}


/*
 * 恢复VIP
 *
 * */

if ($action == "recover_vip") {
    $user_id = $_POST['user_id'];
    $user_save_viptime = get_user_meta($user_id, "user_save_viptime", true);//获取用户当前VIP时间
    $user_viptime = get_user_meta($user_id, "user_viptime", true);

    if ($user_save_viptime) {
        if ($user_viptime - current_time("timestamp") > 0) {//目前还是VIP
            $user_viptime += $user_save_viptime;//叠加VIP时间
        } else {//不是VIP
            $user_viptime = current_time("timestamp") + $user_save_viptime;//增加VIP时间
        }

        if (update_user_meta($user_id, "user_viptime", $user_viptime) && update_user_meta($user_id, "user_save_viptime", "") && update_user_meta($user_id, "user_last_stop_vip_time", current_time("timestamp"))) {
            echo 1;
        } else {
            echo 0;
        }

    } else {
        echo 0;
    }


}


/*
 * 语音评分
 *
 * */

/**
 * 语音评测功能
 * 处理来自single.php的voice_speech_assessment_ajax调用
 */
if ($action == "speech_assessment") {
    error_log('[page-ajax.php] 开始处理语音评测');

    $user_id = $_POST['user_id'];
    $media_id = $_POST['media_id'];//media_id
    $question_stem_1 = $_POST['question_stem_1'];

    // 参数验证
    if (!$user_id || !$media_id || !$question_stem_1) {
        error_log('[page-ajax.php] 语音评测参数不完整: user_id=' . $user_id . ', media_id=' . $media_id . ', question_stem_1=' . $question_stem_1);
        echo 0;
        exit;
    }

    $question_stem_1 = get_english_sentence_no_dot($question_stem_1);
    $question_stem_1 = strtolower($question_stem_1);
    $english_arr = explode(" ", $question_stem_1);//预备信息数组就绪

    error_log('[page-ajax.php] 语音评测目标句子: ' . $question_stem_1);

    $access_token = get_option("main_wechat_access_token");

    if (!$access_token) {
        error_log('[page-ajax.php] 错误: 微信access_token未配置');
        echo 0;
        exit;
    }

    $get_audio_url = "https://api.weixin.qq.com/cgi-bin/media/get?access_token=" . $access_token . "&media_id=" . $media_id;//文件下载地址

    error_log('[page-ajax.php] 开始下载并识别语音: ' . $get_audio_url);

    $user_english_arrs = downloadWeixinAudioAndRecognition($get_audio_url, $media_id, $user_id);//识别用户语音，并获得数组

    if (is_array($user_english_arrs)) {
        error_log('[page-ajax.php] 语音识别成功，开始计算得分');

        $score = "";
        $max_right_num = 0;//最大匹配数

        foreach ($user_english_arrs as $user_english_arr) {

            if (count($user_english_arr) <= count($english_arr)) {
                $right_arr = array_intersect($user_english_arr, $english_arr);//将用户语音数组与正确数组比对,返回交集数组
                $right_num = count($right_arr);//计算交集数组的键数
                if ($right_num >= $max_right_num) {
                    $max_right_num = $right_num;//获得最大交集键数
                }
            }

        }

        if ($max_right_num == 0) {
            $score = 10;
        } else {
            $total_num = count($english_arr);
            $ori_score = (int)round($max_right_num / $total_num * 100);

            if ($ori_score > 100) {
                $score = 100;
            } else {
                $score = $ori_score;
            }
        }

        error_log('[page-ajax.php] 语音评测完成，得分: ' . $score);
        echo $score;
        return;

    } else {
        error_log('[page-ajax.php] 语音识别失败: ' . print_r($user_english_arrs, true));
    }

    echo 0;
    exit;
}


if ($action == "speech_assessment2") {

    $user_id = $_POST['user_id'];//用户ID
    $media_id = $_POST['media_id'];//media_id

    $question_stem_1 = $_POST['question_stem_1'];//要求阅读英文句子

    $question_stem_1 = get_english_sentence_no_dot($question_stem_1);//句子无标点版本

    $question_stem_1 = strtolower($question_stem_1);//所有单词降为小写
    $english_arr = explode(" ", $question_stem_1);//单词数组

    //myDump($english_arr);


    $access_token = get_option("main_wechat_access_token");//微信签名


    $get_audio_url = "https://api.weixin.qq.com/cgi-bin/media/get?access_token=" . $access_token . "&media_id=" . $media_id;//音频下载地址


    $filename = $user_id . "_" . $media_id;//预设音频MP3文件名

    /*
     * curl拉取音频
     * */

    $ch = curl_init($get_audio_url);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_NOBODY, 0);    //只取body头
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

    $audio = curl_exec($ch);//获得音频
    curl_close($ch);

    //myDump($audio);


    file_put_contents("./audio/" . $filename . "amr", $audio);//保存音频


    //开始使用百度翻译翻译

    //开始翻译


    $baiduyun_app_id = '15728439';
    $baiduyun_api_key = '0EAwWdPVlOD3Wwfr45bM8NLt';
    $baiduyun_secret_key = 'LcGpoGrQhSi0xkaiBuGaUhhwajgY5H5S';


    $client = new AipSpeech($baiduyun_app_id, $baiduyun_api_key, $baiduyun_secret_key);

    $res = $client->asr(file_get_contents("./audio/" . $filename . "amr"), 'amr', 8000, array(
        'dev_pid' => 1737,
    ));//识别

    unset($audio);
    unlink("./audio/" . $filename . "amr");

    myDump($res);


}


/*
 *
 * 保存用户学习进度
 *
 * */

/**
 * 保存课程学习进度
 * 处理来自single.php的save_course_rate_of_progress_ajax调用
 */
if ($action == "save_course_rate_of_progress") {
    error_log('[page-ajax.php] 开始保存课程进度');

    $user_id = $_POST['user_id'];
    $post_id = $_POST['post_id'];
    $maxpaged = $_POST['maxpaged'];

    // 参数验证
    if (!$user_id || !$post_id || !$maxpaged) {
        error_log('[page-ajax.php] 保存课程进度参数不完整: user_id=' . $user_id . ', post_id=' . $post_id . ', maxpaged=' . $maxpaged);
        echo "参数错误";
        exit;
    }

    error_log('[page-ajax.php] 保存课程进度 - 用户ID: ' . $user_id . ', 课程ID: ' . $post_id . ', 最大页数: ' . $maxpaged);

    $course_rate_of_progress_json = get_user_meta($user_id, "course_rate_of_progress", true);//获取课程进度json

    if (!$course_rate_of_progress_json) {
        //课程进度为空，创建新记录
        error_log('[page-ajax.php] 创建新的课程进度记录');

        /*将课程ID 课程最大翻页 封装成一个 数组*/
        $json_arr = array("post_id" => $post_id, "maxpaged" => $maxpaged, "timestamp" => current_time("timestamp"));

        /*课程进度总数组*/
        $new_course_rate_of_progress_arr = array();

        /*将单个课程的复习信息数组 合并入 课程进度总数组*/
        array_push($new_course_rate_of_progress_arr, $json_arr);

        /*复习信息总数组转json*/
        $new_course_rate_of_progress_json = json_encode($new_course_rate_of_progress_arr);

        /*保存数据*/
        if (update_user_meta($user_id, 'course_rate_of_progress', $new_course_rate_of_progress_json)) {
            error_log('[page-ajax.php] 新课程进度保存成功');
        } else {
            error_log('[page-ajax.php] 新课程进度保存失败');
        }

    } else {
        //更新现有课程进度
        error_log('[page-ajax.php] 更新现有课程进度');

        $course_rate_of_progress_arr = json_decode($course_rate_of_progress_json);//解析json

        $has_progress = false;//是否有进度

        for ($i = 0; $i < count($course_rate_of_progress_arr); $i++) {
            if ($post_id == $course_rate_of_progress_arr[$i]->post_id) {
                //找到课程
                $has_progress = true;
                if ($course_rate_of_progress_arr[$i]->maxpaged < $maxpaged) {
                    //如果课程最大翻页数<当前翻页数
                    error_log('[page-ajax.php] 更新课程最大页数从 ' . $course_rate_of_progress_arr[$i]->maxpaged . ' 到 ' . $maxpaged);
                    $course_rate_of_progress_arr[$i]->maxpaged = $maxpaged;//记录最大翻页
                    $course_rate_of_progress_arr[$i]->timestamp = current_time("timestamp");//记录时间戳
                }
                break;
            }
        }

        if (!$has_progress) {//无进度处理
            error_log('[page-ajax.php] 为课程添加新的进度记录');
            $json_arr = array("post_id" => $post_id, "maxpaged" => $maxpaged, "timestamp" => current_time("timestamp"));
            array_push($course_rate_of_progress_arr, $json_arr);
        }

        $new_course_rate_of_progress_json = json_encode($course_rate_of_progress_arr);//转为json

        /*保存数据*/
        if (update_user_meta($user_id, 'course_rate_of_progress', $new_course_rate_of_progress_json)) {
            error_log('[page-ajax.php] 课程进度更新成功');
        } else {
            error_log('[page-ajax.php] 课程进度更新失败');
        }
    }

    // 返回成功信息
    echo json_encode(array(
        'status' => 'success',
        'maxpaged' => $maxpaged,
        'user_id' => $user_id,
        'post_id' => $post_id,
        'timestamp' => current_time("timestamp")
    ));
    exit;
}


/*
 *
 * 保存问老师文本
 *
 * */


/**
 * 保存问老师文本
 * 处理来自single.php的save_ask_teacher_text_ajax调用
 */
if ($action == "save_ask_teacher_text") {
    error_log('[page-ajax.php] 开始保存问老师文本');

    $user_id = $_POST['user_id'];//用户ID
    $post_id = $_POST['post_id'];//课程ID
    $pageindex = $_POST['pageindex'];//指针
    $ask_teacher_text = $_POST['ask_teacher_text'];//提问

    // 参数验证
    if (!$user_id || !$post_id) {
        error_log('[page-ajax.php] 保存问老师文本参数不完整: user_id=' . $user_id . ', post_id=' . $post_id);
        echo "参数错误";
        exit;
    }

    if (!$ask_teacher_text) {
        $ask_teacher_text = "";
    }

    error_log('[page-ajax.php] 保存问老师文本 - 用户ID: ' . $user_id . ', 课程ID: ' . $post_id . ', 提问: ' . $ask_teacher_text);

    $post_cat_name = $_POST['post_cat_name'];//课程目录名
    $post_cat_id = $_POST['post_cat_id'];//课程目录ID
    $post_title = $_POST['post_title'];//课程名称
    $stageType = $_POST['stageType'];//关卡类型
    $card_bindKnowledge = ($_POST['card_bindKnowledge']) ? $_POST['card_bindKnowledge'] : "";//绑定知识点
    $card_review_go_num = ($_POST['card_review_go_num']) ? $_POST['card_review_go_num'] : "";//行走步子

    $card_question_stem_1 = ($_POST['card_question_stem_1']) ? $_POST['card_question_stem_1'] : "";//题干1
    $card_question_stem_2 = ($_POST['card_question_stem_2']) ? $_POST['card_question_stem_2'] : "";//题干2

    $card_right_option = ($_POST['card_right_option']) ? $_POST['card_right_option'] : "";//正确选项
    $card_option_1 = ($_POST['card_option_1']) ? $_POST['card_option_1'] : "";//选项1
    $card_option_2 = ($_POST['card_option_2']) ? $_POST['card_option_2'] : "";//选项2
    $card_option_3 = ($_POST['card_option_3']) ? $_POST['card_option_3'] : "";//选项3

    $card_learn_word = ($_POST['card_learn_word']) ? $_POST['card_learn_word'] : "";//单词
    $card_word_explain = ($_POST['card_word_explain']) ? $_POST['card_word_explain'] : "";//单词释义
    $card_english_sentence = ($_POST['card_english_sentence']) ? $_POST['card_english_sentence'] : "";//英语句子
    $card_chinese_sentence = ($_POST['card_chinese_sentence']) ? $_POST['card_chinese_sentence'] : "";//中文句子

    if ($ask_teacher_text) {
        error_log('[page-ajax.php] 开始保存问老师数据到数据库');

        global $wpdb;

        $new_question = array(
            'post_id' => $post_id,
            'user_id' => $user_id,
            'pageindex' => $pageindex,
            'ask_teacher_text' => $ask_teacher_text,
            'post_cat_name' => $post_cat_name,
            'post_cat_id' => $post_cat_id,
            'post_title' => $post_title,
            'stageType' => $stageType,
            'card_bindKnowledge' => $card_bindKnowledge,
            'card_review_go_num' => $card_review_go_num,
            'card_question_stem_1' => $card_question_stem_1,
            'card_question_stem_2' => $card_question_stem_2,
            'card_right_option' => $card_right_option,
            'card_option_1' => $card_option_1,
            'card_option_2' => $card_option_2,
            'card_option_3' => $card_option_3,
            'card_learn_word' => $card_learn_word,
            'card_word_explain' => $card_word_explain,
            'card_english_sentence' => $card_english_sentence,
            'card_chinese_sentence' => $card_chinese_sentence,
            'timestamp' => current_time("timestamp")
        );

        $result = $wpdb->insert($wpdb->prefix . 'user_ask_teacher', $new_question);

        if ($result !== false) {
            error_log('[page-ajax.php] 问老师数据保存成功，插入ID: ' . $wpdb->insert_id);
            echo json_encode(array(
                'status' => 'success',
                'message' => '提问保存成功',
                'insert_id' => $wpdb->insert_id
            ));
        } else {
            error_log('[page-ajax.php] 问老师数据保存失败: ' . $wpdb->last_error);
            echo json_encode(array(
                'status' => 'error',
                'message' => '提问保存失败'
            ));
        }
    } else {
        error_log('[page-ajax.php] 提问内容为空，不保存');
        echo json_encode(array(
            'status' => 'success',
            'message' => '提问内容为空'
        ));
    }
    exit;
}


/*
 *
 * 保存用户行为数据
 *
 * */

/**
 * 保存用户学习行为数据
 * 处理来自single.php的用户行为统计
 */
if ($action == "user_listening_behavior") {
    error_log('[page-ajax.php] 开始保存用户行为数据');

    $user_id = $_POST['user_id'];

    // 参数验证
    if (!$user_id) {
        error_log('[page-ajax.php] 用户行为数据保存失败: 缺少user_id');
        echo "参数错误";
        exit;
    }

    /*
     * 获得用户行为json 并转化为数组
     * */
    $user_listening_behavior = get_user_meta($user_id, "user_listening_behavior", true);

    if ($user_listening_behavior) {
        $user_listening_behavior_arr = json_decode($user_listening_behavior);
    } else {
        $user_listening_behavior_arr = array();
    }

    $post_id = $_POST['post_id'];
    $maxpaged = $_POST['maxpaged'];
    $entertime = $_POST['entertime'];
    $is_user_finish_the_course = $_POST['is_user_finish_the_course']; //用户是否完成课程
    $stay_for_time = $_POST['stay_for_time'];

    error_log('[page-ajax.php] 用户行为数据 - 用户ID: ' . $user_id . ', 课程ID: ' . $post_id . ', 停留时间: ' . $stay_for_time);

    /*
     * 新行为数据
     * */
    $new_behavior = array(
        'user_id' => $user_id,
        'post_id' => $post_id,
        'maxpaged' => $maxpaged,
        'entertime' => $entertime,
        'is_user_finish_the_course' => $is_user_finish_the_course,
        'stay_for_time' => $stay_for_time,
        'timestamp' => current_time("timestamp")
    );

    /*
     * 同一时间进入课程，替换之前用户行为
     * */
    if (count($user_listening_behavior_arr) > 0) {
        for ($i = 0; $i < count($user_listening_behavior_arr); $i++) {
            if ($user_listening_behavior_arr[$i]->post_id == $new_behavior['post_id']) {
                if ($user_listening_behavior_arr[$i]->entertime == $new_behavior['entertime']) {
                    error_log('[page-ajax.php] 替换相同时间的行为记录');
                    array_splice($user_listening_behavior_arr, $i, 1);
                    break;
                }
            }
        }
    }

    /*
     * 并入行为总数组
     * */
    array_push($user_listening_behavior_arr, $new_behavior);

    /*
     * 行为最大200个，超出则删除最早的记录
     * */
    if (count($user_listening_behavior_arr) > 200) {
        error_log('[page-ajax.php] 行为记录超过200个，删除最早记录');
        array_shift($user_listening_behavior_arr);
    }

    /*
     * 保存数据
     * */
    $new_user_listening_behavior = json_encode($user_listening_behavior_arr);

    if (update_user_meta($user_id, "user_listening_behavior", $new_user_listening_behavior)) {
        error_log('[page-ajax.php] 用户行为数据保存成功');
        echo json_encode(array(
            'status' => 'success',
            'message' => '用户行为存储成功',
            'behavior_count' => count($user_listening_behavior_arr)
        ));
    } else {
        error_log('[page-ajax.php] 用户行为数据保存失败');
        echo json_encode(array(
            'status' => 'error',
            'message' => '用户行为存储失败'
        ));
    }
    exit;
}


