# VIPTimeManager 插件

## 插件概述

VIPTimeManager 是一个WordPress插件，用于在后台管理用户的黑卡VIP时间。该插件提供了查询、增加和删除用户VIP资格的功能，并与前端系统的VIP判断逻辑完全兼容。

## 主要功能

### 1. 用户VIP信息查询
- 通过用户ID查询VIP状态
- 显示VIP类型（永久卡/限时卡）
- 显示VIP到期时间
- 支持实时状态验证

### 2. VIP时间管理
- 为用户增加VIP天数
- 支持未过期卡延期
- 支持过期卡重新激活
- 自动处理时间计算

### 3. VIP资格删除
- 完全删除用户VIP资格
- 清除所有VIP相关数据
- 支持批量操作确认

## 兼容性特性

### 前端系统兼容
插件创建的VIP数据完全兼容前端系统的判断逻辑：

```json
{
    "card_type": "HAVE_DATE",
    "can_use_expiration_date": 1625097600,
    "big_course_type": "hlyyin",
    "specific_course": "ALL",
    "training_expiration_date": 0,
    "cash_back_times": 0
}
```

### 向后兼容
- 自动检测并补全缺失的必需字段
- 兼容旧版本创建的VIP数据
- 保持数据结构一致性

## 技术实现

### 数据存储
- 使用WordPress用户元数据表（wp_usermeta）
- 字段名：`heika_vip`
- 数据格式：JSON数组

### 安全机制
- WordPress nonce验证
- 管理员权限检查
- 参数验证和清理
- 详细的错误日志

### AJAX接口
- `selectUserVipTime`：查询用户VIP信息
- `increase_heika_vip_time`：增加VIP时间
- `delete_heika`：删除VIP资格

## 使用方法

### 访问插件
1. 登录WordPress后台
2. 进入"设置" → "用户VIP时间管理"
3. 使用界面进行VIP管理操作

### 查询用户VIP
1. 输入用户ID
2. 点击"搜索"按钮
3. 查看用户VIP状态和到期时间

### 增加VIP时间
1. 搜索并选择用户
2. 输入要增加的天数
3. 点击"保存"按钮确认

### 删除VIP资格
1. 搜索并选择用户
2. 点击"删除黑卡会员资格"按钮
3. 确认删除操作

## 日志和调试

### 日志位置
插件的详细日志记录在：`wp-content/debug.log`

### 日志内容
- 插件加载状态
- AJAX请求处理过程
- VIP数据操作结果
- 错误和异常信息

### 调试模式
在`wp-config.php`中启用调试模式：
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## 版本历史

### v1.1.0 (2025-07-09)
- 修复VIP数据结构兼容性问题
- 添加big_course_type字段支持
- 添加向后兼容处理
- 优化VIP查询逻辑
- 增强日志记录

### v1.0.1 (2025-07-08)
- 解决函数名冲突问题
- 添加独特函数前缀
- 完善错误处理

### v1.0.0 (2025-07-08)
- 初始版本发布
- 基本VIP管理功能
- AJAX接口实现

## 注意事项

1. **权限要求**：只有管理员用户可以使用此插件
2. **数据备份**：建议在使用前备份用户数据
3. **兼容性**：确保与主题的VIP判断逻辑保持一致
4. **测试环境**：建议先在测试环境中验证功能

## 技术支持

如有问题或建议，请联系开发者：showlin
