<?php
/*
Template Name:newStageDesign
*/


if (current_user_can("edit_posts")) {
    get_header();
    wp_enqueue_media();

    $selectUrl = home_url() . "/?page_id=13794";//数据查询链接

    $ajaxUrl = admin_url('admin-ajax.php');//ajax url


    $post_id = $_GET['post_id'];//课程ID

    $web_url = home_url();//网站链接


    $is_super_admin = (is_super_admin()) ? 1 : 0;


    if ($post_id) {

        $post_title = get_post($post_id)->post_title;//课程标题

        $course_audio_src = get_post_meta($post_id, "course_audio_src_two", true);//音频LRC地址


        /*
         * 老版本课程流程json
         * */


        $old_course_learning_stage_json = get_post_meta($post_id, "course_learning_stage", true);


        if ($old_course_learning_stage_json) {
            $old_course_learning_stage_json = urldecode($old_course_learning_stage_json);
        } else {
            $old_course_learning_stage_json = "[]";
        }

        // myDump($old_course_learning_stage_json);


        /*
         *
         * 课程流程 json 和草稿 json
         * */

        $course_learning_stage_json = get_post_meta($post_id, "course_learning_stage_json", true);//课程流程json

        if ($course_learning_stage_json) {
            $course_learning_stage_json = urldecode($course_learning_stage_json);//解码转换
            $rough_draft_course_learning_stage_json = $course_learning_stage_json;

        } else {

            $course_learning_stage_json = '[]';
            $rough_draft_course_learning_stage_json = '[]';

        }


        $course_card_base_json = get_post_meta($post_id, "course_card_base", true);//课程卡片库 json


        if ($course_card_base_json) {

            $course_card_base_json = urldecode($course_card_base_json);//解码后课程关卡 json

        } else {


            $course_card_base_json = '[
            {
            "stageType":"begin",
            "classifyType":"course",
            "timestamp":"",
            "headline":"老师过渡",
            "showtext_1":"Lesson x",
            "showtext_2":"xxxxxx",
            "cat_name":"课程名",
            "brand_name":"—哈密瓜英语—",
            "subhead":"","img":"https://xgn.shuimitao.online/wp-content/uploads/2019/01/qimen.jpg",
            "english_sentence":"",
            "chinese_sentence":"",
            "english_sentence_has_dot":"",
            "english_sentence_no_dot":"",
            "explain_1":"",
            "explain_2":"",
            "explain_3":"",
            "explain_4":"",
            "learn_word":"",
            "word_explain":"",
            "word_phonetic":"",
            "question_stem_1":"",
            "question_stem_2":"",
            "option_1":"",
            "option_2":"",
            "option_3":"",
            "right_option":"",
            "bindKnowledge":"",
            "review_go_num":0,
            "rich_text":""
            },
            {
            bindKnowledge: "",
            brand_name: "",
            cat_name: "",
            chinese_sentence: "",
            classifyType: "course",
            english_sentence: "",
            english_sentence_has_dot: "",
            english_sentence_no_dot: "",
            explain_1: "",
            explain_2: "",
            explain_3: "",
            explain_4: "",
            headline: "老师过渡",
            img: "https://xgn.shuimitao.online/wp-content/uploads/2020/03/kt1500500.png",
            learn_word: "",
            option_1: "",
            option_2: "",
            option_3: "",
            question_stem_1: "",
            question_stem_2: "",
            review_go_num: "0",
            rich_text: "",
            right_option: "",
            showtext_1: "",
            showtext_2: "放松心情，课程马上开始。",
            stageType: "begin",
            subhead: "",
            word_explain: "",
            word_phonetic: ""
            },
            {
            bindKnowledge: "",
            brand_name: "",
            cat_name: "",
            chinese_sentence: "",
            classifyType: "course",
            english_sentence: "",
            english_sentence_has_dot: "",
            english_sentence_no_dot: "",
            explain_1: "",
            explain_2: "",
            explain_3: "",
            explain_4: "",
            headline: "今日课程内容",
            img: "https://xgn.shuimitao.online/wp-content/uploads/2020/03/kt2.png",
            learn_word: "",
            option_1: "",
            option_2: "",
            option_3: "",
            question_stem_1: "",
            question_stem_2: "",
            review_go_num: "0",
            rich_text: "<p>这里讲今天课程内容</p>",
            right_option: "",
            showtext_1: "",
            showtext_2: "",
            stageType: "knowledgeHasImage",
            subhead: "",
            word_explain: "",
            word_phonetic: ""
            },

            {
            "stageType":"begin",
            "classifyType":"course",
            "timestamp":"",
            "headline":"老师过渡",
            "showtext_1":"",
            "showtext_2":"进入单词学习",
            "cat_name":"",
            "brand_name":"",
            "subhead":"",
            "img":"https://xgn.shuimitao.online/wp-content/uploads/2020/04/laoshi4.png",
            "english_sentence":"",
            "chinese_sentence":"",
            "english_sentence_has_dot":"",
            "english_sentence_no_dot":"",
            "explain_1":"",
            "explain_2":"",
            "explain_3":"",
            "explain_4":"",
            "learn_word":"",
            "word_explain":"",
            "word_phonetic":"",
            "question_stem_1":"",
            "question_stem_2":"",
            "option_1":"",
            "option_2":"",
            "option_3":"",
            "right_option":"",
            "bindKnowledge":"",
            "review_go_num":0,
            "rich_text":""
            },

            {"stageType":"knowledge",
            "classifyType":"course",
            "timestamp":"",
            "headline":"知识点",
            "showtext_1":"",
            "showtext_2":"",
            "cat_name":"",
            "brand_name":"",
            "subhead":"",
            "img":"",
            "english_sentence":"",
            "chinese_sentence":"",
            "english_sentence_has_dot":"",
            "english_sentence_no_dot":"",
            "explain_1":"",
            "explain_2":"",
            "explain_3":"",
            "explain_4":"",
            "learn_word":"",
            "word_explain":"",
            "word_phonetic":"",
            "question_stem_1":"",
            "question_stem_2":"",
            "option_1":"",
            "option_2":"",
            "option_3":"",
            "right_option":"",
            "bindKnowledge":"",
            "review_go_num":0,
            "rich_text":"<p>这是一条讲解</p>"
            },


              {"stageType":"word",
            "classifyType":"course",
            "timestamp":"",
            "headline":"单词讲解",
            "showtext_1":"",
            "showtext_2":"",
            "cat_name":"",
            "brand_name":"",
            "subhead":"",
            "img":"",
            "english_sentence":"",
            "chinese_sentence":"",
            "english_sentence_has_dot":"",
            "english_sentence_no_dot":"",
            "explain_1":"",
            "explain_2":"",
            "explain_3":"",
            "explain_4":"",
            "learn_word":"apple",
            "word_explain":"n.苹果",
            "word_phonetic":"æpl",
            "question_stem_1":"",
            "question_stem_2":"",
            "option_1":"",
            "option_2":"",
            "option_3":"",
            "right_option":"",
            "bindKnowledge":"",
            "review_go_num":0,
            "rich_text":"<p>这是一条讲解</p>"
            },

            {"stageType":"sentence",
            "classifyType":"course",
            "timestamp":"",
            "headline":"句型讲解",
            "showtext_1":"",
            "showtext_2":"",
            "cat_name":"",
            "brand_name":"",
            "subhead":"",
            "img":"",
            "english_sentence":"He is my son.",
            "chinese_sentence":"他是我的儿子。",
            "english_sentence_has_dot":"He is my son .",
            "english_sentence_no_dot":"He is my son",
            "explain_1":"",
            "explain_2":"",
            "explain_3":"",
            "explain_4":"",
            "learn_word":"",
            "word_explain":"",
            "word_phonetic":"",
            "question_stem_1":"",
            "question_stem_2":"",
            "option_1":"",
            "option_2":"",
            "option_3":"",
            "right_option":"",
            "bindKnowledge":"",
            "review_go_num":0,
            "rich_text":"<p>这是一条讲解</p>"
            }
            ]';
        }


        ?>


        <style>

            [v-cloak] {
                display: none;
            }

            #container {

            }

            .full_height {
                height: 100vh;
            }

            .max_wh {
                width: 100%;
                height: 100%;
                margin: 0;
                padding: 0;

            }

            .fl {
                float: left;
            }

            .col-center-block {
                float: none;
                display: block;
                margin-left: auto;
                margin-right: auto;
            }

            /*

            col-xs-* 针对移动屏幕 最大为12
            col-md-* 针对桌面 最大为12
            col-sm-* 针对平板 最大为12

            */

            .top_btn {
                width: 75%;
                height: 10vh;
                position: fixed;
                right: 0px;
                top: 0px;
                z-index: 90;
                background-color: white;
                border-bottom: 1px solid #e8e8e8;
            }

            .top_btn > button {
                margin: 5px 10px;
                float: right;
            }

            .bottom_player {

                width: 100%;

                left: 0px;

                position: fixed;
                height: 15vh;
                border-top: 1px solid #e8e8e8;
                background-color: white;

                bottom: 0px;
                z-index: 90;
            }

            .set_cover {

                width: 100%;

                left: 0px;

                position: fixed;
                height: 15vh;
                border-top: 1px solid darkgray;
                background-color: white;

                bottom: 0px;
                z-index: 90;
            }

            .set_cover div {
                height: 15vh;
                width: 100%;
                line-height: 15vh;
                text-align: center;
                color: darkgray;
            }

            .button_area {
                width: 10%;
                height: 15vh;
                position: absolute;
                top: 0;
                left: 0;
            }

            .button_area > div {
                width: 49%;
                float: left;
                height: 4vh;
                text-align: center;
                line-height: 4vh;

                color: orangered;
            }

            .button_area > div:first-child {
                font-size: 16px;
                font-weight: 500;
            }

            .button_area > div:nth-child(2) {
                width: 30%;
                font-size: 8px;
                color: white;
            }

            .button_area > div:nth-child(2) > span {
                background: orangered;

                padding: 2px 3px;
                border-radius: 3px;
            }

            .timebar_area {
                width: 75%;
                height: 5vh;
                position: absolute;
                top: 0;
                left: 10%;
            }

            .timebar_area > div:first-child {
                width: 100%;
                height: 6vh;
                float: left;
                position: relative;

            }

            .timebar_area > div:first-child > span:first-child {
                width: 100%;
                height: 0.3vh;
                position: absolute;
                background-color: orangered;
                top: 2vh;

            }

            .timebar_area > div:first-child > span:nth-child(2) {
                width: 2%;
                height: 2vh;
                border-radius: 1vh;
                background-color: white;
                top: 1.2vh;
                position: absolute;
                z-index: 91;
                border: 1px solid orangered;
                left: -1%;
            }

            .time_text {
                width: 15%;
                height: 16vh;
                text-align: center;
                font-size: 12px;
                line-height: 5vh;
                color: darkgray;
                position: absolute;
                top: 0;
                left: 85%;
            }

            .detail_area {
                width: 75%;
                height: 10vh;
                float: left;
                position: absolute;
                top: 5vh;
                left: 10%;

            }

            .detail_stage_course {
                width: 100%;
                height: 3vh;
                float: left;
                position: relative;
                margin-top: 1vh;
            }

            .detail_stage_course > span {

                width: 2px;
                height: 3vh;
                display: block;
                float: left;
                position: absolute;
                top: 0;

            }

            .oranged {
                background-color: orangered;
            }

            .blued {
                background-color: blue;
            }

            .greend {
                background-color: green;
            }

            .detail_stage_btn {
                width: 100%;
                height: 5vh;
                float: left;
                position: relative;
                margin-top: 2vh;
            }

            .detail_stage_btn button {
                width: 30%;
                height: 3vh;
                font-size: 10px;
                line-height: 3vh;
                text-align: center;
                border: 0;
                margin-left: 2.5%;
                float: left;
                background: #e8e8e8;
            }

            .left_preview {
                width: 25%;
                position: fixed;
                height: 85vh;
                background-color: white;
                left: 0px;
                top: 0px;
                z-index: 90;

                border-right: 1px solid #e8e8e8;
            }

            .review_top_btn {
                width: 100%;
                height: 12vh;
                float: left;
            }

            .review_top_btn div {

                width: 100%;
                text-align: center;
                height: 6vh;
                line-height: 6vh;
                font-size: 12px;

            }

            .review_top_btn button {
                width: 33%;
                height: 4vh;
                line-height: 4vh;
                text-align: center;

            }

            .review_bottom_btn {
                width: 100%;
                text-align: center;
                height: 4vh;
                line-height: 4vh;
                font-size: 12px;
            }

            .review_bottom_btn > div {
                width: 90%;
                margin-left: 5%;
                height: 4vh;
                margin-top: 10px;
                float: left;

            }

            .review_bottom_btn button {
                width: 33%;
                height: 4vh;
                line-height: 4vh;
                text-align: center;
            }




            .inner_screen {
                width: 90%;
                height: 60vh;
                position: relative;
                margin-left: 5%;
                float: left;
            }

            .right_edit {
                float: right;

                margin: 0;
                padding: 0;
            }

            .create_area {

                width: 90%;

                margin-top: 12vh;

                margin-left: 5%;
            }

            .add_new_card {
                width: 20%;
                height: 250px;
                float: left;

                margin-left: 4%;
                margin-top: 20px;

                border-radius: 10px;
                position: relative;
                overflow: hidden;
            }

            .add_new_card span {
                font-size: 36px;

                text-align: center;
                line-height: 250px;
                display: block;
            }

            .single_card {
                width: 20%;
                height: 200px;
                float: left;
                margin-left: 4%;
                margin-top: 20px;
                border-radius: 10px;
                position: relative;

                border: 1px solid #e8e8e8;

            }

            .has_set_area {
                width: 100%;

                background: #F1F3F4;
                height: auto;
            }

            .has_set_inner {
                width: 96%;
                background: #F1F3F4;
                float: left;
                margin-left: 2%;
                padding-bottom: 20px;
            }

            .replace_audio {

                background: #F1F3F4;
            }

            .card {
                width: 20%;
                height: 250px;
                float: left;

                margin-left: 4%;
                margin-top: 20px;
                background: white;
                border-radius: 10px;
                position: relative;
                overflow: hidden;
            }

            .card_top_btn {
                width: 100%;
                position: absolute;
                top: 0px;
                left: 0px;
                height: 30px;
                background-color: black;
                opacity: 0.3;
                z-index: 2;
                color: white;

                font-size: 12px;
            }

            .card_button_btn {
                width: 100%;
                position: absolute;
                bottom: 0px;
                left: 0px;
                height: 30px;
                background-color: black;
                opacity: 0.3;
                z-index: 2;
                color: white;
                font-size: 12px;
            }

            .card_top_btn > div, .card_button_btn > div {
                width: 25%;
                height: 30px;
                line-height: 30px;
                text-align: center;
                float: left;
                font-size: 22px;
            }

            .card_top_btn > div:nth-child(2) {
                font-size: 12px;
                width: 50%;
            }

            .card_top_btn > button, .card_button_btn > button {
                width: 25%;
                height: 30px;
                line-height: 30px;
                text-align: center;
                float: right;
                background-color: black;
                color: white;
                border: 0px;
            }

            .course_card_1 {
                width: 92%;
                height: 92%;
                left: 4%;
                top: 4%;
                border-radius: 10px;
                box-shadow: 0px 0px 5px lightgrey;
                border: 1px solid #e8e8e8;
                position: absolute;
                background: white;
            }

            .course_card_1 > img {
                width: 60%;
                margin: 20% auto;
                display: block;
            }

            .course_card_1_body {
                width: 100%;
                background-color: white;
                border-top-left-radius: 10px;
                border-top-right-radius: 10px;
                float: left;
                position: relative;
                max-height: 70%;
                overflow: hidden;

            }

            .course_card_1_body_trademark {
                width: 60%;
                color: white;
                font-size: 12px;
                font-family: "黑体";
                font-weight: 500;
                text-align: center;
                position: absolute;
                top: 10%;
                left: 20%;
                max-height: 10%;

            }

            .course_card_1_body_title {
                width: 80%;
                color: white;
                font-size: 14px;
                font-family: "黑体";
                font-weight: 600;
                text-align: center;
                position: absolute;
                top: 20%;
                left: 10%;
            }

            .course_card_1_body > img {
                width: 100%;

                border-top-left-radius: 10px;
                border-top-right-radius: 10px;
            }

            .course_card_1_foot {
                width: 100%;
                height: 30%;
                background-color: white;
                border-bottom-left-radius: 10px;
                border-bottom-right-radius: 10px;
                float: left;

            }

            .course_card_1_foot > p {
                width: 90%;
                margin-left: 5%;
                text-align: center;
                font-size: 0.9em;
                color: gray;

                margin-top: 3%;

            }

            .course_card_1_foot > p:first-child {

                margin-top: 5%;
                color: #364B9A;
                font-size: 1.1em;
            }

            .course_card_2 {
                width: 92%;
                height: 92%;
                left: 4%;
                top: 4%;
                position: absolute;
            }

            .course_card_2_top {
                width: 100%;
                height: 28%;
                background: url(/img/newcourse/<EMAIL>);
                background-size: 100% 100%;
                background-repeat: no-repeat;
                position: relative;
                /*background-color: white;

                border-bottom-left-radius: 20px;
                border-bottom-right-radius: 20px;
                float: left;*/
            }

            .course_card_2_top div {
                width: 80%;
                height: 60%;
                top: 25%;
                left: 10%;
                display: block;
                text-align: center;
                font-size: 18px;
                position: absolute;
                line-height: 250%;
                color: #364B9A;
                font-weight: 400;
            }

            .course_card_2_body {
                width: 100%;
                height: 71%;
                background: url(/img/newcourse/<EMAIL>);
                background-size: 100% 100%;
                background-repeat: no-repeat;
                position: relative;
                text-align: justify;
                word-wrap: break-word !important;
                word-break: break-all;
                white-space: normal;
                overflow: hidden;
                text-overflow: ellipsis;

            }

            .course_card_2_body > div:first-child {
                width: 100%;
                margin-top: 7%;
                float: left;
                font-size: 8px;
                color: #364B9A;

            }

            .course_card_2_body > div:nth-child(2) {
                width: 100%;
                margin-top: 5%;
                float: left;
                font-size: 8px;

            }

            .course_card_2_body div p {
                width: 86%;
                margin-left: 7%;
                margin-top: 2%;

            }

            .course_card_2_body .tinyp p {
                width: 86%;
                margin-left: 5%;
                margin-top: 1%;
                font-size: 8px;
            }

            .course_card_3 {

                width: 92%;
                height: 92%;
                left: 4%;
                top: 4%;
                border-radius: 20px;
                box-shadow: 0px 0px 5px lightgrey;
                border: 1px solid #e8e8e8;
                position: absolute;
                background: white;

            }

            .course_card_3_img {

                width: 100%;
                max-height: 50%;

                background-color: white;

                border-top-left-radius: 20px;
                border-top-right-radius: 20px;
                float: left;
                position: relative;
                overflow: hidden;

            }

            .course_card_3_img > img {
                width: 100%;

                border-top-left-radius: 20px;
                border-top-right-radius: 20px;
            }

            .course_card_3_body {
                width: 90%;
                float: left;
                max-height: 45%;
                margin-left: 5%;
                margin-top: 2%;

            }

            .course_card_3_head {
                width: 90%;
                margin-left: 2%;
                color: #00b9eb;
                font-size: 1.1em;
                font-weight: 600;
                border-bottom: 1px solid #e8e8e8;
                padding-top: 2%;
                padding-bottom: 2%;
            }

            .course_card_3_explain {
                font-size: 0.9em;
            }

            .course_card_3_explain p {
                margin-top: 2%;
                color: gray;

            }

            .course_card_4 {
                width: 92%;
                height: 92%;
                left: 4%;
                top: 4%;
                border-radius: 20px;
                box-shadow: 0px 0px 5px lightgrey;
                border: 1px solid #e8e8e8;
                position: absolute;
                background: white;
            }

            .course_card_4_body {
                width: 100%;
                height: 60%;
                float: left;
            }

            .course_card_4_body p {
                width: 100%;
                text-align: center;
                float: left;
            }

            .course_card_4_body p:first-child {
                height: 40px;
                line-height: 40px;
                font-size: 16px;
                text-align: center;
                font-weight: bold;
                max-height: 30%;
                margin-top: 20%;;
            }

            .course_card_4_body p:nth-child(3) {
                height: 50px;
                line-height: 50px;
                font-size: 12px;
                text-align: center;
                max-height: 30%;
                color: #8ACAE3;
                font-style: italic;

            }

            .course_card_4_body p:nth-child(2) {
                height: 22px;
                line-height: 22px;
                font-size: 12px;
                text-align: center;
                max-height: 30%;
                color: darkgrey;

            }

            .course_card_4_bottom {
                width: 100%;
                height: 40%;
                float: left;
                overflow: hidden;

            }

            .course_card_4_bottom p {
                width: 80%;
                height: 20px;
                line-height: 20px;
                font-size: 0.8em;
                color: gray;
                margin-top: 20px;
                margin-left: 10%;
                text-align: justify;
                word-wrap: break-word !important;
                word-break: break-all;
                white-space: normal;
            }

            .exePage {
                width: 100%;
                height: 100%;
                position: absolute;
                background-color: #364B9A;
            }

            .exePage div {

                width: 90%;
                margin-left: 5%;
                font-size: 12px;

                color: white;
                text-align: center;
            }

            .exePage div:first-child {
                margin-top: 20px;
                font-weight: 400;
                margin-bottom: 10px;
                font-size: 14px;
            }

            .exePage_in_screen {
                width: 100%;
                height: 100%;
                position: absolute;
                background-color: #364B9A;
            }

            .exePage_in_screen > div {

                width: 90%;
                margin-left: 5%;
                font-size: 14px;

                color: white;
                text-align: center;
            }

            .exePage_in_screen > div:first-child {
                margin-top: 20px;
                margin-bottom: 10px;
                font-size: 16px;
                font-weight: 600;
            }

            .exePage_in_screen > span {
                display: block;
                width: 90%;
                margin-top: 5px;
                margin-bottom: 10px;
                font-size: 10px;
                font-weight: 200;
                color: white;
                text-align: center;
                margin-left: 5%;
            }

            .popLayer1 {
                position: absolute;
                left: 0;
                top: 0;
                z-index: 91;
                background: black;
                -moz-opacity: 0.8;
                opacity: .80;
                filter: alpha(opacity=80); /* 只支持IE6、7、8、9 */
            }

            .popBox1 {
                position: absolute;
                z-index: 92;
                background: white;
                top: 5%;
                left: 5%;
            }

            .popLayer2 {
                position: absolute;
                left: 0;
                top: 0;
                z-index: 93;
                background: black;
                -moz-opacity: 0.8;
                opacity: .80;
                filter: alpha(opacity=80); /* 只支持IE6、7、8、9 */
            }

            .popBox2 {
                position: absolute;
                z-index: 94;
                background: white;
                top: 10%;
                left: 10%;
            }

            .close_btn {
                width: 100%;
                height: 50px;
                float: left;

            }

            .close_btn span {
                display: block;
                float: right;
                width: 50px;
                height: 50px;
                line-height: 50px;
                text-align: center;
                font-size: 22px;
                color: darkgray;
            }

            .content_area1, .content_area2 {

                width: 100%;
                float: left;
                overflow-y: scroll;

            }

            .create_model {
                width: 100%;
                height: 100%;
                float: left;
            }

            .create_model_nav {
                width: 20%;
                height: auto;
                float: left;
                margin-left: 2%;
            }

            .create_model_form {
                width: 70%;
                height: auto;
                float: left;
                margin-left: 3%;
            }

            .create_model_form > div > div > span {
                font-size: 12px;

            }

            .create_model_form > div > div > button {
                font-size: 12px;
                padding: 1px 3px;
                margin-bottom: 5px;

            }

            .form_btn {
                width: 100%;
                height: 50px;
            }

            .form_btn button {
                width: 100px;
                margin: 0px auto;
                display: block;
            }

            .rich_editor {
                width: 100%;
                height: 200px;
                float: left;
                overflow: scroll;
                background: #e8e8e8e8;
            }

            .fade-enter-active, .fade-leave-active {
                transition: opacity .5s;
            }

            .fade-enter, .fade-leave-to /* .fade-leave-active below version 2.1.8 */
            {
                opacity: 0;
            }

            .choose_stage {
                width: 20%;
                height: 200px;
                float: left;
                margin-left: 4%;
                margin-top: 20px;
                background: white;
                border-radius: 10px;
                position: relative;
                overflow: hidden;
            }

            .choose_stage_inner {
                width: 92%;
                height: 92%;
                left: 4%;
                top: 4%;
                position: absolute;

            }

            .choosed {
                border: 1px solid blue;
            }

            .choose_stage_inner_top {
                width: 100%;
                height: 28%;
                background: url(/img/newcourse/<EMAIL>);
                background-size: 100% 100%;
                background-repeat: no-repeat;
                position: relative;
            }

            .choose_stage_inner_top div {
                width: 80%;
                height: 60%;
                top: 25%;
                left: 10%;
                display: block;
                text-align: center;
                /* font-size: 12px; */
                position: absolute;
                line-height: 250%;
                color: #364B9A;
                font-weight: 400;
                font-size: 0.9em;

            }

            .choose_stage_inner_body {
                width: 100%;
                height: 71%;
                background: url(/img/newcourse/<EMAIL>);
                background-size: 100% 100%;
                background-repeat: no-repeat;
                position: relative;
                text-align: justify;
                word-wrap: break-word !important;
                word-break: break-all;
                white-space: normal;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .choose_stage_inner_body > div:first-child {
                width: 100%;
                margin-top: 7%;
                float: left;
                font-size: 8px;
                color: darkgray;
                text-align: center;
            }

            .choose_stage_inner_body div p {
                width: 86%;
                margin-left: 7%;
                margin-top: 2%;
            }


        </style>


        <div id="container" class="container-fluid full_height">

            <!--导入音频开始-->
            <div class="row max_wh fl" v-show="!course_audio_src" v-cloak>


                <button type="button" @click.stop="upload_audio"
                        class="btn btn-primary btn-lg col-xs-2 col-md-2 col-sm-2 col-center-block"
                        style="margin-top: 20%">导入音频
                </button>


            </div>
            <!--导入音频结束-->


            <!--音频编辑开始-->
            <div class="row max_wh fl" v-show="course_audio_src" v-cloak>


                <!--底部播放器开始-->
                <div class="bottom_player " v-if="rough_draft_course_learning_stage_json.length>0" v-cloak>


                    <!--播放按钮区域-->
                    <div class="button_area">

                        <div>
                            <span class="glyphicon glyphicon-play" v-show="!is_course_audio_playing"
                                  @click.stop="audio_play" aria-hidden="true"></span>
                            <span class="glyphicon glyphicon-pause" v-show="is_course_audio_playing"
                                  @click.stop="audio_pause" aria-hidden="true"></span>
                        </div>


                        <div @click.stop="change_audio_playbackspeed">
                            <span>{{course_audio_playbackspeed}}</span>
                        </div>


                    </div>
                    <!--播放按钮区域-->


                    <!--进度条-->
                    <div class="timebar_area">
                        <div @click.stop='timebar_progress_click($event)'>
                            <span></span>
                            <span id="timebar_bar"></span>

                        </div>
                    </div>
                    <!--进度条-->


                    <!--进度条下关卡显示-->
                    <div class="detail_area">

                        <div class="detail_stage_course">


                            <template v-for="(single_stage_json,key) in rough_draft_course_learning_stage_json">

                                <span
                                    :class="{
                                    oranged:single_stage_json.classifyType=='course',
                                    blued:single_stage_json.classifyType=='exercise',
                                    greend:single_stage_json.classifyType=='exercise'&&!single_stage_json.bindKnowledge
                                    }"
                                    :id="key"
                                    :style="{ left: stage_percent_in_course_process(single_stage_json,key)}"></span>

                            </template>

                        </div>

                        <div class="detail_stage_btn">

                            <button @click.stop="course_audio.currentTime=0;pageindex=0">
                                重听
                            </button>

                            <button @click.stop="back_2_s">
                                倒退2秒
                            </button>


                            <button @click.stop="forward_2_s">
                                前进2秒
                            </button>

                        </div>


                    </div>
                    <!--进度条下关卡显示-->


                    <!--时间显示-->
                    <div class="time_text">{{audio_current_time}}/{{audio_total_time}}</div>
                    <!--时间显示-->


                </div>
                <!--底部播放器结束-->

                <div class="set_cover" v-else v-cloak>
                    <div>请先设置封面</div>
                </div>


                <!--左边预览开始-->
                <div class="left_preview" v-cloak>


                    <!--顶部按钮-->

                    <div class="review_top_btn">
                        <div>关卡序号：{{pageindex}}</div>


                        <div>
                            <button @click.stop="go_last_stage">后退</button>

                            <button @click.stop="go_next_stage">前进</button>
                        </div>


                    </div>
                    <!--顶部按钮-->


                    <!--预览内容-->
                    <div class="inner_screen" @click.stop="edit_stage"
                         v-show="rough_draft_course_learning_stage_json.length>0">


                        <!--blank-->
                        <div v-show="now_stage_json.stageType=='blank'">
                        </div>
                        <!--blank-->


                        <!--begin-->
                        <div class="course_card_1" v-show="now_stage_json.stageType=='begin'">
                            <div class="course_card_1_body">
                                <!--<img src="https://www.joco15.com/wp-content/uploads/2018/09/timg.jpg">-->

                                <img :src="now_stage_json.img">

                                <div v-if="now_stage_json.brand_name" class="course_card_1_body_trademark">
                                    {{now_stage_json.brand_name}}
                                </div>

                                <div v-if="now_stage_json.cat_name" class="course_card_1_body_title">
                                    {{now_stage_json.cat_name}}
                                </div>

                            </div>
                            <div class="course_card_1_foot">
                                <p>{{now_stage_json.showtext_1}}</p>

                                <p>{{now_stage_json.showtext_2}}</p>
                            </div>
                        </div>
                        <!--begin-->

                        <!--sentence-->
                        <div class="course_card_2" v-show="now_stage_json.stageType=='sentence'">

                            <div class="course_card_2_top">
                                <div>{{now_stage_json.headline}}</div>
                            </div>
                            <div class="course_card_2_body">
                                <div>
                                    <p>{{now_stage_json.english_sentence}}</p>

                                    <p>{{now_stage_json.chinese_sentence}}</p>
                                </div>
                                <div v-html='now_stage_json.rich_text'>

                                </div>


                            </div>

                        </div>
                        <!--sentence-->

                        <!--word-->
                        <div class="course_card_4" v-show="now_stage_json.stageType=='word'">


                            <div class="course_card_4_body">
                                <p>{{now_stage_json.learn_word}}</p>

                                <p>/{{now_stage_json.word_phonetic}}/</p>

                                <p>{{now_stage_json.word_explain}}</p>

                            </div>
                            <div class="course_card_4_bottom" v-html='now_stage_json.rich_text'>

                            </div>


                        </div>
                        <!--word-->

                        <!--knowledgeHasImage-->
                        <div class="course_card_3" v-show="now_stage_json.stageType=='knowledgeHasImage'">

                            <div class="course_card_3_img">
                                <img :src="now_stage_json.img">
                            </div>

                            <div class="course_card_3_body">
                                <div class="course_card_3_head">{{now_stage_json.headline}}</div>

                                <div class="course_card_3_explain" v-html='now_stage_json.rich_text'>

                                </div>
                            </div>

                        </div>
                        <!--knowledgeHasImage-->

                        <!--knowledge-->
                        <div class="course_card_2" v-show="now_stage_json.stageType=='knowledge'">

                            <div class="course_card_2_top">
                                <div>{{now_stage_json.headline}}</div>
                            </div>
                            <div class="course_card_2_body">
                                <div>
                                    <p></p>

                                    <p></p>
                                </div>
                                <div v-html='now_stage_json.rich_text'>

                                </div>


                            </div>

                        </div>
                        <!--knowledge-->

                        <!--listen-->
                        <div class="course_card_1" v-show="now_stage_json.stageType=='listen'">
                            <img src="<?php home_url(); ?>/img/laba.gif">

                        </div>
                        <!--listen-->

                        <!--lack-->
                        <div class="exePage_in_screen" v-show="now_stage_json.stageType=='lack'">
                            <div>
                                缺词填空
                            </div>
                            <span v-if="now_stage_json.bindKnowledge">(已绑知识点)</span>

                            <div>
                                {{now_stage_json.english_sentence}}
                            </div>
                            <div>
                                {{now_stage_json.chinese_sentence}}
                            </div>
                        </div>
                        <!--lack-->

                        <!--spell-->
                        <div class="exePage_in_screen" v-show="now_stage_json.stageType=='spell'">
                            <div>
                                单词拼写
                            </div>
                            <span v-if="now_stage_json.bindKnowledge">(已绑知识点)</span>

                            <div>
                                {{now_stage_json.learn_word}}
                            </div>
                            <div>
                                {{now_stage_json.word_explain}}
                            </div>
                        </div>
                        <!--spell-->

                        <!--choice-->
                        <div class="exePage_in_screen" v-show="now_stage_json.stageType=='choice'">
                            <div>
                                选择题
                            </div>
                            <span v-if="now_stage_json.bindKnowledge">(已绑知识点)</span>

                            <div>
                                {{now_stage_json.question_stem_1}}
                            </div>
                            <div>
                                {{now_stage_json.question_stem_2}}
                            </div>
                            <div>a.{{now_stage_json.option_1}}</div>
                            <div>b.{{now_stage_json.option_2}}</div>
                            <div v-if="now_stage_json.option_3">c.{{now_stage_json.option_3}}</div>

                        </div>
                        <!--choice-->

                        <!--wordchoice-->
                        <div class="exePage_in_screen" v-show="now_stage_json.stageType=='wordchoice'">
                            <div>
                                随机单词题
                            </div>
                            <span v-if="now_stage_json.bindKnowledge">(已绑知识点)</span>

                            <div>
                                {{now_stage_json.learn_word}}
                            </div>
                            <div>
                                {{now_stage_json. word_explain}}
                            </div>
                        </div>
                        <!--wordchoice-->

                        <!--voice-->
                        <div class="exePage_in_screen" v-show="now_stage_json.stageType=='voice'">

                            <div>
                                语音练习
                            </div>
                            <span v-if="now_stage_json.bindKnowledge">(已绑知识点)</span>

                            <div>
                                {{now_stage_json.question_stem_1}}
                            </div>
                            <div>
                                {{now_stage_json.question_stem_2}}
                            </div>
                        </div>
                        <!--voice-->


                    </div>
                    <!--预览内容-->


                    <!--无内容状态-->
                    <div class="inner_screen" v-show="rough_draft_course_learning_stage_json.length==0">
                        <div class="course_card_1">
                            <div class="course_card_1_body">


                                <img src="https://xgn.shuimitao.online/wp-content/uploads/2019/03/yy-1.jpg">


                                <div class="course_card_1_body_trademark"></div>

                                <div class="course_card_1_body_title"></div>

                            </div>
                            <div class="course_card_1_foot">
                                <p>暂无内容</p>


                            </div>
                        </div>

                    </div>
                    <!--无内容状态-->


                    <!--删除按钮-->
                    <div class="review_bottom_btn">

                        <div>
                            <button style="" @click.stop="detele_stage"
                                    v-show="rough_draft_course_learning_stage_json.length>0&&pageindex!=0">删除此关卡
                            </button>
                            <button @click.stop="detele_stage"
                                    v-show="rough_draft_course_learning_stage_json.length==1">
                                删除封面
                            </button>


                        </div>

                        <div>


                            <button @click.stop="copy_stage_to_base"
                                    v-show="rough_draft_course_learning_stage_json.length>0">导关卡入库
                            </button>


                            <button @click.stop="clear_stage"
                                    v-show="rough_draft_course_learning_stage_json.length>0">清空关卡
                            </button>

                        </div>


                    </div>
                    <!--删除按钮-->


                </div>
                <!--左边预览结束-->


                <!--顶部按钮-->
                <div class="top_btn" v-cloak>


                    <button type="button" class="btn btn-lg btn-primary" @click.stop="go_replace_audio">替换音频</button>
                    <button type="button" class="btn btn-lg btn-primary" v-show="!is_rough_draft_changed"
                            disabled="disabled">保存关卡数据
                    </button>

                    <button type="button" class="btn btn-lg btn-primary" v-show="is_rough_draft_changed"
                            @click.stop="save_to_course_learning_stage_json">保存关卡数据
                    </button>


                    <button type="button" class="btn btn-lg btn-primary" @click.stop="old_to_rough_draft">老版本转为关卡
                    </button>


                    <button type="button" @click.stop="import_rough_draft_course_learning_stage_json"
                            class="btn btn-lg btn-primary">导出关卡json
                    </button>


                    <button type="button" @click.stop="push_data" class="btn btn-lg btn-primary"
                            v-if="is_super_admin==1">推送数据
                    </button>


                </div>
                <!--顶部按钮-->


                <!--右侧编辑-->
                <div class="right_edit col-xs-9 col-md-9 col-sm-9" v-cloak>


                    <!--卡片库-->
                    <div class="create_area">

                        <div
                            style="width: 100%;height: 8vh;line-height: 8vh;text-align: center;font-size: 30px;color: darkgray; font-weight: 500;">
                            <span>卡片库</span>
                            <button
                                style="height: 4vh; font-size: 12px;line-height: 3vh;width: 100px;text-align: center;background: white;color: darkgray;border: 1px solid;"
                                @click.stop="clear_course_card_base_json"
                                v-show="course_card_base_json.length>0">清空卡片库
                            </button>
                        </div>


                        <template v-for="(single_card,key) in course_card_base_json">


                            <div class="card" @click.stop="edit_card(key)" @mouseover.prevent="move_to_card(key)"
                                 @mouseout.prevent="on_card='null'">


                                <div class="card_top_btn" v-show="key==on_card">
                                    <button v-if="single_card.stageType=='word'||single_card.stageType=='sentence'"
                                            @click.stop="quick_create_exercise(single_card,key)">创题
                                    </button>
                                    <button @click.stop="delete_single_card(key)">删除</button>
                                    <button @click.stop="copy_and_add_single_card(key)">复制</button>

                                    <template v-if="rough_draft_course_learning_stage_json.length>0">
                                        <button @click.stop="insert_single_card(single_card)">插入</button>
                                    </template>


                                    <template
                                        v-if="rough_draft_course_learning_stage_json.length==0&&single_card.stageType=='begin'">

                                        <button @click.stop="insert_single_card(single_card)">设封</button>

                                    </template>

                                </div>


                                <!--begin-->
                                <div class="course_card_1" v-if="single_card.stageType=='begin'">
                                    <div class="course_card_1_body">
                                        <!--<img src="https://www.joco15.com/wp-content/uploads/2018/09/timg.jpg">-->

                                        <img :src="single_card.img">

                                        <div v-if="single_card.brand_name" class="course_card_1_body_trademark">
                                            {{single_card.brand_name}}
                                        </div>

                                        <div v-if="single_card.cat_name" class="course_card_1_body_title">
                                            {{single_card.cat_name}}
                                        </div>


                                    </div>
                                    <div class="course_card_1_foot">
                                        <p style="font-size: 10px">{{single_card.showtext_1}}</p>

                                        <p style="font-size: 10px;margin-top: 0%">{{single_card.showtext_2}}</p>
                                    </div>
                                </div>
                                <!--begin-->


                                <!--sentence-->
                                <div class="course_card_2" v-if="single_card.stageType=='sentence'">

                                    <div class="course_card_2_top">
                                        <div style="line-height: 200%;font-size: 16px">{{single_card.headline}}</div>
                                    </div>
                                    <div class="course_card_2_body">
                                        <div class="tinyp">
                                            <p>{{single_card.english_sentence}}</p>

                                            <p>{{single_card.chinese_sentence}}</p>
                                        </div>
                                        <div class="tinyp" v-html='single_card.rich_text'>

                                        </div>


                                    </div>

                                </div>
                                <!--sentence-->


                                <!--word-->
                                <div class="course_card_4" v-if="single_card.stageType=='word'">


                                    <div class="course_card_4_body">
                                        <p>{{single_card.learn_word}}</p>

                                        <p>/{{single_card.word_phonetic}}/</p>

                                        <p>{{single_card.word_explain}}</p>

                                    </div>
                                    <div class="course_card_4_bottom" v-html='single_card.rich_text'>

                                    </div>

                                </div>
                                <!--word-->


                                <!--knowledgeHasImage-->
                                <div class="course_card_3" v-if="single_card.stageType=='knowledgeHasImage'">

                                    <div class="course_card_3_img">
                                        <img :src="single_card.img">
                                    </div>

                                    <div class="course_card_3_body">
                                        <div style="font-size: 14px" class="course_card_3_head">
                                            {{single_card.headline}}
                                        </div>
                                        <div style="font-size: 12px" class="course_card_3_explain"
                                             v-html='single_card.rich_text'>
                                        </div>
                                    </div>

                                </div>
                                <!--knowledgeHasImage-->

                                <!--knowledge-->
                                <div class="course_card_2" v-if="single_card.stageType=='knowledge'">

                                    <div class="course_card_2_top">
                                        <div style="line-height: 200%;font-size: 16px">{{single_card.headline}}</div>
                                    </div>
                                    <div class="course_card_2_body">
                                        <div class="tinyp">
                                            <p></p>

                                            <p></p>
                                        </div>
                                        <div class="tinyp" v-html='single_card.rich_text'>

                                        </div>


                                    </div>

                                </div>
                                <!--knowledge-->


                                <!--lack-->
                                <div class="exePage" v-show="single_card.stageType=='lack'">


                                    <div>
                                        缺词填空
                                    </div>

                                    <div>
                                        {{single_card.english_sentence}}
                                    </div>
                                    <div>
                                        {{single_card.chinese_sentence}}
                                    </div>


                                </div>
                                <!--lack-->

                                <!--spell-->
                                <div class="exePage" v-show="single_card.stageType=='spell'">
                                    <div>
                                        单词拼写
                                    </div>

                                    <div>
                                        {{single_card.learn_word}}
                                    </div>
                                    <div>
                                        {{single_card.word_explain}}
                                    </div>
                                </div>
                                <!--spell-->

                                <!--choice-->
                                <div class="exePage" v-show="single_card.stageType=='choice'">
                                    <div>
                                        选择题
                                    </div>
                                    <div>
                                        {{single_card.question_stem_1}}
                                    </div>
                                    <div>
                                        {{single_card.question_stem_2}}
                                    </div>
                                </div>
                                <!--choice-->


                                <!--wordchoice-->
                                <div class="exePage" v-show="single_card.stageType=='wordchoice'">
                                    <div>
                                        随机单词题
                                    </div>

                                    <div>
                                        {{single_card.learn_word}}
                                    </div>
                                    <div>
                                        {{single_card. word_explain}}
                                    </div>
                                </div>
                                <!--wordchoice-->


                                <!--voice-->
                                <div class="exePage" v-show="single_card.stageType=='voice'">

                                    <div>
                                        语音练习
                                    </div>
                                    <div>
                                        {{single_card.question_stem_1}}
                                    </div>
                                    <div>
                                        {{single_card.question_stem_2}}
                                    </div>
                                </div>
                                <!--voice-->


                            </div>

                        </template>


                        <div class="add_new_card" @click.stop="go_create_card">


                            <span class="glyphicon glyphicon-plus" aria-hidden="true"></span>

                        </div>


                    </div>
                    <!--卡片库-->


                </div>
                <!--右侧编辑-->

                <div class="bottom_dis col-xs-12 col-md-12 col-sm-12"
                     style="height: 300px;background-color: white" v-cloak></div>


            </div>
            <!--音频编辑结束-->


            <!--对话框内部内容1-->
            <div class="popBox1" v-show="is_open_popBox1" v-cloak>
                <div class="close_btn">
                    <span @click.stop="close_popBox1" class="glyphicon glyphicon-remove" aria-hidden="true"></span>
                </div>


                <!--新建或编辑卡片-->
                <div class="content_area1">


                    <!--创建与编辑卡片-->
                    <div class="create_model"
                         v-show="box1_model=='CREATE_CARD'||box1_model=='EDIT_CARD'||box1_model=='EDIT_STAGE'">

                        <!--导航-->
                        <div class="create_model_nav" v-show="box1_model=='CREATE_CARD'">
                            <ul class="nav nav-pills nav-stacked">
                                <template v-for="(single_nav,key) in create_nav_j">
                                    <li @click.stop="choose_card_type(single_nav,key)"
                                        :class="{ active: stageType==key }"><a href="#">{{single_nav.name}}</a></li>
                                </template>
                            </ul>
                        </div>
                        <!--导航-->


                        <!--表单-->
                        <div class="create_model_form">

                            <!--序号-->
                            <div v-show="box1_model=='EDIT_CARD'">

                                <div class="form-group">
                                    <span>卡片号：</span>
                                    <span>{{current_edit_card_key}}</span>
                                </div>
                            </div>
                            <!--序号-->


                            <!--关卡号-->
                            <div v-show="box1_model=='EDIT_STAGE'">

                                <div class="form-group">
                                    <span>关卡号：</span>
                                    <span>{{pageindex}}</span>
                                </div>
                            </div>
                            <!--关卡号-->


                            <!--标题文字-->
                            <div v-show="stageType=='knowledge'||stageType=='knowledgeHasImage'">

                                <div class="form-group">
                                    <span>标题文字：</span>
                                    <input class="form-control" type="text" v-model="headline"/>
                                </div>

                            </div>
                            <!--标题结束-->


                            <!--图片-->
                            <div v-show="stageType=='begin'||stageType=='knowledgeHasImage'">

                                <div class="form-group">
                                    <span>图片：</span>
                                    <button @click.stop="upload_img">上传图片</button>
                                    <input class="form-control" type="text" v-model="img"/>

                                </div>

                            </div>
                            <!--图片结束-->


                            <!--显示文字1 2 课程名 品牌名-->
                            <div v-show="stageType=='begin'">
                                <div class="form-group">
                                    <span>课程名称：</span>
                                    <input class="form-control" type="text" v-model="cat_name"/>
                                </div>

                                <div class="form-group">
                                    <span>品牌名：</span>
                                    <input class="form-control" type="text" v-model="brand_name"/>
                                </div>

                                <div class="form-group">
                                    <span>显示文字1：</span>
                                    <input class="form-control" type="text" v-model="showtext_1"/>
                                </div>

                                <div class="form-group">
                                    <span>显示文字2：</span>
                                    <input class="form-control" type="text" v-model="showtext_2"/>
                                </div>


                            </div>
                            <!--显示文字1 2 课程名 品牌名-->


                            <!--单词与释义-->
                            <div v-show="stageType=='word'||stageType=='spell'||stageType=='wordchoice'">

                                <div class="form-group">
                                    <span>单词：</span>
                                    <button @click.stop="search_word_text">搜索词典</button>
                                    <input class="form-control" type="text" v-model="learn_word"/>

                                </div>

                                <div class="form-group">
                                    <span>音标：</span>
                                    <input class="form-control" type="text" v-model="word_phonetic"/>
                                </div>

                                <div class="form-group">
                                    <span>释义：</span>
                                    <input class="form-control" type="text" v-model="word_explain"/>
                                </div>
                            </div>
                            <!--单词与释义结束-->


                            <!--中英文例句-->
                            <div
                                v-show="stageType=='sentence'||stageType=='lack'||stageType=='word'||stageType=='spell'||stageType=='wordchoice'">


                                <div class="form-group">
                                    <span>例句英文：</span>

                                    <input class="form-control" type="text" v-model="english_sentence"/>
                                </div>


                                <div class="form-group">
                                    <span>例句中文：</span>
                                    <input class="form-control" type="text" v-model="chinese_sentence"/>

                                </div>

                                <div class="form-group">
                                    <span>英文（有标点）：</span>
                                    <input disabled="disabled" class="form-control" type="text"
                                           v-model="english_sentence_has_dot"/>

                                </div>

                                <div class="form-group">
                                    <span>英文（无标点）：</span>
                                    <input disabled="disabled" class="form-control" type="text"
                                           v-model="english_sentence_no_dot"/>

                                </div>


                            </div>
                            <!--中英文例句结束-->


                            <!--讲解块-->
                            <div
                                v-show="stageType=='sentence'||stageType=='knowledge'||stageType=='knowledgeHasImage'||stageType=='word'">


                                <div class="form-group">
                                    <span>讲解1：</span>
                                    <input class="form-control" type="text" v-model="explain_1"/>

                                </div>

                                <div class="form-group">
                                    <span>讲解2：</span>
                                    <input class="form-control" type="text" v-model="explain_2"/>

                                </div>

                                <div class="form-group">
                                    <span>讲解3：</span>
                                    <input class="form-control" type="text" v-model="explain_3"/>
                                </div>

                                <div class="form-group">
                                    <span>讲解4：</span>
                                    <input class="form-control" type="text" v-model="explain_4"/>
                                </div>

                            </div>
                            <!--讲解块结束-->

                            <!--题干开始-->
                            <div v-show="stageType=='choice'||stageType=='voice'">

                                <div class="form-group">
                                    <span>题干1：</span>
                                    <input class="form-control" type="text"
                                           v-model="question_stem_1"/>

                                </div>

                                <div class="form-group">
                                    <span>题干2：</span>
                                    <input class="form-control" type="text"
                                           v-model="question_stem_2"/>

                                </div>
                            </div>
                            <!--题干结束-->

                            <!--富文本-->
                            <div
                                v-show="stageType=='knowledge'||stageType=='knowledgeHasImage'||stageType=='sentence'||stageType=='word'">


                                <div class="form-group">
                                    <span>富文本：</span>
                                    <button @click.stop="rich_editor_to_rich_text">保存到富文本</button>
                                    <?php

                                    $args = array(
                                        'teeny' => true,
                                        'media_buttons' => false
                                    );

                                    wp_editor('', 'rich_editor', $args);
                                    ?>


                                </div>
                            </div>
                            <!--富文本-->

                            <!--选项及正确答案-->
                            <div v-show="stageType=='choice'">

                                <div class="form-group">
                                    <span>选项a：</span>
                                    <input class="form-control" type="text" v-model="option_1"/>

                                </div>

                                <div class="form-group">
                                    <span>选项b：</span>
                                    <input class="form-control" type="text" v-model="option_2"/>
                                </div>

                                <div class="form-group">
                                    <span>选项c：</span>
                                    <input class="form-control" type="text" v-model="option_3"/>
                                </div>

                                <div class="form-group">
                                    <span>正确选项：</span>
                                    <select v-model="right_option" v-show="option_1||option_2||option_3">
                                        <option value="0" v-show="option_1">a</option>
                                        <option value="1" v-show="option_2">b</option>
                                        <option value="2" v-show="option_3">c</option>
                                    </select>
                                </div>

                            </div>
                            <!--选项及正确答案-->

                            <!--绑定知识点-->
                            <div v-show="classifyType=='exercise'&&box1_model=='EDIT_STAGE'">

                                <div class="form-group">
                                    <span>绑定知识点：</span>
                                    <button @click.stop="set_bindKnowledge">设置</button>
                                    <input class="form-control" type="text" v-model="bindKnowledge"
                                           disabled="disabled"/>

                                    <span>行走步数：</span>
                                    <button @click.stop="set_bindKnowledge">设置</button>
                                    <input class="form-control" type="text" v-model="review_go_num"
                                           disabled="disabled"/>


                                </div>

                            </div>
                            <!--绑定知识点-->


                            <div class="form_btn">
                                <button v-show="box1_model=='CREATE_CARD'" @click.stop="create_single_card">创建卡片
                                </button>
                                <button v-show="box1_model=='EDIT_CARD'" @click.stop="save_edit_card">保存修改</button>
                                <button v-show="box1_model=='EDIT_STAGE'" @click.stop="save_edit_stage">保存关卡</button>
                            </div>


                        </div>
                        <!--表单-->


                    </div>
                    <!--创建与编辑卡片-->


                    <!--替换音频-->
                    <div v-show="box1_model=='REPLACE_AUDIO'">
                        <div class="create_model_form">

                            <div>

                                <div class="form-group">
                                    <button @click.stop="upload_audio">选择音频</button>
                                    <input class="form-control" type="text" v-model="course_audio_src"/>


                                </div>

                            </div>
                        </div>

                    </div>
                    <!--替换音频-->


                    <!--导入或导出json-->
                    <div v-show="box1_model=='IMPORT_AND_OUTPORT_JSON'">
                        <div class="create_model_form">

                            <textarea cols="120" rows="10" id="input_course_json">{{show_json}}</textarea>

                            <button @click.stop="copy_rough_draft_course_learning_stage_json">复制</button>
                            <button @click.stop="save_rough_draft_course_learning_stage_json">保存</button>

                        </div>


                    </div>
                    <!--导入或导出json-->


                    <!--推送数据-->
                    <div v-show="box1_model=='PUSH_DATA'">
                        <div class="create_model_form">

                            <div>

                                <div class="form-group">
                                    <label>文章ID</label>
                                    <input class="form-control" type="text" v-model="push_post_id"/>
                                </div>

                            </div>

                            <button v-show="push_post_id" @click.stop="select_push_post">查询</button>

                            <div v-if="setect_push_post_result==0">
                                没有这篇文章

                            </div>


                            <div v-if="setect_push_post_result" v-html="setect_push_post_result">
                                展示文章信息
                            </div>


                            <button v-if="setect_push_post_result" @click.stop="push_post">推送</button>


                        </div>


                    </div>
                    <!--推送数据-->


                </div>
                <!--新建或编辑卡片-->


            </div>
            <div class="popLayer1" v-show="is_open_popBox1" v-cloak>

            </div>
            <!--对话框内部内容1-->


            <!--对话框内部内容2-->
            <div class="popBox2" v-show="is_open_popBox2" v-cloak>
                <div class="close_btn">
                    <span @click.stop="close_popBox2" class="glyphicon glyphicon-remove" aria-hidden="true"></span>
                </div>

                <div class="content_area2">


                    <template v-for="(stage,key) in rough_draft_course_learning_stage_json">

                        <div @click.stop="choose_knowledge(stage,key)" class='choose_stage'
                             :class="{choosed:is_binded(stage,key)}"
                             v-if="stage.classifyType=='course'&&key<=pageindex">


                            <div class="choose_stage_inner">

                                <div class="choose_stage_inner_top">
                                    <div>{{stage.headline}}</div>
                                </div>
                                <div class="choose_stage_inner_body">

                                    <div v-if="stage.stageType=='knowledge'||stage.stageType=='knowledgeHasImage'"
                                         v-html="stage.rich_text">


                                    </div>

                                    <div v-if="stage.stageType=='sentence'">
                                        <p style="font-weight: 800;font-size: 14px">{{stage.english_sentence}}</p>

                                        <p>{{stage.chinese_sentence}}</p>

                                        <div v-html="stage.rich_text">

                                        </div>
                                    </div>

                                    <div v-if="stage.stageType=='word'">
                                        <p style="font-weight: 800;font-size: 14px">{{stage.learn_word}}</p>

                                        <p>{{stage.word_explain}}</p>

                                        <div v-html="stage.rich_text">

                                        </div>
                                    </div>


                                    <div v-if="stage.stageType=='begin'">

                                        <p v-if="stage.showtext_1">{{stage.showtext_1}}</p>

                                        <p v-if="stage.showtext_2">{{stage.showtext_2}}</p>
                                    </div>

                                </div>

                            </div>


                        </div>


                    </template>

                    <div style="width: 100%;height: 300px;float: left"></div>


                </div>
                <div>
                    <button @click.stop="reset_choosed">重选</button>
                    <button @click.stop="close_popBox2">确定并关闭</button>
                </div>


            </div>
            <div class="popLayer2" v-show="is_open_popBox2" v-cloak>

            </div>
            <!--对话框内部内容2-->


        </div>


        <script>


            var container = new Vue({
                    el: '#container',
                    data: {

                        CLIENTW: 0,
                        CLIENTH: 0,
                        post_title: "<?php echo $post_title;?>",//课程标题
                        post_id:<?php echo $post_id;?>,//课程ID


                        ajaxUrl: "<?php echo $ajaxUrl;?>",//ajaxUrl
                        selectUrl: "<?php echo $selectUrl;?>",//数据查询url
                        web_url: "<?php echo $web_url;?>",//网站url


                        course_audio_src: "<?php echo $course_audio_src ;?>",//音频url

                        course_audio: "",//音频对象

                        is_course_audio_playing: false,//音频是否正在播放

                        course_audio_playbackspeed: "1x",

                        audio_current_time: "00:00.00",
                        audio_total_time: "00:00.00",

                        audio_listener_int: 0,
                        audio_played_percent: 0,


                        replace_audio: false,//替换音频窗口
                        is_open_popBox1: false,//是否开启弹窗1
                        is_open_popBox2: false,//是否开启弹窗1


                        old_course_learning_stage_json:<?php echo $old_course_learning_stage_json;?>,


                        rough_draft_course_learning_stage_json:<?php echo $rough_draft_course_learning_stage_json;?>,//草稿课程json内容

                        is_rough_draft_changed: false,//草稿是否与之前课程json相同

                        course_learning_stage_json:<?php echo $course_learning_stage_json;?>,


                        course_card_base_json:<?php echo $course_card_base_json;?>,//卡片库json

                        on_card: "null",//鼠标指向卡片


                        box1_model: "",//一号弹窗工作模式
                        box2_model: "",

                        stageType: "begin",//卡片类型
                        classifyType: "course",//大分类  course 课程 exercise


                        timestamp: "",//时间戳


                        headline: "",//课文标题
                        purport: "",
                        showtext_1: "",
                        showtext_2: "",
                        cat_name: "",//课程名
                        brand_name: "",//品牌名
                        subhead: "",
                        img: "",//图片
                        english_sentence: "",//英文句子
                        chinese_sentence: "",//中文句子
                        english_sentence_has_dot: "",
                        english_sentence_no_dot: "",


                        explain_1: "",//讲解1
                        explain_2: "",//讲解2
                        explain_3: "",//讲解3
                        explain_4: "",//讲解4

                        learn_word: "",//单词
                        word_explain: "",//释义
                        word_phonetic: "",//音标

                        question_stem_1: '',//题干1
                        question_stem_2: '',//题干2
                        option_1: "",//选项1
                        option_2: "",//选项2
                        option_3: "",//选项3
                        right_option: "",//正确选项
                        bindKnowledge: "",//绑定知识点
                        review_go_num: 0,//行走步数

                        rich_text: "",//富文本

                        current_edit_card_key: "",


                        pageindex: 0,//页面指针
                        now_stage_json: {}, //当前关卡
                        create_nav_j: {

                            "begin": {
                                "name": "封面与过渡",
                                "classifyType": "course"
                            },

                            "knowledge": {
                                "name": "知识点（无图）",
                                "classifyType": "course"
                            },


                            "knowledgeHasImage": {
                                "name": "知识点（带图）",
                                "classifyType": "course"
                            },


                            "sentence": {
                                "name": "句型讲解",
                                "classifyType": "course"
                            },

                            "word": {
                                "name": "单词讲解",
                                "classifyType": "course"
                            },


                            "voice": {
                                "name": "语音练习",
                                "classifyType": "exercise"
                            },

                            "lack": {
                                "name": "缺词填空",
                                "classifyType": "exercise"
                            },

                            "choice": {
                                "name": "单项选择题",
                                "classifyType": "exercise"
                            },

                            "spell": {
                                "name": "单词拼写",
                                "classifyType": "exercise"
                            },

                            "wordchoice": {
                                "name": "随机单词题",
                                "classifyType": "exercise"
                            },

                            "listen": {
                                "name": "纯听力",
                                "classifyType": "course"
                            }

                        },//创建卡片菜单项目

                        choose_create_card_type: "begin",
                        choose_create_card_classify: "course",

                        is_super_admin:<?php echo $is_super_admin;?>,
                        push_post_id: "",
                        setect_push_post_result: 0


                    },
                    methods: {


                        old_to_rough_draft: function () {


                            if (confirm("将老关卡数据转为新关卡，确认后草稿数据将被替换")) {


                                /*
                                 *
                                 *
                                 * 第一步，将所有老关卡补上 timestamp
                                 *
                                 * */


                                for (var i = 0; i < this.old_course_learning_stage_json.length; i++) {
                                    if (!this.old_course_learning_stage_json[i].timestamp) {
                                        var audio_time = this.audio_time_to_timestamp(this.old_course_learning_stage_json[i - 1].timestamp) + 0.1;
                                        this.old_course_learning_stage_json[i].timestamp = "[" + this.timestamp_to_audio_time(audio_time) + "]";
                                    }
                                }


                                /*
                                 * 第二步，老关卡中，超过音频总时长的关卡删掉
                                 *
                                 * */

                                /*    for (var j = 0; j < this.old_course_learning_stage_json.length; j++) {


                                 if (this.audio_time_to_timestamp(this.old_course_learning_stage_json[j].timestamp) > this.course_audio.duration - 0.3) {
                                 //console.log("卡片" + j + "超过总时长");
                                 //console.log("总时长：" + this.course_audio.duration);
                                 //console.log("卡片时长：" + this.audio_time_to_timestamp(this.old_course_learning_stage_json[j].timestamp));

                                 this.old_course_learning_stage_json.splice(j, 1);
                                 j--
                                 }


                                 }*/


                                /*
                                 *
                                 * 第三步，复制到草稿关卡
                                 * */


                                this.rough_draft_course_learning_stage_json = [].concat(this.old_course_learning_stage_json);


                                /*
                                 *
                                 * 第四步，当前预览关卡设置
                                 * */

                                this.audio_listener(false);//音频监听暂停
                                this.pageindex = 0;//指针设为0

                                this.now_stage_json = this.rough_draft_course_learning_stage_json[this.pageindex];//预览json
                                this.course_audio.currentTime = 0;//当前时间设置

                                this.timebar_set_position(0);//设置进度条位置
                                this.audio_listener(true);//音频监听暂停


                                alert("更换成功");


                            }


                        },

                        audio_initialization: function () {


                            var _this = this;


                            this.CLIENTW = document.body.offsetWidth;//屏幕宽度
                            this.CLIENTH = document.body.offsetHeight;//屏幕高度


                            /*
                             *
                             * 音频进度监听，在自动播放的情况下
                             *
                             * */


                            if (this.course_audio_src) {
                                this.course_audio = new Audio(this.course_audio_src);//音频初始化


                                //监听音频 显示目前音频进度

                                var int1 = window.setInterval(function () {

                                    _this.audio_current_time = _this.timestamp_to_audio_time(_this.course_audio.currentTime);//显示时间
                                    _this.audio_total_time = _this.timestamp_to_audio_time(_this.course_audio.duration);//显示最长时间


                                    /*计算音频播放百分比*/

                                    _this.audio_played_percent = ((_this.course_audio.currentTime / _this.course_audio.duration).toFixed(6)) * 100;//音频播放百分比;


                                    /*设置进度条位置*/

                                    _this.timebar_set_position(_this.audio_played_percent);//设置进度条位置

                                }, 100);


                            }


                            //观察草稿与保存关卡区别

                            window.setInterval(function () {


                                //console.log(_this.course_learning_stage_json);
                                //console.log(_this.rough_draft_course_learning_stage_json);


                                if (!_this.array_equar(_this.course_learning_stage_json, _this.rough_draft_course_learning_stage_json)) {

                                    _this.is_rough_draft_changed = true;

                                } else {
                                    _this.is_rough_draft_changed = false;
                                }

                            }, 100);


                            //初始化关卡

                            if (this.rough_draft_course_learning_stage_json.length > 0) {
                                this.now_stage_json = this.rough_draft_course_learning_stage_json[this.pageindex];
                            }


                        },//音频初始化

                        audio_play: function () {


                            this.course_audio.play();//播放音频
                            this.is_course_audio_playing = true;//可音频播放中

                            this.audio_listener(true);//音频监听开启


                        },//播放音频

                        audio_pause: function () {


                            this.course_audio.pause();//音频暂停
                            this.is_course_audio_playing = false;//音频停止播放

                            this.audio_listener(false);//音频监听开启
                        },//音频暂停


                        change_audio_playbackspeed: function () {

                            if (this.course_audio_playbackspeed == "1x") {
                                //this.course_audio.defaultPlaybackRate=10;
                                this.course_audio.playbackRate = 2;
                                this.course_audio.defaultPlaybackRate = 2;
                                this.course_audio_playbackspeed = "2x";

                                //console.log(this.course_audio.defaultPlaybackRate);
                            } else if (this.course_audio_playbackspeed == "2x") {
                                // this.course_audio.defaultPlaybackRate=1;
                                this.course_audio.playbackRate = 3;
                                this.course_audio.defaultPlaybackRate = 3;
                                this.course_audio_playbackspeed = "3x";

                            } else {
                                this.course_audio.playbackRate = 1;
                                this.course_audio.defaultPlaybackRate = 1;
                                this.course_audio_playbackspeed = "1x";
                            }

                        },//音频播放速度更改


                        audio_listener: function (isOpen) {
                            var _this = this;


                            if (isOpen) {

                                //console.log("音频监听:开启");


                                this.audio_listener_int = window.setInterval(function () {





                                    /*播放结束重置音频*/


                                    /*通过音频时间判断，来设置卡片的 page index增加*/

                                    _this.pageindex_auto_go();


                                }, 10);

                            } else {

                                //console.log("音频监听:关闭");

                                window.clearInterval(this.audio_listener_int);
                            }


                        },//监听音频


                        timebar_set_position: function (audio_played_percent) {

                            var l = (audio_played_percent - 1).toString() + "%";

                            $("#timebar_bar").css("left", l);

                        },//设置进度条位置


                        pageindex_auto_go: function () {


                            var next_stage_timestamp = 0;//下一个关卡时间点

                            /*获取下一个时间节点*/

                            for (var i = this.pageindex + 1; i < this.rough_draft_course_learning_stage_json.length; i++) {


                                if (this.rough_draft_course_learning_stage_json[i].timestamp) {


                                    next_stage_timestamp = this.audio_time_to_timestamp(this.rough_draft_course_learning_stage_json[i].timestamp);//转换成可计算时间
                                    break;
                                }

                            }


                            /*没有获取到*/

                            if (next_stage_timestamp == 0) {
                                next_stage_timestamp = this.course_audio.duration - 0.3;

                            }


                            if (this.course_audio.currentTime >= next_stage_timestamp) {


                                if (this.pageindex < this.rough_draft_course_learning_stage_json.length - 1) {
                                    this.pageindex += 1;
                                } else {
                                    this.pageindex = 0;
                                    this.course_audio.currentTime = 0;
                                }


                            }


                        },


                        timebar_progress_click: function (event) {


                            var startx = event.x;//点击横轴位置

                            var screenWidth = document.body.clientWidth;//屏幕宽度


                            var processpercent = ((startx - screenWidth * 10 / 100) / (screenWidth * 75 / 100) * 100).toFixed(2);//获得进度百分比。

                            //设置拖动按钮位置


                            //console.log(processpercent);


                            var time = processpercent * this.course_audio.duration / 100;//确定音频时间


                            this.adjust_audio_currentTime(time);


                        },//时间轴点击事件


                        adjust_audio_currentTime: function (time) {


                            this.audio_listener(false);//关闭音频监听


                            this.course_audio.currentTime = time;//调整音频时间


                            //调整pageindex

                            for (let i = 0; i < this.rough_draft_course_learning_stage_json.length; i++) {

                                var stage_timestamp_1 = "";
                                var stage_timestamp_2 = "";

                                if (i < this.rough_draft_course_learning_stage_json.length - 1) {

                                    stage_timestamp_1 = this.audio_time_to_timestamp(this.rough_draft_course_learning_stage_json[i].timestamp);
                                    stage_timestamp_2 = this.audio_time_to_timestamp(this.rough_draft_course_learning_stage_json[i + 1].timestamp);


                                } else {

                                    stage_timestamp_1 = this.audio_time_to_timestamp(this.rough_draft_course_learning_stage_json[i].timestamp);
                                    stage_timestamp_2 = this.course_audio.duration;

                                }


                                if (this.course_audio.currentTime >= stage_timestamp_1 && this.course_audio.currentTime < stage_timestamp_2) {
                                    this.pageindex = i;
                                    break;
                                }


                            }


                            this.audio_listener(true);//开启音频监听


                        },


                        timestamp_to_audio_time: function (timestamp) {

                            var t = (timestamp / 60).toFixed(4).toString();
                            var a = t.split(".");
                            var integer = parseInt(a[0]);
                            var integerStr = "";
                            var float = (timestamp % 60).toFixed(2);
                            var floatStr = "";


                            if (float > 0 && float < 10) {
                                floatStr = "0" + float.toString();

                            } else if (float >= 10) {
                                floatStr = float.toString();

                            } else {
                                floatStr = "00.00";
                            }


                            if (integer > 0 && integer < 10) {
                                integerStr = "0" + integer.toString();

                            } else if (integer >= 10) {
                                integerStr = integer.toString();

                            } else {
                                integerStr = "00";
                            }


                            return integerStr + ":" + floatStr;


                        },//时间戳转为可视化时间

                        audio_time_to_timestamp: function (audio_time) {


                            audio_time = audio_time.substring(1, audio_time.length - 1);//去掉[]


                            var audio_time_arr = audio_time.split(":");

                            return parseFloat((parseInt(audio_time_arr[0] * 60) + parseFloat(audio_time_arr[1])).toFixed(3));


                        },//可视化时间转为时间戳


                        upload_audio: function (e) {

                            var upload_frame;//上传音频对话框

                            var _this = this;


                            e.preventDefault();

                            if (upload_frame) {
                                upload_frame.open();
                                return;
                            }


                            upload_frame = wp.media({
                                title: '导入音频',
                                button: {
                                    text: '导入'
                                },
                                multiple: false
                            });//上传音频对话框编辑

                            upload_frame.on('select', function () {

                                var attachment = upload_frame.state().get('selection').first().toJSON();
                                //alert(attachment.url)


                                $$HTTPPOST(_this.ajaxUrl, {
                                    post_id: _this.post_id,
                                    action: 'save_audio_src_two',
                                    course_audio_src: attachment.url

                                }, function (e) {

                                    //console.log(e);
                                    if (e == 1) {

                                        _this.course_audio_src = attachment.url;

                                        _this.course_audio = new Audio(_this.course_audio_src);//音频初始化


                                        _this.course_audio.currentTime = 0;//时间初始化
                                        _this.pageindex = 0;


                                        _this.cut_out_time_stage();//切掉多余关卡


                                    }


                                });


                            }, true);


                            upload_frame.open();


                        },//上传音频；

                        upload_img: function (e) {


                            var upload_frame;//上传图片

                            var _this = this;


                            e.preventDefault();

                            if (upload_frame) {
                                upload_frame.open();
                                return;
                            }


                            upload_frame = wp.media({
                                title: '导入图片',
                                button: {
                                    text: '导入'
                                },
                                multiple: false
                            });//上传音频对话框编辑

                            upload_frame.on('select', function () {

                                var attachment = upload_frame.state().get('selection').first().toJSON();
                                alert(attachment.url)

                                _this.img = attachment.url


                            }, true);


                            upload_frame.open();

                        },


                        cut_out_time_stage: function () {

                            //监听音频，将音频总时长外的关卡自动删除

                            var _this = this;


                            var int2 = window.setInterval(function () {

                                if (_this.course_audio.duration) {


                                    var out_time_stage_key = [];//在总时长外的关卡

                                    for (let i = 0; i < _this.rough_draft_course_learning_stage_json.length; i++) {

                                        var stage_time = _this.audio_time_to_timestamp(_this.rough_draft_course_learning_stage_json[i].timestamp);


                                        if (stage_time >= _this.course_audio.duration) {

                                            out_time_stage_key.push(i);
                                        }


                                    }


                                    //console.log(out_time_stage_key);

                                    if (out_time_stage_key.length > 0) {


                                        _this.rough_draft_course_learning_stage_json.splice(out_time_stage_key[0], _this.rough_draft_course_learning_stage_json.length - out_time_stage_key[0]);


                                    }

                                    //console.log(_this.rough_draft_course_learning_stage_json);


                                    window.clearInterval(int2);


                                }


                            }, 100);

                        },


                        go_replace_audio: function () {

                            this.box1_model = "REPLACE_AUDIO";

                            this.open_popBox1();

                        },// 更换音频

                        open_popBox1: function () {


                            /*禁止滚动条*/
                            document.documentElement.style.overflow = 'hidden';
                            document.body.style.overflow = 'hidden';//手机版设置这个


                            var popLayer1 = $(".popLayer1")[0];//黑色透明背景
                            var popBox1 = $(".popBox1")[0];//弹窗页面

                            var w = document.body.clientWidth;//屏宽
                            var h = document.body.clientHeight;//屏高

                            /*设置黑色背景宽高*/
                            popLayer1.style.width = w + "px";
                            popLayer1.style.height = h + "px";

                            /*设置弹窗页面宽高*/
                            popBox1.style.width = (w * 9 / 10).toString() + "px";
                            popBox1.style.height = (h * 9 / 10).toString() + "px";


                            var content_area1 = $(".content_area1")[0];//弹窗内容区域

                            /*设置页面内容宽高*/
                            content_area1.style.height = (h * 9 / 10 - 50).toString() + "px";


                            this.is_open_popBox1 = true;//打开弹窗

                            //暂停音频

                            this.audio_pause();


                        },//打开一层弹窗

                        close_popBox1: function () {


                            this.is_open_popBox1 = false;//关闭弹窗

                            //恢复页面滚动
                            document.documentElement.style.overflow = '';
                            document.body.style.overflow = '';//手机版设置这个

                            this.data_reset();//数据重置

                        },//关闭一层弹窗


                        open_popBox2: function () {


                            var popLayer2 = $(".popLayer2")[0];//黑色透明背景
                            var popBox2 = $(".popBox2")[0];//弹窗页面

                            var w = document.body.offsetWidth;//屏宽
                            var h = document.body.offsetHeight;//屏高

                            /*设置黑色背景宽高*/
                            popLayer2.style.width = w + "px";
                            popLayer2.style.height = h + "px";

                            /*设置弹窗页面宽高*/
                            popBox2.style.width = (w * 8 / 10).toString() + "px";
                            popBox2.style.height = (h * 8 / 10).toString() + "px";


                            var content_area2 = $(".content_area2")[0];//弹窗内容区域

                            /*设置页面内容宽高*/
                            content_area2.style.height = (h * 8 / 10 - 50).toString() + "px";


                            this.is_open_popBox2 = true;//打开弹窗


                        },//打开二层弹窗


                        close_popBox2: function () {


                            this.is_open_popBox2 = false;//关闭弹窗


                        },//关闭二层弹窗


                        stage_percent_in_course_process: function (single_stage_json, key) {






                            //转成时间戳模式
                            var realtimestamp = this.audio_time_to_timestamp(single_stage_json.timestamp);
                            return ((realtimestamp / this.course_audio.duration * 100).toFixed(2)).toString() + "%";


                        },//获取关卡在整节课内的进度百分比


                        array_equar: function (a, b) {


                            if (a.length !== b.length) {
                                return false;


                            } else {
                                //循环遍历数组的值进行比较

                                var flag = true;

                                for (let i = 0; i < a.length; i++) {

                                    if (!this.json_equar(a[i], b[i])) {
                                        flag = false;
                                        break
                                    }
                                }

                                return flag;


                            }


                        },//判断课程数组是否相同


                        json_equar: function (obj1, obj2) {


                            if (obj1.length !== obj2.length) {
                                return false;

                            } else {

                                var flag = true;
                                for (let x in obj1) {


                                    if (obj2.hasOwnProperty(x)) {

                                        if (obj1[x] != obj2[x]) {

                                            flag = false;
                                            break
                                        }

                                    } else {
                                        flag = false;
                                        break
                                    }
                                }

                                return flag;
                            }


                        },//判断两个json是否相同

                        go_create_card: function () {

                            this.box1_model = "CREATE_CARD";
                            this.open_popBox1();


                        },//创建卡片


                        choose_card_type: function (single_nav, key) {


                            this.data_reset();//数据重置


                            /*
                             * 重置富文本编辑器
                             * */


                            $("#rich_editor-html").trigger("click");

                            $("#rich_editor").val("");//富文本区域重置

                            $("#rich_editor-tmce").trigger("click");


                            this.stageType = key;//关卡类型


                            this.classifyType = single_nav.classifyType;//关卡分类;


                        },//选择卡片类型


                        move_to_card: function (key) {
                            this.on_card = key;
                        },//鼠标移动到卡片


                        /*
                         *
                         * 将对象的属性，赋值到当前属性
                         * */


                        obj_to_this: function (obj) {


                            this.stageType = obj.stageType;//卡片类型
                            this.classifyType = obj.classifyType;//大分类  course 课程 exercise

                            this.timestamp = obj.timestamp;//时间赋值


                            this.headline = obj.headline;//课文标题


                            this.purport = obj.purport;//主旨
                            this.showtext_1 = obj.showtext_1;
                            this.showtext_2 = obj.showtext_2;
                            this.cat_name = obj.cat_name;//课程名
                            this.brand_name = obj.brand_name;//品牌名
                            this.subhead = obj.subhead;
                            this.img = obj.img;//图片


                            this.english_sentence = obj.english_sentence;//英文句子
                            this.chinese_sentence = obj.chinese_sentence;//中文句子

                            this.english_sentence_has_dot = obj.english_sentence_has_dot;
                            this.english_sentence_no_dot = obj.english_sentence_no_dot;


                            this.explain_1 = obj.explain_1;//讲解1
                            this.explain_2 = obj.explain_2;//讲解2
                            this.explain_3 = obj.explain_3;//讲解3
                            this.explain_4 = obj.explain_4;//讲解4

                            this.learn_word = obj.learn_word;//单词
                            this.word_explain = obj.word_explain;//释义
                            this.word_phonetic = obj.word_phonetic;//音标

                            this.question_stem_1 = obj.question_stem_1;//题干1
                            this.question_stem_2 = obj.question_stem_2;//题干2
                            this.option_1 = obj.option_1;//选项1
                            this.option_2 = obj.option_2;//选项2
                            this.option_3 = obj.option_3;//选项3
                            this.right_option = obj.right_option;//正确选项

                            this.bindKnowledge = obj.bindKnowledge;
                            this.review_go_num = obj.review_go_num;


                            this.rich_text = (obj.rich_text) ? obj.rich_text : "";//富文本


                            this.rich_text_to_rich_editor();   //rich_text赋值到rich_editor

                        },


                        this_to_obj: function () {


                            var obj = {};


                            obj.stageType = this.stageType;//卡片类型

                            obj.classifyType = this.classifyType;//大分类  course 课程 exercise


                            obj.timestamp = this.timestamp;//时间戳


                            obj.headline = this.headline;//课文标题

                            obj.purport = this.purport;//主旨
                            obj.showtext_1 = this.showtext_1;//显示文字1
                            obj.showtext_2 = this.showtext_2;//显示文字2
                            obj.cat_name = this.cat_name;//课程名
                            obj.brand_name = this.brand_name;//品牌名
                            obj.subhead = this.subhead;
                            obj.img = this.img;//图片


                            obj.english_sentence = this.english_sentence;//英文句子
                            obj.chinese_sentence = this.chinese_sentence;//中文句子
                            obj.english_sentence_has_dot = this.english_sentence_has_dot;
                            obj.english_sentence_no_dot = this.english_sentence_no_dot;


                            obj.explain_1 = this.explain_1;//讲解1
                            obj.explain_2 = this.explain_2;//讲解2
                            obj.explain_3 = this.explain_3;//讲解3
                            obj.explain_4 = this.explain_4;//讲解4

                            obj.learn_word = this.learn_word;//单词
                            obj.word_explain = this.word_explain;//释义
                            obj.word_phonetic = this.word_phonetic;//音标

                            obj.question_stem_1 = this.question_stem_1;//题干1
                            obj.question_stem_2 = this.question_stem_2;//题干2
                            obj.option_1 = this.option_1;//选项1
                            obj.option_2 = this.option_2;//选项2
                            obj.option_3 = this.option_3;//选项3
                            obj.right_option = this.right_option;//正确选项


                            obj.bindKnowledge = this.bindKnowledge;//绑定知识点
                            obj.review_go_num = this.review_go_num;//行走步数

                            obj.rich_text = this.rich_text;//富文本


                            return obj;


                        },


                        edit_card: function (key) {

                            this.box1_model = "EDIT_CARD";

                            this.current_edit_card_key = key;


                            var obj = this.course_card_base_json[key];

                            console.log(obj);

                            this.obj_to_this(obj);


                            this.open_popBox1();

                        },//编辑卡片

                        save_edit_card: function () {

                            var is_can_save = this.is_card_can_save();

                            if (is_can_save) {
                                var obj = this.this_to_obj();


                                /*
                                 * 替换掉当前K的obj
                                 * */


                                this.course_card_base_json.splice(this.current_edit_card_key, 1, obj);


                                this.close_popBox1();//关闭弹窗1 数据重置


                            } else {
                                $$alert("提示", "缺少必填项", function () {
                                });
                            }

                        },//保存修改卡片

                        create_single_card: function () {


                            var is_can_create = this.is_card_can_save();


                            if (is_can_create) {
                                var obj = this.this_to_obj();


                                this.course_card_base_json.push(obj);//压入数组


                                this.close_popBox1();//关闭弹窗1 数据重置


                            } else {
                                $$alert("提示", "缺少必填项", function () {
                                });
                            }


                        },//创建单个卡片


                        is_card_can_save: function () {


                            /*富文本启用但没有保存*/


                            if (!this.is_rich_editor_equal_rich_text()) {


                                if (confirm("文本框改动未保存，是否保存？")) {

                                    this.rich_editor_to_rich_text();

                                }


                            }


                            switch (this.stageType) {

                                case "begin":
                                    if (this.img) {// 时间戳 标题 图片地址必须

                                        this.headline = "老师过渡";
                                        return true;
                                    }
                                    break;


                                case "knowledge":
                                    if (this.headline) {//时间戳，英文中文例句必须
                                        return true;
                                    }
                                    break;


                                case "knowledgeHasImage":
                                    if (this.img && this.headline) {//时间戳，英文中文例句必须
                                        return true;
                                    }
                                    break;


                                case "sentence":
                                    if (this.english_sentence && this.chinese_sentence) {//时间戳，英文中文例句必须
                                        this.headline = "句型讲解";
                                        return true;

                                    }
                                    break;


                                case "word":
                                    if (this.learn_word && this.word_explain && this.word_phonetic) {//单词、释义、音标必须

                                        this.learn_word = this.learn_word.trim();
                                        this.headline = "单词讲解";
                                        return true;

                                    }
                                    break;


                                case "listen":
                                    this.headline = "纯听力";
                                    return true;
                                    break;


                                case "voice":
                                    if (this.question_stem_1) {//时间戳，英文中文例句必须
                                        this.headline = "语音练习";
                                        return true;

                                    }
                                    break;

                                case "lack":
                                    if (this.english_sentence && this.chinese_sentence) {//英文中文例句必须
                                        this.headline = "填空题";
                                        return true;
                                    }
                                    break;


                                case "choice":
                                    if (this.question_stem_1 && this.option_1 && this.option_2 && this.right_option) {
                                        this.headline = "选择题";
                                        return true;
                                    }
                                    break;

                                case "spell":


                                    if (this.learn_word && this.word_explain) {
                                        this.learn_word = this.learn_word.trim();
                                        this.headline = "单词拼写";
                                        return true;
                                    }
                                    break;

                                case "wordchoice":

                                    if (this.learn_word && this.word_explain) {
                                        this.learn_word = this.learn_word.trim();
                                        this.headline = "随机单词题";
                                        return true;
                                    }
                                    break;
                            }


                            return false;


                        },//卡片是否可以保存


                        delete_single_card: function (key) {
                            this.course_card_base_json.splice(key, 1);
                        },//删除单个卡片


                        copy_and_add_single_card: function (key) {


                            var obj = this.json_deep_copy(this.course_card_base_json[key]);//复制obj

                            this.course_card_base_json.splice(key, 0, obj);

                        },//复制并新建相同卡片

                        insert_single_card: function (single_card) {


                            var new_obj = this.json_deep_copy(single_card);//复制该卡片


                            if (this.rough_draft_course_learning_stage_json.length > 0) {


                                var ready_to_set_time = "[" + this.timestamp_to_audio_time(this.course_audio.currentTime) + "]";

                                var is_seted = false;


                                for (let i = 0; i < this.rough_draft_course_learning_stage_json.length; i++) {
                                    if (ready_to_set_time == this.rough_draft_course_learning_stage_json[i].timestamp) {
                                        is_seted = true;
                                    }
                                }


                                if (is_seted) {

                                    if (confirm("该时间点上已经存在卡片，是否错开时间")) {

                                        if (this.course_audio.currentTime + 0.1 >= this.course_audio.duration - 0.3) {
                                            alert("超出时间范围");
                                            return;
                                        } else {
                                            ready_to_set_time = "[" + this.timestamp_to_audio_time(this.course_audio.currentTime + 0.1) + "]";

                                            this.adjust_audio_currentTime(this.course_audio.currentTime + 0.1);

                                        }


                                    } else {
                                        return;
                                    }


                                }


                                //时间设置
                                new_obj.timestamp = ready_to_set_time;


                                //插入位置设置
                                var key = this.pageindex + 1;//插入位置


                                //若是课程卡片，需要设置主旨，用于绑定知识点。

                                if (new_obj.classifyType == 'course') {
                                    //设置主旨，用于绑定知识点
                                    new_obj.purport = new_obj.headline + "_" + new Date().getTime();
                                }


                                //添加入课程关卡
                                this.rough_draft_course_learning_stage_json.splice(key, 0, new_obj);


                            }

                            else {

                                //设置主旨，用于绑定知识点


                                new_obj.purport = new_obj.headline + "_" + new Date().getTime();


                                //设置时间
                                new_obj.timestamp = "[" + this.timestamp_to_audio_time(this.course_audio.currentTime) + "]";


                                //设置封面情况
                                this.rough_draft_course_learning_stage_json.push(new_obj);

                                //初始化关卡
                                this.now_stage_json = this.rough_draft_course_learning_stage_json[this.pageindex];
                            }


                        },//插入卡片到音频


                        quick_create_exercise: function (single_card, key) {
                            console.log(single_card);


                            this.obj_to_this(single_card);

                            if (this.stageType == 'word') {
                                console.log("word");
                                this.stageType = 'wordchoice'

                            } else {
                                console.log("sentence");
                                this.stageType = 'lack'
                            }

                            this.classifyType = "exercise";

                            console.log(this.english_sentence);

                            this.english_sentence_has_dot = this.get_english_sentence_has_dot(this.english_sentence);
                            this.english_sentence_no_dot = this.get_english_sentence_no_dot(this.english_sentence);
                            console.log(this.english_sentence_has_dot);
                            console.log(this.english_sentence_no_dot);

                            this.create_single_card();


                        },


                        search_word_text: function () {

                            if (this.learn_word) {
                                var _this = this;

                                $.ajax({
                                    url: _this.selectUrl,
                                    type: 'post',
                                    data: {action: 'selectWord', word: _this.learn_word},
                                    success: function (data, status) {
                                        var res = data.toString().trim();
                                        res = $.parseJSON(res);//转为json对象
                                        _this.word_phonetic = res.phonetic;
                                        _this.word_explain = res.explains.toString().trim();
                                    }
                                });

                            }


                        },//搜索词典


                        rich_text_to_rich_editor: function () {


                            $("#rich_editor-html").trigger("click");


                            var content = this.rich_text;


                            /*
                             * 将content 中的html标签去除。
                             * 每个段落尾部加上# 并换行
                             *
                             * 在富文本编辑器中显示。
                             *
                             * */


                            if (content) {


                                var contentArr = content.split("</p>");


                                for (let i = 0; i < contentArr.length; i++) {

                                    if (!contentArr[i]) {
                                        contentArr.splice(i, 1);
                                        i--;
                                    } else {

                                        contentArr[i] = contentArr[i].replace(/<[^>]+>/g, "");//去掉html 标签
                                        contentArr[i] = contentArr[i] + "#\n";
                                    }
                                }

                                content = contentArr.join("");

                            }


                            //console.log("赋值到富文本区域内容："+content);


                            $("#rich_editor").val(content);


                            $("#rich_editor-tmce").trigger("click");


                        },//rich_text赋值到rich_editor


                        rich_editor_to_rich_text: function () {

                            $("#rich_editor-html").trigger("click");


                            /*
                             *
                             *
                             * 从富文本编辑器获取content
                             *
                             * */


                            if ($(".wp-editor-area").length > 0) {
                                var wp_editor_id = $(".wp-editor-area").attr('id');

                                var content;
                                var editor = tinyMCE.get(wp_editor_id);
                                if (editor) {
                                    content = editor.getContent();
                                } else {
                                    content = $('#' + wp_editor_id).val();
                                }
                            }


                            /*
                             *
                             *
                             * 去掉 content html 标签
                             *
                             * 去掉#号
                             *
                             * 给段落加上<p>
                             *
                             * 保存 到 rich_text
                             * */


                            if (content) {


                                content = content.replace(/<[^>]+>/g, "");//去掉html 标签


                                if (content.indexOf("#") != -1) {
                                    var contentArr = content.split("#");
                                    contentArr.pop();

                                    console.log(contentArr);

                                    for (let i = 0; i < contentArr.length; i++) {
                                        contentArr[i] = "<p>" + contentArr[i] + "</p>";
                                    }

                                    content = contentArr.join("");

                                } else {
                                    content = "<p>" + content + "</p>";
                                }


                            }


                            //console.log("存到数据库内容："+content);


                            this.rich_text = content;


                            $("#rich_editor-tmce").trigger("click");


                        },//富文本框内内容赋值到rich_text

                        is_rich_editor_equal_rich_text: function () {


                            console.log("进行文本框与内容检测");


                            $("#rich_editor-html").trigger("click");


                            /*
                             *
                             *
                             * 从富文本编辑器获取content
                             *
                             * */


                            if ($(".wp-editor-area").length > 0) {
                                var wp_editor_id = $(".wp-editor-area").attr('id');

                                var content;
                                var editor = tinyMCE.get(wp_editor_id);
                                if (editor) {
                                    content = editor.getContent();
                                } else {
                                    content = $('#' + wp_editor_id).val();
                                }
                            }


                            /*
                             *
                             *
                             * 去掉 content html 标签
                             *
                             * 去掉#号
                             *
                             * 给段落加上<p>
                             *
                             * 保存 到 rich_text
                             * */


                            if (content) {


                                content = content.replace(/<[^>]+>/g, "");//去掉html 标签


                                if (content.indexOf("#") != -1) {
                                    var contentArr = content.split("#");
                                    contentArr.pop();

                                    console.log(contentArr);

                                    for (let i = 0; i < contentArr.length; i++) {
                                        contentArr[i] = "<p>" + contentArr[i] + "</p>";
                                    }

                                    content = contentArr.join("");

                                } else {
                                    content = "<p>" + content + "</p>";
                                }


                            }


                            console.log("富文本中内容：" + content);


                            $("#rich_editor-tmce").trigger("click");


                            if (content != this.rich_text) {
                                return false;
                            }


                            return true;


                        },

                        clear_rich_editor: function () {
                            $("#rich_editor-html").trigger("click");
                            $("#rich_editor").val("");
                            $("#rich_editor-tmce").trigger("click");


                        },//清空富文本框

                        go_last_stage: function () {

                            if (this.rough_draft_course_learning_stage_json.length > 0) {

                                this.audio_listener(false);//音频监听暂停

                                if (this.pageindex == 0) {
                                    this.pageindex = 0
                                } else {
                                    this.pageindex -= 1
                                }


                                //设置时间


                                var stage_timestamp = this.rough_draft_course_learning_stage_json[this.pageindex].timestamp;


                                stage_timestamp = this.audio_time_to_timestamp(stage_timestamp);


                                this.course_audio.currentTime = stage_timestamp;

                                /*计算音频播放百分比*/

                                this.audio_played_percent = ((this.course_audio.currentTime / this.course_audio.duration).toFixed(6)) * 100;//音频播放百分比;


                                /*设置进度条位置*/

                                this.timebar_set_position(this.audio_played_percent);//设置进度条位置


                                this.audio_listener(true);//音频监听开启

                            }


                        },

                        go_next_stage: function () {

                            if (this.rough_draft_course_learning_stage_json.length > 0) {

                                this.audio_listener(false);//音频监听暂停

                                if (this.pageindex >= this.rough_draft_course_learning_stage_json.length - 1) {

                                } else {

                                    this.pageindex += 1
                                }


                                //设置时间


                                var stage_timestamp = this.rough_draft_course_learning_stage_json[this.pageindex].timestamp;


                                stage_timestamp = this.audio_time_to_timestamp(stage_timestamp);


                                this.course_audio.currentTime = stage_timestamp;

                                /*计算音频播放百分比*/

                                this.audio_played_percent = ((this.course_audio.currentTime / this.course_audio.duration).toFixed(6)) * 100;//音频播放百分比;


                                /*设置进度条位置*/

                                this.timebar_set_position(this.audio_played_percent);//设置进度条位置


                                this.audio_listener(true);//音频监听开启
                            }


                        },


                        detele_stage: function () {


                            /*
                             * 当关卡类型是课程时，删除关卡会影响习题所绑定的知识点
                             *
                             * */


                            if (this.rough_draft_course_learning_stage_json[this.pageindex].classifyType == 'course') {




                                /*
                                 *
                                 *遍历习题，看看有没有习题关卡绑定的知识点中，有没有需要当前关卡的
                                 *
                                 * */

                                var influence_exercise_stage_key = [];//被影响到的习题关卡组


                                for (let i = 0; i < this.rough_draft_course_learning_stage_json.length; i++) {

                                    if (this.rough_draft_course_learning_stage_json[i].classifyType == 'exercise') {

                                        /*
                                         *
                                         *
                                         * 用习题关卡的 bindKnowledge 定位需要关卡的的key(起始)，用 review_go_num 定位剩余的需要关卡key
                                         * */


                                        var bindKnowledge = this.rough_draft_course_learning_stage_json[i].bindKnowledge;
                                        var review_go_num = this.rough_draft_course_learning_stage_json[i].review_go_num;

                                        var card_need_stage_key = [];//习题关卡需要的关卡序号组


                                        if (bindKnowledge) {


                                            var begin_key = 0;//开始key


                                            //找出绑定知识点开始的那个key
                                            for (let j = 0; j < this.rough_draft_course_learning_stage_json.length; j++) {

                                                if (this.rough_draft_course_learning_stage_json[j].purport == bindKnowledge) {
                                                    begin_key = j;
                                                    break;
                                                }

                                            }

                                            //根据行走步数，找出后面的key

                                            for (let k = begin_key; k <= begin_key + review_go_num - 1; k++) {
                                                card_need_stage_key.push(k);
                                            }


                                            //遍历card_need_stage_key 比对要删除的 pageindex 看看是否存在

                                            for (let l = 0; l < card_need_stage_key.length; l++) {
                                                if (this.pageindex == card_need_stage_key[l]) {

                                                    influence_exercise_stage_key.push(i);
                                                    break;

                                                }
                                            }


                                        }


                                    }
                                }


                                /*
                                 *
                                 * 当”被影响到的习题关卡组“有值时，需要提醒是否继续删除，确定则重置这些关卡组的 bindKnowledge 和 review_go_num
                                 * */


                                if (influence_exercise_stage_key.length > 0) {

                                    if (confirm("删除此关卡将影响习题关卡" + influence_exercise_stage_key.join("、") + "的绑定知识点，继续?")) {

                                        //确定后，将被影响的习题卡片的 bindKnowledge 设为""  将 review_go_num的值设为0
                                        for (let m = 0; m < influence_exercise_stage_key.length; m++) {
                                            this.rough_draft_course_learning_stage_json[influence_exercise_stage_key[m]].bindKnowledge = "";
                                            this.rough_draft_course_learning_stage_json[influence_exercise_stage_key[m]].review_go_num = 0;
                                        }

                                    } else {
                                        //不确定则返回
                                        return;
                                    }
                                }

                            }


                            /*
                             *
                             *
                             * 执行删除关卡
                             * */


                            this.rough_draft_course_learning_stage_json.splice(this.pageindex, 1);


                            /*
                             *
                             *
                             * 删除关卡后下一步
                             * */


                            if (this.rough_draft_course_learning_stage_json.length > 0) {

                                //返回上一个关卡

                                this.go_last_stage();
                            } else {

                                //音频暂停，时间，指针设为0
                                this.audio_pause();
                                this.course_audio.currentTime = 0;
                                this.pageindex = 0;
                            }


                        },

                        copy_stage_to_base: function () {

                            console.log(this.rough_draft_course_learning_stage_json);


                            if (confirm("是否将关卡全部导为卡片？")) {

                                for (let i = 0; i < this.rough_draft_course_learning_stage_json.length; i++) {
                                    var obj = {};


                                    obj.stageType = this.rough_draft_course_learning_stage_json[i].stageType;


                                    obj.bindKnowledge = "";
                                    obj.review_go_num = 0;

                                    obj.brand_name = (this.rough_draft_course_learning_stage_json[i].brand_name) ? this.rough_draft_course_learning_stage_json[i].brand_name : "";

                                    obj.cat_name = (this.rough_draft_course_learning_stage_json[i].cat_name) ? this.rough_draft_course_learning_stage_json[i].cat_name : "";

                                    obj.chinese_sentence = (this.rough_draft_course_learning_stage_json[i].chinese_sentence) ? this.rough_draft_course_learning_stage_json[i].chinese_sentence : "";

                                    obj.classifyType = (this.rough_draft_course_learning_stage_json[i].classifyType) ? this.rough_draft_course_learning_stage_json[i].classifyType : "";

                                    obj.english_sentence = (this.rough_draft_course_learning_stage_json[i].english_sentence) ? this.rough_draft_course_learning_stage_json[i].english_sentence : "";


                                    if (obj.english_sentence) {
                                        obj.english_sentence_has_dot = this.get_english_sentence_has_dot(obj.english_sentence);
                                        obj.english_sentence_no_dot = this.get_english_sentence_no_dot(obj.english_sentence);
                                    } else {
                                        obj.english_sentence_has_dot = "";
                                        obj.english_sentence_no_dot = "";
                                    }

                                    obj.explain_1 = (this.rough_draft_course_learning_stage_json[i].explain_1) ? this.rough_draft_course_learning_stage_json[i].explain_1 : "";
                                    obj.explain_2 = (this.rough_draft_course_learning_stage_json[i].explain_2) ? this.rough_draft_course_learning_stage_json[i].explain_2 : "";
                                    obj.explain_3 = (this.rough_draft_course_learning_stage_json[i].explain_3) ? this.rough_draft_course_learning_stage_json[i].explain_3 : "";
                                    obj.explain_4 = (this.rough_draft_course_learning_stage_json[i].explain_4) ? this.rough_draft_course_learning_stage_json[i].explain_4 : "";


                                    switch (obj.stageType) {
                                        case "begin":
                                            obj.headline = "老师过渡";
                                            break;


                                        case "knowledge":
                                            obj.headline = this.rough_draft_course_learning_stage_json[i].headline;
                                            break;


                                        case "knowledgeHasImage":
                                            obj.headline = this.rough_draft_course_learning_stage_json[i].headline;
                                            break;


                                        case "sentence":
                                            obj.headline = "句型讲解";
                                            break;

                                        case "word":
                                            obj.headline = "单词讲解";
                                            break;

                                        case "listen":
                                            obj.headline = "纯听力";
                                            break;


                                        case "voice":
                                            obj.headline = "语音练习";
                                            break;

                                        case "lack":
                                            obj.headline = "填空题";
                                            break;


                                        case "choice":
                                            obj.headline = "选择题";
                                            break;

                                        case "spell":
                                            obj.headline = "单词拼写";
                                            break;

                                        case "wordchoice":
                                            obj.headline = "随机单词题";
                                            break;
                                        default :
                                            obj.headline = "关卡卡片";
                                            break;
                                    }


                                    obj.img = (this.rough_draft_course_learning_stage_json[i].img) ? this.rough_draft_course_learning_stage_json[i].img : "";


                                    obj.learn_word = (this.rough_draft_course_learning_stage_json[i].learn_word) ? this.rough_draft_course_learning_stage_json[i].learn_word.trim() : "";
                                    obj.option_1 = (this.rough_draft_course_learning_stage_json[i].option_1) ? this.rough_draft_course_learning_stage_json[i].option_1 : "";
                                    obj.option_2 = (this.rough_draft_course_learning_stage_json[i].option_2) ? this.rough_draft_course_learning_stage_json[i].option_2 : "";
                                    obj.option_3 = (this.rough_draft_course_learning_stage_json[i].option_3) ? this.rough_draft_course_learning_stage_json[i].option_3 : "";
                                    obj.right_option = (this.rough_draft_course_learning_stage_json[i].right_option) ? this.rough_draft_course_learning_stage_json[i].right_option : "";

                                    obj.purport = "";

                                    obj.question_stem_1 = (this.rough_draft_course_learning_stage_json[i].question_stem_1) ? this.rough_draft_course_learning_stage_json[i].question_stem_1 : "";
                                    obj.question_stem_2 = (this.rough_draft_course_learning_stage_json[i].question_stem_2) ? this.rough_draft_course_learning_stage_json[i].question_stem_2 : "";

                                    obj.rich_text = (this.rough_draft_course_learning_stage_json[i].rich_text) ? this.rough_draft_course_learning_stage_json[i].rich_text : "";
                                    obj.showtext_1 = (this.rough_draft_course_learning_stage_json[i].showtext_1) ? this.rough_draft_course_learning_stage_json[i].showtext_1 : "";
                                    obj.showtext_2 = (this.rough_draft_course_learning_stage_json[i].showtext_2) ? this.rough_draft_course_learning_stage_json[i].showtext_2 : "";

                                    obj.subhead = (this.rough_draft_course_learning_stage_json[i].subhead) ? this.rough_draft_course_learning_stage_json[i].subhead : "";

                                    obj.word_explain = (this.rough_draft_course_learning_stage_json[i].word_explain) ? this.rough_draft_course_learning_stage_json[i].word_explain : "";
                                    obj.word_phonetic = (this.rough_draft_course_learning_stage_json[i].word_phonetic) ? this.rough_draft_course_learning_stage_json[i].word_phonetic : "";


                                    obj.timestamp = "";

                                    console.log(obj);


                                    this.course_card_base_json.push(obj);//压入数组
                                }


                            }
                        },

                        clear_stage:function(){

                            if (confirm("清空当前课程全部关卡？")) {


                                this.rough_draft_course_learning_stage_json=[];

                            }

                        },

                        clear_course_card_base_json: function () {

                            if (confirm("是否清空全部卡片？")) {
                                this.course_card_base_json = [];
                            }
                        },

                        edit_stage: function () {

                            //console.log(this.now_stage_json);


                            //暂停音频

                            this.audio_pause();


                            this.box1_model = "EDIT_STAGE";


                            var obj = this.now_stage_json;

                            //console.log(obj);


                            this.obj_to_this(obj);


                            this.open_popBox1();


                        },//编辑单个关卡

                        save_edit_stage: function () {

                            var is_can_save = this.is_card_can_save();

                            //console.log(is_can_save);

                            if (is_can_save) {


                                var obj = this.this_to_obj();


                                //当该卡片是 word类型时，修改卡片时，需要替换掉相应wordchoice 的释义

                                if (obj.stageType == 'word') {
                                    //alert(obj.learn_word);
                                    //alert(obj.word_explain);
                                    //alert(obj.purport);

                                    var is_has_bind_exe = false;

                                    if (this.rough_draft_course_learning_stage_json.length > 0) {
                                        for (var i = 0; i < this.rough_draft_course_learning_stage_json.length; i++) {
                                            console.log(this.rough_draft_course_learning_stage_json[i]);

                                            if (this.rough_draft_course_learning_stage_json[i].learn_word == obj.learn_word) {
                                                if (this.rough_draft_course_learning_stage_json[i].stageType == "wordchoice" || this.rough_draft_course_learning_stage_json[i].stageType == "spell") {
                                                    //alert("有绑");
                                                    is_has_bind_exe = true;
                                                    break;
                                                }
                                            }
                                        }
                                    }


                                    if (is_has_bind_exe) {

                                        if (confirm("是否将该单词所有习题更换成此释义?")) {

                                            for (var i = 0; i < this.rough_draft_course_learning_stage_json.length; i++) {
                                                console.log(this.rough_draft_course_learning_stage_json[i]);

                                                if (this.rough_draft_course_learning_stage_json[i].learn_word == obj.learn_word) {
                                                    if (this.rough_draft_course_learning_stage_json[i].stageType == "wordchoice" || this.rough_draft_course_learning_stage_json[i].stageType == "spell") {
                                                        this.rough_draft_course_learning_stage_json[i].word_explain = obj.word_explain;
                                                    }
                                                }
                                            }

                                        }
                                    }


                                }


                                //当该卡片是 wordchoice类型时，修改卡片时，需要替换掉相应word 的释义。


                                if (obj.stageType == 'wordchoice' || obj.stageType == 'spell') {


                                    var is_has_bind_exe = false;

                                    if (this.rough_draft_course_learning_stage_json.length > 0) {
                                        for (var i = 0; i < this.rough_draft_course_learning_stage_json.length; i++) {
                                            console.log(this.rough_draft_course_learning_stage_json[i]);

                                            if (this.rough_draft_course_learning_stage_json[i].learn_word == obj.learn_word) {
                                                if (this.rough_draft_course_learning_stage_json[i].stageType == "word") {
                                                    //alert("有绑");
                                                    is_has_bind_exe = true;
                                                    break;
                                                }
                                            }
                                        }
                                    }


                                    if (is_has_bind_exe) {

                                        if (confirm("是否将单词卡更换成此释义?")) {

                                            for (var i = 0; i < this.rough_draft_course_learning_stage_json.length; i++) {
                                                console.log(this.rough_draft_course_learning_stage_json[i]);

                                                if (this.rough_draft_course_learning_stage_json[i].learn_word == obj.learn_word) {
                                                    if (this.rough_draft_course_learning_stage_json[i].stageType == "word") {
                                                        this.rough_draft_course_learning_stage_json[i].word_explain = obj.word_explain;
                                                    }
                                                }
                                            }

                                        }
                                    }


                                }


                                /*
                                 * 替换掉当前K的obj
                                 * */


                                this.rough_draft_course_learning_stage_json.splice(this.pageindex, 1, obj);


                                //初始化关卡

                                if (this.rough_draft_course_learning_stage_json.length > 0) {
                                    this.now_stage_json = this.rough_draft_course_learning_stage_json[this.pageindex];
                                }


                                this.close_popBox1();//关闭弹窗1 数据重置


                            } else {
                                $$alert("提示", "缺少必填项", function () {
                                });
                            }

                        },//保存编辑关卡

                        set_bindKnowledge: function () {

                            this.open_popBox2();//打开窗口2
                        },

                        is_binded: function (stage, key) {


                            //绑定知识点存在
                            if (this.bindKnowledge) {

                                var bindKnowledge_key_group = [];//绑定知识点key 组

                                var begin_key = 0;//开始key


                                //找出绑定知识点开始的那个key
                                for (let i = 0; i < this.rough_draft_course_learning_stage_json.length; i++) {

                                    if (this.rough_draft_course_learning_stage_json[i].purport == this.bindKnowledge) {
                                        begin_key = i;
                                        break;
                                    }

                                }

                                //根据行走步数，找出后面的key

                                for (let j = begin_key; j <= begin_key + this.review_go_num - 1; j++) {
                                    bindKnowledge_key_group.push(j);
                                }


                                //遍历bindKnowledge_key_group 看看当前key 是否有

                                for (let k = 0; k < bindKnowledge_key_group.length; k++) {
                                    if (key == bindKnowledge_key_group[k]) {
                                        return true;
                                    }
                                }


                            }


                            return false;

                        },

                        choose_knowledge: function (stage, key) {


                            if (this.bindKnowledge) {
                                //已经有绑定知识点的情况下


                                //检查是否已经绑定过
                                if (!this.is_binded(stage, key)) {

                                    //检查前一个关卡是否绑定过

                                    if (this.is_binded(this.rough_draft_course_learning_stage_json[key - 1], key - 1)) {
                                        this.review_go_num += 1;
                                    }

                                }


                            } else {

                                //没有绑定知识点，直接赋值purport
                                this.bindKnowledge = stage.purport;

                                this.review_go_num = 1;
                            }


                            console.log("绑定知识点：" + this.bindKnowledge);
                            console.log("行走步数：" + this.review_go_num);
                        },


                        reset_choosed: function () {
                            this.bindKnowledge = "";
                            this.review_go_num = 0;

                            console.log("绑定知识点：" + this.bindKnowledge);
                            console.log("行走步数：" + this.review_go_num);
                        },

                        json_deep_copy: function (json) {

                            if (typeof json == 'number' || typeof json == 'string' || typeof json == 'boolean') {
                                return json;
                            } else if (typeof json == 'object') {
                                if (json instanceof Array) {
                                    var newArr = [], i, len = json.length;
                                    for (i = 0; i < len; i++) {
                                        newArr[i] = arguments.callee(json[i]);
                                    }
                                    return newArr;
                                } else {
                                    var newObj = {};
                                    for (var name in json) {
                                        newObj[name] = arguments.callee(json[name]);
                                    }
                                    return newObj;
                                }
                            }

                        },//json复制


                        save_to_course_learning_stage_json: function () {


                            //草稿保存


                            this.course_learning_stage_json = [];//课程流程json

                            var save_json = [];//通讯专用课程流程joson


                            var _this = this;


                            /*
                             *
                             * 深度复制 rough_draft_course_learning_stage_json
                             *
                             * */


                            for (let i = 0; i < this.rough_draft_course_learning_stage_json.length; i++) {

                                var obj = this.rough_draft_course_learning_stage_json[i];


                                this.course_learning_stage_json.push(this.json_deep_copy(obj));

                                save_json.push(this.json_deep_copy(obj));

                            }


                            /*
                             *
                             *
                             * encodeURI元素内容
                             *
                             * */


                            var content = encodeURI(JSON.stringify(save_json));


                            /*
                             *
                             * 通讯,保存数据
                             * */

                            $.ajax({
                                url: _this.ajaxUrl,
                                type: 'post',
                                data: {
                                    action: 'save_course_learning_stage_json',
                                    post_id: _this.post_id,
                                    content: content
                                },
                                success: function (data, status) {
                                    var res = data.toString().trim();
                                    console.log(res);

                                }
                            });


                        },

                        forward_2_s: function () {

                            var time = 0;


                            if (this.course_audio.currentTime >= this.course_audio.duration - 2) {
                                time = this.course_audio.duration;
                            } else {
                                time = this.course_audio.currentTime + 2;
                            }


                            this.adjust_audio_currentTime(time);//设置时间


                        },


                        back_2_s: function () {


                            var time = 0;//设定的时间

                            if (this.course_audio.currentTime <= 2) {
                                time = 0
                            } else {
                                time = this.course_audio.currentTime - 2;
                            }


                            this.adjust_audio_currentTime(time);//设置时间


                        },

                        get_english_sentence_no_dot: function (str) {


                            var reg = new RegExp(/\,/, "g");
                            str = str.replaceAll(reg, ' ');

                            reg = new RegExp(/\./, "g");
                            str = str.replaceAll(reg, ' ');

                            reg = new RegExp(/\"/, "g");
                            str = str.replaceAll(reg, ' ');

                            reg = new RegExp(/\?/, "g");
                            str = str.replaceAll(reg, ' ');

                            reg = new RegExp(/\!/, "g");
                            str = str.replaceAll(reg, ' ');

                            reg = new RegExp(/\:/, "g");
                            str = str.replaceAll(reg, ' ');

                            reg = new RegExp(/\;/, "g");
                            str = str.replaceAll(reg, ' ');

                            reg = new RegExp(/\(/, "g");
                            str = str.replaceAll(reg, ' ');

                            reg = new RegExp(/\)/, "g");
                            str = str.replaceAll(reg, ' ');


                            /*
                             * 将多空格变为单空格
                             * */


                            str = str.replace(/\s+/g, " ");


                            /*
                             * 去掉收尾空格
                             * */

                            str = str.trim();

                            return str;

                            // console.log("句子演变："+str);


                            //var strarr=str.split(" ");
                            // console.log(strarr);


                            /* reg = new RegExp("     ", "g");
                             str = str.replaceAll(reg, ' ');

                             reg = new RegExp("    ", "g");
                             str = str.replaceAll(reg, ' ');

                             reg = new RegExp("   ", "g");
                             str = str.replaceAll(reg, ' ');

                             reg = new RegExp("  ", "g");
                             str = str.replaceAll(reg, ' ');



                             var strarr=str.split(" ");
                             console.log(strarr);*!/

                             /!*  str = str.trim();


                             return str;*!/*/

                        },


                        get_english_sentence_has_dot: function (str) {


                            var reg = new RegExp(/\,/, "g");
                            str = str.replaceAll(reg, ' \, ');

                            reg = new RegExp(/\./, "g");
                            str = str.replaceAll(reg, ' \. ');

                            reg = new RegExp(/\"/, "g");
                            str = str.replaceAll(reg, ' \" ');

                            reg = new RegExp(/\?/, "g");
                            str = str.replaceAll(reg, ' \? ');

                            reg = new RegExp(/\!/, "g");
                            str = str.replaceAll(reg, ' \! ');

                            reg = new RegExp(/\:/, "g");
                            str = str.replaceAll(reg, ' \: ');

                            reg = new RegExp(/\;/, "g");
                            str = str.replaceAll(reg, ' \; ');

                            reg = new RegExp(/\(/, "g");
                            str = str.replaceAll(reg, ' \( ');

                            reg = new RegExp(/\)/, "g");
                            str = str.replaceAll(reg, ' \) ');

                            /*
                             * 将多空格变为单空格
                             * */


                            str = str.replace(/\s+/g, " ");


                            /*
                             * 去掉收尾空格
                             * */


                            str = str.trim();


                            return str;

                        },


                        data_reset: function () {

                            this.stageType = "begin";//卡片类型
                            this.classifyType = "course";//大分类  course 课程 exercise


                            this.timestamp = "";//时间戳


                            this.headline = "";//课文标题
                            this.purport = "";//主旨，用来绑定知识点
                            this.showtext_1 = "";
                            this.showtext_2 = "";
                            this.cat_name = "";//课程名
                            this.brand_name = "";//品牌名
                            this.subhead = "";
                            this.img = "";//图片


                            this.english_sentence = "";//英文句子
                            this.chinese_sentence = "";//中文句子

                            this.english_sentence_has_dot = "";
                            this.english_sentence_no_dot = "";

                            this.explain_1 = "";//讲解1
                            this.explain_2 = "";//讲解2
                            this.explain_3 = "";//讲解3
                            this.explain_4 = "";//讲解4

                            this.learn_word = "";//单词
                            this.word_explain = "";//释义
                            this.word_phonetic = "";//音标

                            this.question_stem_1 = '';//题干1
                            this.question_stem_2 = '';//题干2
                            this.option_1 = "";//选项1
                            this.option_2 = "";//选项2
                            this.option_3 = "";//选项3
                            this.right_option = "";//正确选项


                            this.bindKnowledge = "";//绑定知识点
                            this.review_go_num = 0;//行走步数

                            this.rich_text = "";//富文本


                            this.current_edit_card_key = "";


                            this.clear_rich_editor();//清空富文本框

                            /*
                             * 非关卡数据
                             * */

                            this.push_post_id = "";

                            this.setect_push_post_result = 0;


                        },//创建关卡的各项数据重置

                        import_rough_draft_course_learning_stage_json: function () {

                            this.box1_model = "IMPORT_AND_OUTPORT_JSON";

                            this.open_popBox1();


                        },


                        push_data: function () {

                            this.box1_model = "PUSH_DATA";

                            this.open_popBox1();


                        },

                        select_push_post: function () {
                            //alert("AJAX查询文章信息");

                            var _this = this;

                            $$loading("加载中");


                            $.ajax({
                                url: _this.ajaxUrl,
                                type: 'post',
                                data: {action: 'select_push_post_mes', post_id: _this.push_post_id},
                                success: function (data, status) {
                                    var res = data.toString().trim();
                                    console.log(res);

                                    _this.setect_push_post_result = res;

                                    _this.setect_push_post_result = _this.setect_push_post_result.slice(0, _this.setect_push_post_result.length - 1)

                                    $$closeLoading();


                                }

                            });


                        },


                        push_post: function () {


                            if (confirm("将课程数据推送到目标课程，将替换掉目标课程音频以及其原始数据，是否继续？")) {





                                /*
                                 *
                                 *
                                 * 通讯课程内容
                                 * */


                                this.course_learning_stage_json = [];//课程流程json

                                var save_json = [];//通讯专用课程流程joson


                                var _this = this;


                                /*
                                 *
                                 * 深度复制 rough_draft_course_learning_stage_json
                                 *
                                 * */


                                for (let i = 0; i < this.rough_draft_course_learning_stage_json.length; i++) {

                                    var obj = this.rough_draft_course_learning_stage_json[i];


                                    this.course_learning_stage_json.push(this.json_deep_copy(obj));

                                    save_json.push(this.json_deep_copy(obj));

                                }


                                /*
                                 *
                                 *
                                 * encodeURI元素内容
                                 *
                                 * */


                                var content = encodeURI(JSON.stringify(save_json));


                                /*
                                 *
                                 * 通讯,保存数据
                                 * */


                                $.ajax({
                                    url: _this.ajaxUrl,
                                    type: 'post',
                                    data: {
                                        action: 'save_course_learning_stage_json',
                                        post_id: _this.push_post_id,
                                        content: content
                                    },
                                    success: function (data, status) {
                                        var res = data.toString().trim();
                                        console.log(res);


                                    }
                                });


                                $.ajax({
                                    url: _this.ajaxUrl,
                                    type: 'post',
                                    data: {
                                        action: 'save_audio_src_two',
                                        post_id: _this.push_post_id,
                                        course_audio_src: _this.course_audio_src
                                    },
                                    success: function (data, status) {
                                        var res = data.toString().trim();
                                        console.log(res);


                                    }
                                });


                            }


                        },


                        copy_rough_draft_course_learning_stage_json: function () {
                            $("#input_course_json")[0].select();
                            document.execCommand('copy');
                            alert("复制成功");


                        },


                        save_rough_draft_course_learning_stage_json: function () {

                            var obj = $("#input_course_json").val();


                            if (!obj) {
                                $("#input_course_json").val("[]");
                                this.rough_draft_course_learning_stage_json = [];
                            } else {
                                this.rough_draft_course_learning_stage_json = JSON.parse(decodeURI(obj));
                            }


                            alert("保存成功");


                        }


                    },

                    computed: {

                        show_json: function () {


                            var save_json = [];//通讯专用课程流程joson


                            var _this = this;


                            /*
                             *
                             * 深度复制 rough_draft_course_learning_stage_json
                             *
                             * */


                            for (let i = 0; i < this.rough_draft_course_learning_stage_json.length; i++) {

                                var obj = this.rough_draft_course_learning_stage_json[i];


                                save_json.push(this.json_deep_copy(obj));

                            }


                            /*
                             *
                             *
                             * encodeURI元素内容
                             *
                             * */


                            return encodeURI(JSON.stringify(save_json));
                        }

                    },

                    watch: {
                        is_course_audio_playing: function (n, o) {


                            //console.log(this.timestamp_to_audio_time(this.course_audio.currentTime));


                        }
                        ,


                        course_card_base_json: function (n, o) {

                            console.log("卡片库已改变");

                            console.log(n);

                            //console.log(encodeURI(JSON.stringify(n)));


                            var _this = this;
                            var content = "";

                            //将course_card_base_json数组转为json 并进行encodeURI 处理 生成content 可直接保存数据库。

                            if (n.length > 0) {

                                content = encodeURI(JSON.stringify(n));
                            }


                            /*
                             *  静默更新数据库内容主动通讯
                             * */


                            $.ajax({
                                url: _this.ajaxUrl,
                                type: 'post',
                                data: {action: 'alter_course_card_base', post_id: _this.post_id, content: content},
                                success: function (data, status) {
                                    var res = data.toString().trim();
                                    //console.log(res);

                                }
                            });


                        }
                        ,

                        english_sentence: function (n, o) {

                            if (n) {

                                var reg = /[\u3002|\uff1f|\uff01|\uff0c|\u3001|\uff1b|\uff1a|\u201c|\u201d|\u2018|\u2019|\uff08|\uff09|\u300a|\u300b|\u3008|\u3009|\u3010|\u3011|\u300e|\u300f|\u300c|\u300d|\ufe43|\ufe44|\u3014|\u3015|\u2026|\u2014|\uff5e|\ufe4f|\uffe5]/;


                                if (reg.test(n)) {
                                    alert("禁止输入中文字符！请检查原内容！");
                                    this.english_sentence = o;


                                } else {

                                    this.english_sentence_no_dot = this.get_english_sentence_no_dot(n);
                                    this.english_sentence_has_dot = this.get_english_sentence_has_dot(n);
                                }

                            }


                        },


                        pageindex: function (n, o) {


                            this.now_stage_json = {
                                "stageType": "blank"
                            };

                            var _this = this;


                            window.setTimeout(function () {


                                _this.now_stage_json = _this.rough_draft_course_learning_stage_json[n];


                            }, 100);


                        }

                    }
                })
                ;


            $("title")[0].innerHTML = container.post_title + "-关卡设计";

            container.audio_initialization();


        </script>


        <?php


        get_footer();

    } else {
        echo "课程ID错误";

    }


} else {

    echo "权限不足";
}

?>


<!--<div id="app">
    <el-row>
        <el-col :span="24">
            <el-container>
                <el-aside >Aside</el-aside>
                <el-container>
                    <el-header>Header</el-header>
                    <el-main>Main</el-main>
                </el-container>
            </el-container>

        </el-col>
    </el-row>
</div>




<script>
    new Vue({
        el: '#app',
        data: function() {
            return { visible: false }
        }
    })
</script>
-->













