<?php

/*


Template Name Posts: test

*/


/*
 *
 * 12.15更新内容
 * 单词习题切换成选择题与单词拼写随机发生  已完成
 * 缺词填空中选项增加，难度增加  已完成
 *
 * 加载页面标语随机切换  已完成
 *
 * 记录用户错题
 *
 * 缺词填空的中文翻译增加句子读音  完成
 *
 *
 *
 * */


require_once 'course_class_0604/users_data.class.php';//用户类
require_once 'course_class_0604/courses_data.class.php';//课程类
require_once 'course_class_0604/review_plan_processing.class.php';//课程类


pcVisitHandle();

global $post;

//myDump($post);

$courses_data = new courses_Data($post);//课程对象


$cat_url = home_url() . "/?cat=" . $courses_data->post_cat_id;//课程列表 url


$today_review_url = home_url() . get_option("g_today_review_page");//今日复习url

$heika_task_url = home_url() . get_option("g_heika_task_page");//黑卡任务链接


$users_data = new users_data();//初始化用户对象


$review_plan_processing = new review_plan_processing($users_data->user_id, current_time("timestamp"), $users_data->isHeikaVip);//初始化记忆系统对象


/*
 *
 * 查看用户是不是测试用户
 * */


$is_test_user = $users_data->judge_is_test_user();//是否测试用户


/*
 *
 * 查看当前用户此课程状态
 * */


$course_status = $review_plan_processing->get_now_post_course_status($courses_data->post_id);


/*
 *
 * 查看当前用户上一节课状态
 * */


/*
 *
 * 查看当前用户此课程最大翻页
 * */


$course_maxpaged = $users_data->get_user_course_rate_of_progress($courses_data->post_id);//获取用户在此课的最大翻页


$webUrl = home_url();//网站域名


$isWeixin = (is_weixin()) ? 1 : 0;//是否为微信浏览器


$course_ajax_url = $webUrl . get_option("g_course_ajax_url");//保存学习数据的AJAX


$course_review_ajax_url = $webUrl . get_option("g_course_review_ajax_url");//记忆系统专属通讯地址


$nowTime = current_time("timestamp");


$sound_wrong_src = $webUrl . "/wp-content/uploads/2018/09/sound_wrong.mp3";
$sound_right_src = $webUrl . "/wp-content/uploads/2020/01/sound_right3.mp3";


$vipaction_text = get_option("g_vipaction_text");//VIP活动标语
$vipaction_url = get_option("g_vipaction_single_pro_url");//主推商品页


get_header();


if (!$users_data->isLogin) {

    get_login_page();//叫出登录按钮

} else {

    if ($users_data->isHeikaVip || $users_data->isOldVip || $courses_data->is_unvip_listen) {

        ?>

        <script src="<?php bloginfo('template_url'); ?>/js/NoSleep.js?v0.1" type="text/javascript"></script>


        <style>

            [v-cloak] {
                display: none;
            }

            #container {
                width: 100%;
                height: 100%;
                position: fixed;
                top: 0;
                left: 0;

            }

            .loadingPage {
                width: 100%;
                height: 100%;
                position: fixed;
            }

            .loadingPage_body {
                width: 100%;
                height: 75%;
                background: #364B9A;
                float: left;

            }

            .loadingPage_body_logo_area {
                width: 100%;
                height: 75%;
                float: left;
            }

            .loadingPage_body_logo_area img:first-child {
                max-height: 50%;
                width: 120px;
                margin: 50px auto 20px;
                display: block;
                overflow: hidden;
            }

            .loadingPage_body_logo_area img:nth-child(2) {
                max-height: 10%;
                display: block;
                margin: 0px auto 10px;
                width: 100px;
                overflow: hidden;
            }

            .loadingPage_body_logo_area p {
                color: #FED130;
                width: 100%;
                height: 20px;
                line-height: 20px;
                text-align: center;
                font-size: 12px;
                max-height: 10%;
                float: left;
            }

            .loadingPage_body_progress_area {
                width: 100%;
                height: 25%;
                background: #364B9A;
                float: left;
            }

            .loadingPage_body_progress_num {
                width: 70%;
                margin-left: 15%;
                color: white;
            }

            .loadingPage_body_progress_num span {
                margin-right: 88%;
                display: block;
                text-align: right;
                font-size: 14px;
            }

            .loadingPage_body_progress {
                width: 70%;
                height: 24%;
                margin-left: 15%;
                background: url(/img/newcourse/<EMAIL>);
                background-size: 100% 100%;
                background-repeat: no-repeat;
            }

            .loadingPage_body_progress > div {
                width: 12%;
                overflow: hidden;
                height: 100%;
            }

            .loadingPage_body_progress > div > div {
                width: 100%;
                height: 100%;
                background: url(/img/newcourse/<EMAIL>);
                background-size: 100% 100%;
                background-repeat: no-repeat;
                border-radius: 50px;

            }

            .loadingPage_foot {
                width: 100%;
                height: 25%;
                background: #364B9A;
                float: left;
                color: white;
                font-size: 0.8em;
                text-align: center;
            }

            .loadingPage_foot div {
                text-align: center;
            }

            .loadingPage_foot img {
                width: 18px;
                height: 18px;
                display: block;
                float: left;
            }

            .loadingPage_foot div:first-child {
                width: 100px;
                font-size: 16px;
                height: 20px;
                line-height: 20px;
                margin: 10px auto;
            }

            .loadingPage_foot > p {

                width: 80%;
                margin-left: 10%;
                font-size: 1em;
                text-align: center;

            }

            .version {
                width: 40%;
                font-size: 0.8em;
                position: fixed;
                bottom: 5px;
                left: 30%;
                color: white;
                height: 30px;
                line-height: 30px;
                text-align: center;
            }

            .slide-left-enter-active {
                transition: left .2s ease;
            }

            .slide-left-enter {

                left: 100%;
            }

            .slide-left-enter-to {

                left: 0;
            }

            .slide-left-leave {
                opacity: 1;
            }

            .slide-left-leave-to {
                opacity: 0;
            }

            .slide-left-leave-active {
                transition: opacity .1s ease;
            }

            .slide-right-enter-active {
                transition: left .2s ease;
            }

            .slide-right-enter {

                left: -100%;
            }

            .slide-right-enter-to {

                left: 0;
            }

            .slide-right-leave {
                opacity: 1;
            }

            .slide-right-leave-to {
                opacity: 0;
            }

            .slide-right-leave-active {
                transition: opacity .1s ease;

            }

            .fade-in-enter {

                opacity: 0;
            }

            .fade-in-enter-to {

                opacity: 1;
            }

            .fade-in-leave {
                opacity: 1;
            }

            .fade-in-leave-to {
                opacity: 0;
            }

            .fade-in-leave-active, .fade-in-enter-active {
                transition: opacity 1s ease;

            }

            .coursePage {
                width: 100%;
                height: 100%;
                position: fixed;
            }

            .coursePage_progress {
                width: 100%;
                height: 1%;
                background: #e8e8e8;
                float: left;
                position: absolute;
                top: 0;
                left: 0;
            }

            .coursePage_progress_has_go {
                width: 0%;
                height: 100%;
                background: darkgray;
            }

            .courseContent {
                width: 100%;
                height: 79%;
                background: #F4F8FB;
                /* background-color: #0000cc;*/
                position: absolute;
                top: 1%;
                left: 0;
            }

            .courseContent > div {
                width: 100%;
                height: 100%;
                position: absolute;
            }

            .course_card_1 {
                width: 92%;
                height: 92%;
                left: 4%;
                top: 4%;
                border-radius: 20px;
                box-shadow: 0px 0px 5px lightgrey;
                border: 1px solid #e8e8e8;
                position: absolute;
                background: white;
            }

            .course_card_1 > img {
                width: 60%;
                margin: 20% auto;
                display: block;
            }

            .course_card_1_body {
                width: 100%;
                background-color: white;
                border-top-left-radius: 20px;
                border-top-right-radius: 20px;
                float: left;
                position: relative;
                max-height: 70%;
                overflow: hidden;

            }

            .course_card_1_body_trademark {
                width: 60%;
                color: white;
                font-size: 1.0em;
                font-family: "黑体";
                font-weight: 500;
                text-align: center;
                position: absolute;
                top: 10%;
                left: 20%;
                max-height: 10%;

            }

            .course_card_1_body_title {
                width: 80%;
                color: white;
                font-size: 1.8em;
                font-family: "黑体";
                font-weight: 600;
                text-align: center;
                position: absolute;
                top: 20%;
                left: 10%;
            }

            .course_card_1_body > img {
                width: 100%;

                border-top-left-radius: 20px;
                border-top-right-radius: 20px;
            }

            .course_card_1_foot {
                width: 100%;
                height: 30%;
                background-color: white;
                border-bottom-left-radius: 20px;
                border-bottom-right-radius: 20px;
                float: left;

            }

            .course_card_1_foot > p {
                width: 90%;
                margin-left: 5%;
                text-align: center;
                font-size: 0.9em;
                color: gray;

                margin-top: 3%;

            }

            .course_card_1_foot > p:first-child {

                margin-top: 5%;
                color: #364B9A;
                font-size: 1.1em;
            }

            .course_card_2 {
                width: 92%;
                height: 92%;
                left: 4%;
                top: 4%;
                position: absolute;
            }

            .course_card_2_top {
                width: 100%;
                height: 28%;
                background: url(/img/newcourse/<EMAIL>);
                background-size: 100% 100%;
                background-repeat: no-repeat;
                position: relative;
                /*background-color: white;

                border-bottom-left-radius: 20px;
                border-bottom-right-radius: 20px;
                float: left;*/
            }

            .course_card_2_top div {
                width: 80%;
                height: 60%;
                top: 25%;
                left: 10%;
                display: block;
                text-align: center;
                font-size: 22px;
                position: absolute;
                line-height: 250%;
                color: #364B9A;
                font-weight: 400;
            }

            .course_card_2_body {
                width: 100%;
                height: 71%;
                background: url(/img/newcourse/<EMAIL>);
                background-size: 100% 100%;
                background-repeat: no-repeat;
                position: relative;
                text-align: justify;
                word-wrap: break-word !important;
                word-break: break-all;
                white-space: normal;
            }

            .course_card_2_body > div:first-child {
                width: 100%;
                margin-top: 7%;
                float: left;
                font-size: 0.8em;
                color: #364B9A;

            }

            .course_card_2_body > div:nth-child(2) {
                width: 100%;
                margin-top: 5%;
                float: left;
                font-size: 0.8em;

            }

            .course_card_2_body div p {
                width: 86%;
                margin-left: 7%;
                margin-top: 2%;
            }

            .course_card_3 {

                width: 92%;
                height: 92%;
                left: 4%;
                top: 4%;
                border-radius: 20px;
                box-shadow: 0px 0px 5px lightgrey;
                border: 1px solid #e8e8e8;
                position: absolute;
                background: white;

            }

            .course_card_3_img {

                width: 100%;
                max-height: 50%;

                background-color: white;

                border-top-left-radius: 20px;
                border-top-right-radius: 20px;
                float: left;
                position: relative;
                overflow: hidden;

            }

            .course_card_3_img > img {
                width: 100%;

                border-top-left-radius: 20px;
                border-top-right-radius: 20px;
            }

            .course_card_3_body {
                width: 90%;
                float: left;
                max-height: 45%;
                margin-left: 5%;
                margin-top: 6%;

            }

            .course_card_3_head {
                width: 90%;
                margin-left: 2%;
                color: #00b9eb;
                font-size: 1.1em;
                font-weight: 600;
                border-bottom: 1px solid #e8e8e8;
                padding-top: 2%;
                padding-bottom: 2%;
            }

            .course_card_3_explain {
                font-size: 0.9em;
            }

            .course_card_3_explain p {
                margin-top: 2%;
                color: gray;

            }

            .course_card_4 {
                width: 92%;
                height: 92%;
                left: 4%;
                top: 4%;
                border-radius: 20px;
                box-shadow: 0px 0px 5px lightgrey;
                border: 1px solid #e8e8e8;
                position: absolute;
                background: white;
            }

            .course_card_4_body {
                width: 100%;
                height: 60%;
                float: left;
            }

            .course_card_4_body p {
                width: 100%;
                text-align: center;
                float: left;
            }

            .course_card_4_body p:first-child {
                height: 40px;
                line-height: 40px;
                font-size: 24px;
                text-align: center;
                font-weight: bold;
                max-height: 30%;
                margin-top: 20%;;
            }

            .course_card_4_body p:nth-child(3) {
                height: 50px;
                line-height: 50px;
                font-size: 14px;
                text-align: center;
                max-height: 30%;
                color: #8ACAE3;
                font-style: italic;

            }

            .course_card_4_body p:nth-child(2) {
                height: 22px;
                line-height: 22px;
                font-size: 16px;
                text-align: center;
                max-height: 30%;
                color: darkgrey;

            }

            .course_card_4_bottom {
                width: 100%;
                height: 40%;
                float: left;

            }

            .course_card_4_bottom p {
                width: 80%;
                height: 20px;
                line-height: 20px;
                font-size: 0.9em;
                color: gray;
                margin-top: 20px;
                margin-left: 10%;
                text-align: justify;
                word-wrap: break-word !important;
                word-break: break-all;
                white-space: normal;
            }

            .coursePlayPannel {

                width: 100%;
                height: 20%;
                background: #364B9A;
                float: left;
                color: white;
                font-size: 0.8em;
                text-align: center;
                position: absolute;
                left: 0;
                bottom: 0;

            }

            .skip_btn {
                width: 70px;
                height: 30px;

                line-height: 30px;
                color: #364B9A;
                text-align: center;
                cursor: pointer;
                border-radius: 5px;
                font-size: 10px;
                overflow: hidden;
                position: fixed;

                border: 1px solid #364B9A;
                top: 100px;
                right: -11px;
                opacity: 0.5;

                background: white;

            }

            .skip_btn div {
                width: 29px;
                height: 29px;
                float: left;
                font-weight: bold;
                line-height: 29px;
                text-align: center;

            }

            .ask_teacher_btn {
                width: 70px;
                height: 30px;

                line-height: 30px;
                color: #364B9A;
                text-align: center;
                cursor: pointer;
                border-radius: 5px;
                font-size: 10px;
                overflow: hidden;
                position: fixed;

                border: 1px solid #364B9A;
                top: 150px;
                right: -11px;
                opacity: 0.5;

                background: white;

            }

            .ask_teacher_btn div {
                width: 58px;
                height: 29px;
                float: left;
                line-height: 29px;
                text-align: center;

            }

            .slide-in-enter-active {
                animation: slide-in-in .9s;
            }

            .slide-in-leave-active {
                animation: slide-in-in .2s reverse;
            }

            @keyframes slide-in-in {
                0% {
                    right: -50px;
                }
                50% {
                    right: -3px;
                }
                100% {
                    right: -9px;
                }
            }

            .coursePlayPannel_circle_btn {

                width: 60px;
                height: 60px;
                border-radius: 30px;
                line-height: 60px;
                border: 1px solid white;
                color: white;
                text-align: center;
                cursor: pointer;
                font-size: 21px;
                overflow: hidden;
                position: absolute;
                box-shadow: 0px 0px 8px lightgrey;

            }

            .coursePlayPannel_small_btn {

                width: 60px;
                height: 30px;
                border-radius: 10px;
                line-height: 30px;

                /*color: #526ED1;*/
                text-align: center;
                cursor: pointer;
                font-size: 12px;
                overflow: hidden;
                position: absolute;
                /* background: white;*/
                background: #526ED1;
                color: white;

            }

            .coursePlayPannel_lect_btn {

                width: 110px;
                height: 50px;
                border-radius: 10px;
                line-height: 50px;
                border: 1px solid white;
                color: #fff;
                text-align: center;
                cursor: pointer;
                font-size: 1.2em;
                overflow: hidden;
                position: absolute;

            }

            .locking {
                width: 60px;
                height: 30px;
                border-radius: 10px;
                line-height: 30px;

                color: white;
                text-align: center;
                cursor: pointer;
                font-size: 12px;
                overflow: hidden;
                position: absolute;
                background: #526ED1;

            }

            .unlocking {
                width: 60px;
                height: 30px;
                border-radius: 10px;
                line-height: 30px;

                /*color: #526ED1;*/
                text-align: center;
                cursor: pointer;
                font-size: 12px;
                overflow: hidden;
                position: absolute;
                /* background: white;*/
                background: #526ED1;
                color: white;
            }

            .playbackspeed {
                width: 60px;
                height: 30px;
                border-radius: 10px;
                line-height: 30px;

                text-align: center;
                cursor: pointer;
                font-size: 12px;
                overflow: hidden;
                position: absolute;

                background: #526ED1;
                color: white;

            }

            .exePage {
                width: 100%;
                height: 100%;
                position: fixed;
                background-color: #364B9A;
            }

            .exeArea {
                width: 100%;
                height: 100%;
                position: absolute;
                color: white;
            }

            .lack_body {
                width: 90%;
                height: 50%;
                float: left;
                margin-left: 5%;

            }

            .lack_body_img {
                width: 100%;
                /* height: 50%; */
                /* margin-left: 20%; */
                display: block;
                text-align: center;
                font-size: 1.5em;
                /* position: absolute; */
                line-height: 300%;
                font-weight: 400;
                float: left;
                max-height: 50%;
                overflow: hidden;
            }

            .lack_body_img img {

                width: 60%;
                display: block;
                margin-left: 20%;
                max-height: 80%;
                float: left;
                margin-top: 5%;
            }

            .lack_body_type {
                width: 100%;
                height: 20%;
                display: block;
                text-align: center;
                font-size: 22px;
                line-height: 300%;
                font-weight: 400;
                float: left;
            }

            .lack_body_chinese {
                width: 100%;
                font-size: 16px;
                float: left;
                max-height: 20%;
                margin-top: 5%;
                line-height: 30px;
                text-align: left;

            }

            .lack_body_english {
                width: 100%;
                font-size: 14px;
                float: left;
                margin-top: 5%;
                max-height: 50%;
            }

            .lack_body_english span {
                display: block;
                float: left;
                height: 16px;
                margin-right: 2%;
                margin-top: 10px;
                text-align: center;
            }

            .lack_body_english img {
                width: 24px;
                height: 24px;
                float: left;
                margin-right: 3%;
                margin-top: 8px;
            }

            .lack_right_wrong {
                width: 90%;
                float: left;
                margin-left: 5%;
                height: 200px;
                line-height: 200px;
                text-align: center;
                font-size: 100px;
            }

            .lack_word_pool {
                width: 90%;
                height: 30%;
                float: left;
                margin-left: 5%;

            }

            .lack_word_pool span {
                font-size: 0.8em;
                display: block;
                float: left;
                margin-right: 3%;
                margin-top: 3%;
                text-align: center;
                padding: 2% 4%;
                background: #526ED1;
                border-radius: 2px;
            }

            .lack_answer {
                width: 90%;
                height: 30%;
                float: left;
                margin-left: 5%;

            }

            .lack_answer div:first-child span {
                background-color: white;
                color: #526ED1;
                padding: 1% 2%;
                border-radius: 5px;
                font-size: 1em;
            }

            .lack_answer div:nth-child(2) {
                margin-top: 5%;

            }

            .spell_body {
                width: 90%;
                height: 50%;
                float: left;
                margin-left: 5%;

            }

            .spell_body_type {
                width: 100%;
                height: 20%;
                display: block;
                text-align: center;
                font-size: 22px;
                line-height: 300%;
                font-weight: 400;
                float: left;
            }

            .spell_body_chinese {
                width: 100%;
                font-size: 16px;
                float: left;
                max-height: 20%;
                margin-top: 5%;
                line-height: 30px;
                text-align: center;
                font-style: italic;

            }

            .spell_body_english {
                width: 100%;
                font-size: 26px;
                float: left;
                margin-top: 20%;
                max-height: 50%;
                text-align: center;
                font-weight: bold;
            }

            .spell_body_english span {
                display: block;
                float: left;
                height: 18px;
                margin-top: 10px;
                text-align: center;
            }

            .spell_body_english img {
                width: 24px;
                height: 24px;
                float: left;
                margin-right: 3%;
                margin-top: 8px;
            }

            .spell_voice {
                width: 100%;
                float: left;
                height: 20%;
                margin-top: 5%;
                display: block;

            }

            .spell_voice button {
                margin: 0px auto;
                width: 80px;
                height: 40px;
                display: block;
                background: #364B9A;
                border: 0;
                border-radius: 25px;
                line-height: 40px;
                text-align: center;
                font-size: 28px;
            }

            .spell_right_wrong {
                width: 90%;
                float: left;
                margin-left: 5%;
                height: 200px;
                line-height: 200px;
                text-align: center;
                font-size: 100px;
            }

            .spell_word_pool {
                width: 90%;
                height: 40%;
                float: left;
                margin-left: 5%;

            }

            .spell_word_pool span {
                font-size: 0.8em;
                display: block;
                float: left;
                margin-right: 3%;
                margin-top: 3%;
                text-align: center;
                padding: 2% 4%;
                background: #526ED1;
                border-radius: 2px;
            }

            .spell_answer {
                width: 90%;
                height: 30%;
                float: left;
                margin-left: 5%;

            }

            .spell_answer div:first-child span {
                background-color: white;
                color: #526ED1;
                padding: 1% 2%;
                border-radius: 5px;
                font-size: 1em;
            }

            .spell_answer div:nth-child(2) {
                margin-top: 5%;

            }

            .wordchoice_body {
                width: 90%;
                height: 50%;
                float: left;
                margin-left: 5%;

            }

            .wordchoice_body_chinese {
                width: 100%;
                font-size: 16px;
                float: left;
                max-height: 20%;
                margin-top: 5%;
                line-height: 30px;
                text-align: center;
                font-style: italic;

            }

            .wordchoice_body_english {
                width: 100%;
                font-size: 26px;
                float: left;
                margin-top: 20%;
                max-height: 50%;
                text-align: center;
                font-weight: bold;
            }

            .wordchoice_foot {
                width: 90%;
                height: 60%;
                float: left;
                margin-left: 5%;

            }

            .wordchoice_foot > div {
                width: 100%;
                height: 70px;
                margin-top: 5%;
                background: #526ED1;
                float: left;
                max-height: 20%;
                font-size: 12px;

            }

            .wordchoice_foot > div > div {
                width: 90%;
                height: 70px;
                display: table;
                margin-left: 5%;
            }

            .wordchoice_foot > div > div > div {
                text-align: center;
                overflow: hidden;
                vertical-align: middle;
                display: table-cell;
                font-size: 14px;
            }

            .wordchoice_answer {
                width: 90%;
                height: 30%;
                float: left;
                margin-left: 5%;

            }

            .wordchoice_answer div:first-child span {
                background-color: white;
                color: #526ED1;
                padding: 1% 2%;
                border-radius: 5px;
                font-size: 1em;
            }

            .wordchoice_answer div:nth-child(2) {
                margin-top: 5%;

            }

            .bottom_one_button {
                width: 90%;
                height: 10%;
                float: left;
                margin-left: 5%;
                position: absolute;
                bottom: 0px;

            }

            .bottom_one_button button {
                width: 40%;
                height: 60%;
                left: 30%;
                background-color: #364B9A;
                border: 1px solid white;
                position: absolute;
                text-align: center;
                border-radius: 6px;
                font-size: 0.9em;
            }

            .bottom_two_buttons {
                width: 90%;
                height: 10%;
                float: left;
                margin-left: 5%;
                position: absolute;
                bottom: 0px;

            }

            .bottom_two_buttons button {
                width: 25%;
                height: 60%;
                bottom: 20%;

                background-color: #364B9A;
                border: 1px solid white;
                position: absolute;
                text-align: center;
                border-radius: 6px;
                font-size: 0.9em;
            }

            .bottom_two_buttons button:first-child {
                left: 15%;
            }

            .bottom_two_buttons button:nth-child(2) {
                right: 15%;
            }

            .bottom_three_buttons {
                width: 100%;
                height: 10%;
                float: left;
                position: absolute;
                bottom: 0px;

            }

            .bottom_three_buttons div {
                width: 33%;
                height: 100%;
                float: left;

            }

            .bottom_three_buttons div button {
                width: 70%;
                height: 56%;
                background-color: #364B9A;
                border: 1px solid white;
                text-align: center;
                border-radius: 6px;
                font-size: 0.9em;
                margin-left: 15%;
            }

            .spell_delete_button {
                width: 90%;
                height: 10%;
                float: left;
                margin-left: 5%;
                position: relative;

            }

            .spell_delete_button button {
                width: 25%;
                height: 60%;
                bottom: 20%;
                background-color: #364B9A;
                border: 1px solid white;
                position: absolute;
                text-align: center;
                border-radius: 6px;
                font-size: 0.9em;
            }

            .spell_delete_button button:first-child {
                left: 15%;
            }

            .spell_delete_button button:nth-child(2) {
                right: 15%;
            }

            .reviewPage {
                width: 100%;
                height: 100%;
                position: fixed;
            }

            .reviewContent {
                width: 100%;
                height: 79%;
                background: #F4F8FB;
                /* background-color: #0000cc;*/
                float: left;
                position: absolute;
            }

            .fade-enter-active, .fade-leave-active {
                transition: opacity .8s;
            }

            .fade-enter, .fade-leave-to /* .fade-leave-active below version 2.1.8 */
            {
                opacity: 0;
            }

            .choice_body {
                width: 90%;
                height: 30%;
                float: left;
                margin-left: 5%;

            }

            .choice_body_img {
                width: 100%;
                /* height: 50%; */
                /* margin-left: 20%; */
                display: block;
                text-align: center;
                font-size: 1.5em;
                /* position: absolute; */
                line-height: 300%;
                font-weight: 400;
                float: left;
                max-height: 50%;
                overflow: hidden;
            }

            .choice_body_img img {

                width: 60%;
                display: block;
                margin-left: 20%;
                max-height: 80%;
                float: left;
                margin-top: 5%;
            }

            .choice_body_type {
                width: 100%;
                height: 30%;

                display: block;
                text-align: center;
                font-size: 22px;

                line-height: 300%;
                font-weight: 400;
                float: left;
            }

            .choice_body_stem_2 {
                width: 100%;
                font-size: 16px;
                float: left;
                margin-top: 5%;

            }

            .choice_body_stem_1 {
                width: 100%;
                font-size: 16px;
                float: left;
                margin-top: 8%;

            }

            .choice_foot {
                width: 90%;
                height: 60%;
                float: left;
                margin-left: 5%;

            }

            .choice_foot > div {
                width: 100%;
                height: 70px;
                margin-top: 20px;
                background: #526ED1;
                float: left;
                max-height: 24%;
                font-size: 12px;

            }

            .choice_foot > div > div {
                width: 90%;
                height: 70px;
                display: table;
                margin-left: 5%;
            }

            .choice_foot > div > div > div {
                text-align: center;
                overflow: hidden;
                vertical-align: middle;
                display: table-cell;
                font-size: 14px;
            }

            .voice_body {
                width: 90%;
                height: 50%;
                float: left;
                margin-left: 5%;

            }

            .voice_body_img {
                border-radius: 6px;
                background: url(/wp-content/uploads/2019/03/yy-3.jpg);
                background-size: 100% 100%;
                background-repeat: no-repeat;
                width: 200px;
                margin: 10% auto 5% auto;
                height: 120px;
                display: block;
                overflow: hidden;
                position: relative;
                max-height: 40%;
            }

            .play_ori_voice {
                width: 50px;
                height: 50px;
                position: absolute;
                text-align: center;
                background: grey;
                line-height: 50px;
                border-radius: 25px;
                left: 75px;
                top: 35px;
                font-size: 20px;
                opacity: 0.9;
            }

            .voice_body_type {
                width: 100%;
                height: 20%;
                /* margin-left: 20%; */
                display: block;
                text-align: center;
                font-size: 1.5em;
                /* position: absolute; */
                line-height: 300%;
                font-weight: 400;
                float: left;
            }

            .voice_body_stem_2 {
                width: 100%;
                font-size: 16px;
                float: left;
                margin-top: 2%;

            }

            .voice_button {
                width: 100%;
                height: 300px;
                position: fixed;
                bottom: 0px;
                left: 0px;
                max-height: 50%;
            }

            .voice_button_text {
                width: 120px;
                height: 50px;
                line-height: 50px;
                text-align: center;
                font-size: 16px;
                margin: 0px auto;
                background-color: black;
                opacity: 0.7;
                border-radius: 5px;
            }

            .voice_button_score {
                width: 200px;
                height: 100px;
                line-height: 50px;
                text-align: center;
                /* font-size: 16px; */
                margin: 0px auto;
            }

            .voice_button_score p:first-child {
                font-weight: bold;
                font-size: 32px;
            }

            .voice_button_score p:nth-child(2) {
                font-weight: bold;
                font-size: 32px;
            }

            .voice_button_score p:nth-child(3) {
                font-weight: bold;
                font-size: 32px;
            }

            .voice_button_score p:nth-child(4) {
                font-weight: bold;
                font-size: 32px;
            }

            .voice_over_btn {
                width: 90%;
                height: 60px;
                float: left;
                margin-left: 5%;
                bottom: 10px;
                position: absolute;

            }

            .voice_over_btn button {
                width: 25%;
                height: 60%;
                bottom: 20%;
                background-color: #364B9A;
                border: 1px solid white;
                position: absolute;
                text-align: center;
                border-radius: 6px;
                font-size: 0.9em;
            }

            .voice_over_btn button:first-child {
                left: 15%;
            }

            .voice_over_btn button:nth-child(2) {
                right: 15%;
            }

            .voice_record_button {
                width: 100%;
                height: 150px;
                position: absolute;
                bottom: 0px;
                left: 0px;
                max-height: 50%;

            }

            .voice_record_button > div {
                width: 90%;
                margin-left: 5%;
                height: 150px;
            }

            .voice_record_button > div > img {
                width: 20%;
                margin: 0px auto;
                display: block;
            }

            .voice_record_button > div > p {
                width: 100%;
                text-align: center;
                font-size: 12px;
                height: 30px;
                line-height: 30px;
            }

            .voice_body_stem_1 {
                width: 100%;
                font-size: 16px;
                float: left;
                margin-top: 2%;

            }

            .voice_over_btn_2 {
                width: 90%;
                height: 60px;
                float: left;
                margin-left: 5%;
                bottom: 10px;
                position: absolute;

            }

            .voice_over_btn_2 button {
                width: 40%;
                height: 60%;
                left: 30%;
                background-color: #364B9A;
                border: 1px solid white;
                position: absolute;
                text-align: center;
                border-radius: 6px;
                font-size: 0.9em;
            }

            .overPage {
                width: 100%;
                height: 100%;

            }

            .over_page_mask_transparent {
                position: fixed;
                z-index: 1999;
                top: 0;
                right: 0;
                left: 0;
                bottom: 0;
                display: block;
                background-color: black;
                opacity: 1;
            }

            .over_page_dialog {
                width: 80%;
                height: 80%;
                position: fixed;
                z-index: 2000;
                top: 10%;
                right: 10%;
                display: block;
                background-color: white;
                border-radius: 10px;

            }

            .over_page_dialog_title {
                width: 100%;
                background: #FED545;
                color: black;
                border-top-left-radius: 10px;
                border-top-right-radius: 10px;
                display: table;
                font-size: 14px;
                max-height: 30%;
                height: 26%;

            }

            .over_page_dialog_title > div {
                width: 80%;
                margin-top: 12%;
                margin-left: 10%;
                font-size: 14px;
                text-align: center;

            }

            .over_page_dialog_body {
                width: 100%;
                height: 69%;
                float: left;
            }

            .over_page_dialog_body div {
                width: 70%;
                max-height: 15%;
                height: 40px;
                margin: 10% auto;
                background: url(/img/newcourse/<EMAIL>);
                background-size: 100% 100%;
                background-repeat: no-repeat;
                text-align: center;
                color: black;
                font-size: 14px;
                line-height: 40px;
            }

            .is_continue_page_mask {
                position: fixed;
                z-index: 1999;
                top: 0;
                right: 0;
                left: 0;
                bottom: 0;
                display: block;
                background-color: black;
                opacity: 0.8;
            }

            .is_continue_page_dialog {
                width: 80%;
                height: 60%;
                position: fixed;
                z-index: 2000;
                top: 20%;
                right: 10%;
                display: block;
                background-color: white;
                border-radius: 10px;

            }

            .is_continue_page_dialog_title {
                width: 100%;
                background: #FED545;
                color: black;
                border-top-left-radius: 10px;
                border-top-right-radius: 10px;
                display: table;
                font-size: 14px;
                max-height: 30%;
                height: 26%;

            }

            .is_continue_page_dialog_title > div {
                width: 80%;
                margin-top: 12%;
                margin-left: 10%;
                font-size: 14px;
                text-align: center;

            }

            .is_continue_page_dialog_body {
                width: 100%;
                height: 69%;
                float: left;
            }

            .is_continue_page_dialog_body div {
                width: 70%;
                max-height: 15%;
                height: 40px;
                margin: 10% auto;
                background: url(/img/newcourse/<EMAIL>);
                background-size: 100% 100%;
                background-repeat: no-repeat;
                text-align: center;
                color: black;
                font-size: 14px;
                line-height: 40px;
            }

            .ask_teacher_page_mask {
                position: fixed;
                z-index: 1999;
                top: 0;
                right: 0;
                left: 0;
                bottom: 0;
                display: block;
                background-color: black;
                opacity: 0.8;
            }

            .ask_teacher_page_dialog {
                width: 80%;
                height: 200px;
                position: fixed;
                z-index: 2000;
                top: 10%;
                right: 10%;
                display: block;
                background-color: white;
                border-radius: 10px;

            }

            .ask_teacher_page_dialog_title {
                width: 100%;
                height: 20px;
                float: left;

            }

            .ask_teacher_page_dialog_title span {
                display: block;
                width: 18px;
                height: 18px;
                float: right;
                color: darkgray;
                margin-right: 5px;
                margin-top: 3px;
            }

            .ask_teacher_page_dialog_body {
                width: 100%;
                height: 100px;
                float: left;
            }

            .ask_teacher_page_dialog_body textarea {
                padding: 8px;
                width: 90%;
                height: 90px;
                border: 1px solid #e8e8e8;
                margin-top: 5px;
                margin-left: 5%;
                font-size: 12px;
                color: darkgray;
                border-radius: 5px;
            }

            .ask_teacher_page_dialog_warning {
                width: 90%;
                height: 20px;
                margin-left: 6%;
                color: red;
                font-size: 12px;
                float: left;

            }

            .ask_teacher_page_dialog_foot {
                width: 100%;
                height: 50px;
                float: left;
            }

            .ask_teacher_page_dialog_foot button {
                width: 60%;
                margin: 7px auto;
                height: 36px;
                background: #FED130;
                text-align: center;
                color: black;
                font-size: 14px;
                line-height: 36px;
                border: 0px;
                border-radius: 10px;
                display: block;
            }

            .test_button {
                width: 50px;
                height: 50px;
                z-index: 9999;
                background: honeydew;
                top: 0%;
                position: fixed;
                border: 1px solid;
                opacity: 0.5;
            }

            @-webkit-keyframes rotation {

                from {
                    -webkit-transform: rotate(0deg);
                }

                to {
                    -webkit-transform: rotate(360deg);
                }

            }

            .ro {

                -webkit-transform: rotate(360deg);

                animation: rotation 3s linear infinite;

                -moz-animation: rotation 3s linear infinite;

                -webkit-animation: rotation 3s linear infinite;

                -o-animation: rotation 3s linear infinite;

            }

            .warning {
                background: black;
                color: white;
                height: 30px;
                width: 160px;
                opacity: 1;
                z-index: 1000;
                position: absolute;
                line-height: 30px;
                /* padding: 5px; */
                padding: 0px 10px 0px 10px;
                font-size: 12px;
                text-align: center;
                top: 68%;
                border-radius: 5px;
            }

            .top_fixed {

                width: 100%;
                height: 100px;
                max-height: 18%;
                line-height: 30px;
                color: #364B9A;
                text-align: center;
                cursor: pointer;
                font-size: 10px;
                overflow: hidden;
                position: absolute;
                /* opacity: 1; */
                background: white;
                z-index: 3000;
                top: 0px;
                left: 0px;

            }

            .top_fixed div:first-child {
                width: 95%;
                height: 72%;
                float: left;
                margin-left: 4%;
                margin-top: 4%;

            }

            .top_fixed div:first-child img {
                display: block;
                float: left;
                width: 22px;

            }

            .top_fixed div:first-child span {
                float: left;
                font-size: 14px;
                height: 30px;
                line-height: 28px;
                color: black;
                margin-left: 10px;
            }

            .top_fixed div:nth-child(2) {
                float: left;
                width: 100%;
                height: 8%;

            }

            .top_fixed div:nth-child(2) span {
                float: left;
                width: 16%;
                height: 100%;
                display: block;
                margin-left: 42%;
                background: #e8e8e8;
                border-radius: 10px;

            }

            .slide-down-enter-active {
                transition: top .2s ease;
            }

            .slide-down-enter {

                top: -50%;
            }

            .slide-down-enter-to {

                top: 0;
            }

            .slide-down-leave {
                top: 0;
            }

            .slide-down-leave-to {
                top: -50%;
            }

            .slide-down-leave-active {
                transition: top .4s ease;

            }


        </style>


        <div id="container">


            <!--now_stage_model为空时的加载页面-->


            <div class="weui-loadmore" v-show="!now_stage_model">
                <i class="weui-loading"></i>
                <span class="weui-loadmore__tips">正在加载</span>
            </div>


            <!--now_stage_model为空时的加载页面-->


            <!--加载页面开始-->
            <div class="loadingPage" v-show="now_stage_model=='load'" v-cloak>
                <div class="loadingPage_body">
                    <div class="loadingPage_body_logo_area">
                        <img src="<?php echo home_url() ?>/img/newcourse/<EMAIL>"/>
                        <img src="<?php echo home_url() ?>/img/newcourse/<EMAIL>"/>

                        <p v-if="course_status==1">复习模式</p>

                        <!--<img src="<?php /*echo home_url() */ ?>/img/newcourse/<EMAIL>"/>-->
                    </div>
                    <div class="loadingPage_body_progress_area">
                        <div class="loadingPage_body_progress_num">
                            <span ref="loadingPage_body_progress_span">12%</span>
                        </div>
                        <div class="loadingPage_body_progress">
                            <div ref="loadingPage_body_progress_img">
                                <div></div>
                            </div>
                        </div>

                    </div>

                </div>
                <div class="loadingPage_foot">
                    <div><img src="<?php echo home_url() ?>/img/newcourse/<EMAIL>"/><span>温馨提示</span></div>

                    <template v-if="course_status!=1">

                        <template v-if="loading_page_random_number<=0.3">
                            <p>本课程为有声课程，学习前请调整好手机音量。</p>
                        </template>


                        <template v-else-if="loading_page_random_number>0.3&&loading_page_random_number<=0.7">
                            <p>为了更好地使用AI记忆系统，请完整地把课程听完。</p>
                        </template>


                        <template v-else>
                            <p>本课程是"边学边练"的模式，做错题时，记得回看知识点哦！</p>
                        </template>


                    </template>

                    <template v-if="course_status==1">

                        <template v-if="loading_page_random_number<=0.3">
                            <p>复习模式以习题为主，用多样化的习题来巩固所学知识点。</p>
                        </template>


                        <template v-else-if="loading_page_random_number>0.3&&loading_page_random_number<=0.7">
                            <p>为了保证英语记忆效果，请及时完成复习任务。</p>
                        </template>


                        <template v-else>
                            <p>复习模式中，每次复习的题型和顺序会有些区别。</p>
                        </template>

                    </template>



                </div>

                <div class="version">— v.1.0.1 —</div>

            </div>
            <!--加载页面结束-->


            <!--课程页开始-->
            <div class="coursePage" v-show="now_stage_model=='course'" v-cloak>


                <!--进度条开始-->
                <div class="coursePage_progress">

                    <div class="coursePage_progress_has_go">


                    </div>

                </div>
                <!--进度条结束-->

                <!--讲课内容开始-->
                <div class="courseContent">

                    <template v-for="(course_learning_stage,key) in course_learning_stage_json">


                        <transition :name="get_slide_dir()">


                            <div v-if="is_current_stage(key)">

                                <!--begin-->
                                <div class="course_card_1" v-if="now_stage_json.stageType=='begin'">
                                    <div class="course_card_1_body">
                                        <!--<img src="https://xgn.shuimitao.online/wp-content/uploads/2018/09/timg.jpg">-->

                                        <img :src="now_stage_json.img">

                                        <div v-if="now_stage_json.brand_name" class="course_card_1_body_trademark">
                                            {{now_stage_json.brand_name}}
                                        </div>

                                        <div v-if="now_stage_json.cat_name" class="course_card_1_body_title">
                                            {{now_stage_json.cat_name}}
                                        </div>

                                    </div>
                                    <div class="course_card_1_foot">
                                        <p>{{now_stage_json.showtext_1}}</p>

                                        <p>{{now_stage_json.showtext_2}}</p>
                                    </div>
                                </div>
                                <!--begin-->

                                <!--sentence-->
                                <div class="course_card_2" v-if="now_stage_json.stageType=='sentence'">

                                    <div class="course_card_2_top">
                                        <div>{{now_stage_json.headline}}</div>
                                    </div>
                                    <div class="course_card_2_body">
                                        <div>
                                            <p>{{now_stage_json.english_sentence}}</p>

                                            <p>{{now_stage_json.chinese_sentence}}</p>
                                        </div>
                                        <div v-html='now_stage_json.rich_text'>

                                        </div>

                                    </div>

                                </div>
                                <!--sentence-->


                                <!--word-->
                                <div class="course_card_4" v-if="now_stage_json.stageType=='word'">


                                    <div class="course_card_4_body">
                                        <p>{{now_stage_json.learn_word}}</p>

                                        <p>/{{now_stage_json.word_phonetic}}/</p>

                                        <p>{{now_stage_json.word_explain}}</p>

                                    </div>
                                    <div class="course_card_4_bottom" v-html='now_stage_json.rich_text'>

                                    </div>


                                </div>
                                <!--word-->


                                <!--knowledgeHasImage-->
                                <div class="course_card_3" v-if="now_stage_json.stageType=='knowledgeHasImage'">

                                    <div class="course_card_3_img">
                                        <img :src="now_stage_json.img">
                                    </div>

                                    <div class="course_card_3_body">
                                        <div class="course_card_3_head">{{now_stage_json.headline}}</div>
                                        <div class="course_card_3_explain" v-html='now_stage_json.rich_text'>

                                        </div>
                                    </div>

                                </div>
                                <!--knowledgeHasImage-->

                                <!--knowledge-->
                                <div class="course_card_2" v-if="now_stage_json.stageType=='knowledge'">

                                    <div class="course_card_2_top">
                                        <div>{{now_stage_json.headline}}</div>
                                    </div>
                                    <div class="course_card_2_body">
                                        <div>
                                            <p></p>

                                            <p></p>
                                        </div>
                                        <div v-html='now_stage_json.rich_text'>


                                        </div>


                                    </div>

                                </div>
                                <!--knowledge-->


                                <!--listen-->
                                <div class="course_card_1" v-if="now_stage_json.stageType=='listen'">
                                    <img src="<?php home_url(); ?>/img/laba.gif">
                                </div>
                                <!--listen-->


                            </div>

                        </transition>


                    </template>
                    <!-- </template>-->


                </div>

                <!--讲课内容结束-->


                <!--播放器开始-->

                <div class="coursePlayPannel">

                    <transition name="fade">
                        <div id="locking" class="coursePlayPannel_small_btn" @click="locking_click"
                             v-show="isLocking==false">重复
                        </div>
                    </transition>

                    <transition name="fade">
                        <div id="unlocking" class="coursePlayPannel_small_btn" @click="locking_click"
                             v-show="isLocking==true">取消
                        </div>
                    </transition>


                    <div id="playbackspeed" class="coursePlayPannel_small_btn" @click="change_audio_playbackspeed">
                        {{course_audio_playbackspeed}}
                    </div>


                    <div id='playAudio' class="coursePlayPannel_circle_btn" @click="audio_play"
                         v-show="!is_course_audio_playing">
                        <span class="glyphicon glyphicon-play" aria-hidden="true"></span>
                    </div>

                    <div class="coursePlayPannel_circle_btn" id="pauseAudio" @click="audio_pause"
                         v-show="is_course_audio_playing">
                        <span class="glyphicon glyphicon-pause" aria-hidden="true"></span>
                    </div>
                </div>
                <!--播放器结束-->


            </div>
            <!--课程页结束-->


            <!--复习页开始-->
            <div class="reviewPage" v-show="now_stage_model=='review'" v-cloak>

                <!--讲课内容开始-->
                <div class="reviewContent">


                    <transition name="fade">

                        <div class="course_card_1" v-if="review_now_stage_json.stageType=='begin'">
                            <div class="course_card_1_body">
                                <!--<img src="https://xgn.shuimitao.online/wp-content/uploads/2018/09/timg.jpg">-->

                                <img :src="review_now_stage_json.img">

                                <div v-if="review_now_stage_json.brand_name" class="course_card_1_body_trademark">
                                    {{review_now_stage_json.brand_name}}
                                </div>

                                <div v-if="review_now_stage_json.cat_name" class="course_card_1_body_title">
                                    {{review_now_stage_json.cat_name}}
                                </div>

                            </div>
                            <div class="course_card_1_foot">
                                <p>{{review_now_stage_json.showtext_1}}</p>

                                <p>{{review_now_stage_json.showtext_2}}</p>
                            </div>
                        </div>


                        <div class="course_card_2" v-if="review_now_stage_json.stageType=='sentence'">

                            <div class="course_card_2_top">
                                <div>{{review_now_stage_json.headline}}</div>
                            </div>
                            <div class="course_card_2_body">
                                <div>
                                    <p>{{review_now_stage_json.english_sentence}}</p>

                                    <p>{{review_now_stage_json.chinese_sentence}}</p>
                                </div>
                                <div v-html="review_now_stage_json.rich_text">

                                </div>


                            </div>

                        </div>

                        <div class="course_card_4" v-if="review_now_stage_json.stageType=='word'">


                            <div class="course_card_4_body">

                                <p>{{review_now_stage_json.learn_word}}</p>

                                <p>/{{review_now_stage_json.word_phonetic}}/</p>

                                <p>{{review_now_stage_json.word_explain}}</p>


                            </div>
                            <div class="course_card_4_bottom" v-html="review_now_stage_json.rich_text">


                            </div>


                        </div>

                        <div class="course_card_3" v-if="review_now_stage_json.stageType=='knowledgeHasImage'">

                            <div class="course_card_3_img">
                                <img :src="review_now_stage_json.img">
                            </div>

                            <div class="course_card_3_body">
                                <div class="course_card_3_head">{{review_now_stage_json.headline}}</div>
                                <div class="course_card_3_explain" v-html="review_now_stage_json.rich_text">

                                </div>
                            </div>

                        </div>

                        <div class="course_card_2" v-if="review_now_stage_json.stageType=='knowledge'">

                            <div class="course_card_2_top">
                                <div>{{review_now_stage_json.headline}}</div>
                            </div>
                            <div class="course_card_2_body">
                                <div>
                                    <p></p>

                                    <p></p>
                                </div>
                                <div v-html="review_now_stage_json.rich_text">

                                </div>


                            </div>

                        </div>


                    </transition>


                </div>

                <!--讲课内容结束-->


                <div class="coursePlayPannel">

                    <div id="reviewText" class="coursePlayPannel_lect_btn">
                        回顾中
                    </div>


                    <div id="back_to_exe_in_reivew" @click.stop="back_to_exe" class="coursePlayPannel_small_btn">
                        返回
                    </div>


                </div>


            </div>
            <!--复习页结束-->


            <!--练习页开始-->
            <transition name="fade">
                <div class="exePage" v-show="now_stage_model=='exe'" v-cloak>

                    <!--lack-->
                    <div class="exeArea" v-show="now_stage_json.stageType=='lack'">


                        <div class="lack_body">
                            <div class="lack_body_img" v-if="now_stage_json.img"><img :src="now_stage_json.img"></div>
                            <div class="lack_body_type" v-else><span>{{now_stage_json.headline}}</span></div>
                            <div class="lack_body_chinese">{{now_stage_json.chinese_sentence}}</div>
                            <div class="lack_body_english">
                                <template v-for="word in now_blank_words_arr">
                                    <span>{{word}}</span>
                                </template>
                            </div>
                        </div>


                        <div class="lack_word_pool" v-show="answer_status=='answering'">
                            <template v-for="(word,key) in now_pool_words_arr">
                                <span class="click_word" @click.stop="lack_click_word(key,word)">{{word}}</span>
                            </template>

                        </div>

                        <div class="lack_right_wrong" v-show="answer_status=='right'||answer_status=='wrong'">
                            <span style="color:#68D778" v-show="answer_status=='right'"
                                  class="glyphicon glyphicon glyphicon-ok" aria-hidden="true"></span>
                            <span style="color:#FB667A" v-show="answer_status=='wrong'"
                                  class="glyphicon glyphicon glyphicon-remove" aria-hidden="true"></span>
                        </div>

                        <div class="lack_answer" v-show="answer_status=='go_right'||answer_status=='no_review'">
                            <div><span>正确答案</span></div>

                            <div @click.stop="go_play_lack_sentence" style="font-weight:500;font-size: 14px;"><span
                                    class="glyphicon glyphicon-volume-down"></span>&nbsp;&nbsp;{{now_stage_json.english_sentence_has_dot}}
                            </div>
                            <div style="font-size: 14px;">{{now_stage_json.chinese_sentence}}</div>
                        </div>

                        <div class="bottom_one_button" v-show="answer_status=='answering'">
                            <button @click.stop="lack_detele_word">删除</button>
                        </div>

                        <div class="bottom_two_buttons" v-show="answer_status=='go_right'">
                            <button @click.stop="exercise_restart">重做</button>
                            <button style="color:#364B9A;background:white;" @click.stop="go_to_review_knowledge">
                                回顾
                            </button>
                        </div>

                        <div class="bottom_one_button" v-show="answer_status=='no_review'">
                        <button @click.stop="stage_walk">继续</button>
                        </div>


                    </div>
                    <!--lack-->


                    <!--spell-->
                    <div class="exeArea" v-show="now_stage_json.stageType=='spell'">
                        <div class="spell_body">

                            <!-- <div class="spell_body_type"><span>单词拼写</span></div>-->


                            <div class="spell_body_english">{{spell_show_str}}</div>

                            <div class="spell_body_chinese">{{now_stage_json.word_explain}}</div>


                        </div>
                        <div class="spell_word_pool" v-show="answer_status=='answering'">
                            <template v-for="(word,key) in now_pool_words_arr">
                                <span class="click_word" @click.stop="spell_click_word(key,word)">{{word}}</span>
                            </template>

                        </div>

                        <div class="spell_right_wrong" v-show="answer_status=='right'||answer_status=='wrong'">
                        <span style="color:#68D778" v-show="answer_status=='right'"
                              class="glyphicon glyphicon glyphicon-ok" aria-hidden="true"></span>
                        <span style="color:#FB667A" v-show="answer_status=='wrong'"
                              class="glyphicon glyphicon glyphicon-remove" aria-hidden="true"></span>
                        </div>

                        <div class="spell_answer" v-show="answer_status=='go_right'||answer_status=='no_review'">
                            <div><span>正确答案</span></div>
                            <div @click.stop="go_play_word_voice" style="font-weight:500;font-size: 18px;"><span
                                    class="glyphicon glyphicon-volume-down"></span>&nbsp;&nbsp;{{now_stage_json.learn_word}}
                            </div>
                            <div style="font-size: 14px;">{{now_stage_json.word_explain}}</div>
                        </div>


                        <div class="bottom_one_button" v-show="answer_status=='answering'">
                            <button @click.stop="spell_detele_word">删除</button>
                        </div>

                        <div class="bottom_two_buttons" v-show="answer_status=='go_right'">
                            <button @click.stop="exercise_restart">重做</button>
                            <button style="color:#364B9A;background:white;" @click.stop="go_to_review_knowledge">
                                回顾
                            </button>
                        </div>


                        <div class="bottom_one_button" v-show="answer_status=='no_review'">
                            <button @click.stop="stage_walk">继续</button>
                        </div>
                    </div>
                    <!--spell-->


                    <!--choice-->
                    <div class="exeArea" v-show="now_stage_json.stageType=='choice'">
                        <div class="choice_body">
                            <div class="choice_body_img" v-if="now_stage_json.img">
                                <img :src="now_stage_json.img">
                            </div>
                            <div class="choice_body_type" v-else>单项选择题</div>

                            <div class="choice_body_stem_1" v-if="now_stage_json.question_stem_1">
                                {{now_stage_json.question_stem_1}}
                            </div>
                            <div class="choice_body_stem_2" v-if="now_stage_json.question_stem_2">
                                {{now_stage_json.question_stem_2}}
                            </div>


                        </div>
                        <div class="choice_foot">
                            <div answer_key="0" v-if="now_stage_json.option_1" @click.stop="choice_click">
                                <div>
                                    <div>
                                        {{now_stage_json.option_1}}
                                    </div>
                                </div>
                            </div>

                            <div answer_key="1" v-if="now_stage_json.option_2" @click.stop="choice_click">
                                <div>

                                    <div>
                                        {{now_stage_json.option_2}}
                                    </div>
                                </div>
                            </div>
                            <div answer_key="2" v-if="now_stage_json.option_3" @click.stop="choice_click">
                                <div>
                                    <div>
                                        {{now_stage_json.option_3}}
                                    </div>
                                </div>
                            </div>


                        </div>

                        <div class="bottom_one_button" v-show="answer_status=='wrong'">
                            <button style="color:#364B9A;background:white;" @click.stop="go_to_review_knowledge">回顾
                            </button>
                        </div>


                        <div class="bottom_one_button" v-show="answer_status=='no_review'">
                            <button @click.stop="stage_walk">继续</button>
                        </div>

                    </div>
                    <!--choice-->


                    <!--wordchoice-->
                    <div class="exeArea" v-show="now_stage_json.stageType=='wordchoice'">
                        <div class="wordchoice_body">
                            <div class="wordchoice_body_english">{{now_stage_json.learn_word}}</div>
                            <div class="wordchoice_body_chinese" style="font-style: normal;">这个单词的释义是？</div>
                        </div>

                        <div class="wordchoice_foot"
                             v-show="answer_status=='answering'||answer_status=='answering_end'">

                            <template v-for="(ex,key) in wordchoice_option_array">
                                <div :disabled="answer_status=='answering_end'" @click.stop="wordchoice_click">
                                    <div>
                                        <div>{{ex}}</div>
                                    </div>
                                </div>
                            </template>


                        </div>


                        <div class="wordchoice_answer" v-show="answer_status=='wrong'||answer_status=='no_review'">
                            <div><span>正确答案</span></div>
                            <div @click.stop="go_play_word_voice" style="font-weight:500;font-size: 18px;"><span
                                    class="glyphicon glyphicon-volume-down"></span>&nbsp;&nbsp;{{now_stage_json.learn_word}}
                            </div>
                            <div style="font-size: 14px;">{{now_stage_json.word_explain}}</div>
                        </div>


                        <div class="bottom_one_button" v-show="answer_status=='wrong'">
                            <button style="color:#364B9A;background:white;" @click.stop="go_to_review_knowledge">回顾
                            </button>
                        </div>


                        <div class="bottom_one_button" v-show="answer_status=='no_review'">
                            <button @click.stop="stage_walk">继续</button>
                        </div>

                    </div>
                    <!--wordchoice-->


                    <!--voice-->
                    <div class="exeArea" v-show="now_stage_json.stageType=='voice'">

                        <!--

                        voice

                        voice_status状态：
                        "no_movement"没有动作
                        "ori_voice_playing"正确语音播放中
                        "begin_record_cant_stop" 开始录音但不能停止
                        "begin_record_can_stop" 可以停止录音
                        "end_record_no_assessment" 结束录音，但没有评价
                        "end_record_has_assessment" 结束录音，已经有了评价


                        -->
                        <div class="voice_body">
                            <div class="voice_body_img">
                                <div @click.stop="ori_voice_go" class="play_ori_voice"
                                     v-show="voice_status=='no_movement'">
                                    <span aria-hidden="true" class="glyphicon glyphicon-play"></span>
                                </div>
                                <div class="play_ori_voice" v-show="voice_status=='ori_voice_playing'">
                                    <span aria-hidden="true" class="glyphicon glyphicon-pause"></span>
                                </div>

                            </div>





                            <div class="voice_body_stem_1" style="font-size: 18px;font-weight: bold;">
                                语音练习
                            </div>


                            <div class="voice_body_stem_1" v-if="now_stage_json.question_stem_1">
                                {{now_stage_json.question_stem_1}}
                            </div>
                            <div class="voice_body_stem_2" v-if="now_stage_json.question_stem_2">
                                {{now_stage_json.question_stem_2}}
                            </div>
                        </div>




                        <div class="voice_over_btn_2" v-show="voice_status=='no_movement'">
                            <button @click.stop="stage_walk">继续</button>
                        </div>

                        <div class="voice_over_btn_2" v-show="voice_status=='ori_voice_playing'">
                            <button style="border: 1px solid darkgray;color: darkgray">继续</button>
                        </div>


                    </div>
                    <!--voice-->


                </div>
            </transition>
            <!--练习页结束-->


            <!--结束页-->
            <div class="overPage" v-cloak v-show="now_stage_model=='over'&&isCourseOver">
                <div class="over_page_mask_transparent">
                    <div class="over_page_dialog">
                        <div class="over_page_dialog_title">
                            <div v-if="course_status==0">课程已结束播放</div>
                            <div v-else-if="course_status==1">课程已结束播放</div>
                            <div v-else="course_status==2">课程已结束播放</div>
                        </div>
                        <div class="over_page_dialog_body">
                            <div @click.stop="course_restart">重新学习</div>
                            <div @click.stop="go_back_to_cat">课程表</div>
                        </div>

                    </div>
                </div>
            </div>
            <!--结束页-->


            <!--是否继续面板-->

            <div class="is_continue_page" v-cloak v-show="ask_is_continue">
                <div class="is_continue_page_mask"></div>

                <transition name="slide-left">
                    <div class="is_continue_page_dialog" v-show="ask_is_continue">
                        <div class="is_continue_page_dialog_title">
                            <div>检测到已有学习进度，是否继续学习？</div>
                        </div>
                        <div class="is_continue_page_dialog_body">
                            <div @click.stop="continue_learning">继续上次学习</div>
                            <div @click.stop="ask_is_continue=false">从头开始</div>
                        </div>
                    </div>
                </transition>

            </div>
            <!--是否继续面板-->


            <template v-if="is_show_skip_btn()">
                <transition name="slide-in">
                    <div class="skip_btn" v-cloak>
                        <div @click.stop="user_move_back">
                            <span class="glyphicon glyphicon-menu-left" aria-hidden="true"></span>
                        </div>

                        <div @click.stop="user_move_forward">
                            <span class="glyphicon glyphicon-menu-right" aria-hidden="true"></span>
                        </div>
                    </div>
                </transition>
            </template>
            <!--课程前后移动按钮-->


            <!--向老师提问按钮-->
            <!--<template v-if="course_status!=1&&isHeikaVip==1">
                    <transition name="slide-in">
                        <div class="ask_teacher_btn" @click.stop="go_ask_teacher" v-show="can_show_ask_teacher_btn()"
                             v-cloak>
                            <div>
                                问老师
                            </div>

                        </div>

                    </transition>

                </template>-->
            <!--向老师提问按钮-->


            <!--向老师提问面板-->
            <div class="ask_teacher_page" v-show="is_ask_teacher" v-cloak>
                <div class="ask_teacher_page_mask"></div>

                <transition name="slide-left">
                    <div class="ask_teacher_page_dialog">

                        <div class="ask_teacher_page_dialog_title" @click.stop="close_ask_teacher">
                            <span class="glyphicon glyphicon glyphicon-remove" aria-hidden="true"></span>
                        </div>

                        <div class="ask_teacher_page_dialog_body">
                            <textarea type="text" @input="listen_ask_teacher_text()" v-model="ask_teacher_text"
                                      placeholder="针对此题进行提问，老师会在后台尽快回复。">

                            </textarea>
                        </div>

                        <div class="ask_teacher_page_dialog_warning" v-show="ask_teacher_can_submit==false">
                            <div v-show="ask_teacher_can_submit==true">&nbsp;&nbsp;&nbsp;&nbsp;</div>
                            <div v-show="ask_teacher_can_submit==false">请填写至少15个汉字的问题</div>
                        </div>


                        <div class="ask_teacher_page_dialog_foot">
                            <button @click.stop="submit_ask_teacher_text" v-show="ask_teacher_can_submit==true">提交
                            </button>
                            <button style='background-color: darkgray;color: white'
                                    v-show="ask_teacher_can_submit==false">提交
                            </button>
                        </div>


                    </div>
                </transition>

            </div>
            <!--向老师提问面板--->


            <!--警告提示-->
            <div class="warning" v-show="show_warning==true&&warning_text" v-cloak>{{warning_text}}</div>
            <!--警告提示-->


            <!--顶部提示-->

            <transition name="slide-down">

                <div class="top_fixed" v-show="show_top_fixed==true" v-cloak>

                    <div>
                        <img
                            src="<?php echo home_url() ?>/img/newcourse/<EMAIL>"/><span>{{top_fixed_text}}</span>
                    </div>

                    <div @click.stop="close_top_fixed">
                        <span></span>
                    </div>
                </div>

            </transition>
            <!--顶部提示-->


            <!--测试用户按钮-->

            <div v-cloak
                 v-show="is_test_user==1&&now_stage_model!='load'&&course_status!=1&&now_stage_model!='over'&&now_stage_model=='course'">

                <div class="test_button" style="left: 30%;width:40%;height: 80px">
                    <div style="height: 40px;line-height: 40px; text-align: center;" id="test_pause_time">
                        {{test_user_show_time}}
                    </div>
                    <div @click.stop="copy_test_user_show_time"
                         style="height: 40px;line-height: 40px; text-align: center;color: #007aff">{{copy_button}}
                    </div>
                </div>


            </div>

            <div v-cloak
                 v-show="is_test_user==1&&now_stage_model!='load'&&course_status!=1&&now_stage_model!='over'&&now_stage_model!='review'">

                <button class="test_button" @click.stop="test_back" style="left: 0%">向后
                </button>

                <button class="test_button" @click.stop="test_forward" style="right:0%">向前
                </button>

            </div>

            <div v-cloak
                 v-show="is_test_user==1&&now_stage_model!='load'&&course_status==1&&now_stage_model!='over'&&now_stage_model!='review'">


                <button class="test_button" @click.stop="test_back_in_review" style="left: 0%">向后
                </button>
                <button class="test_button" @click.stop="test_forward_in_review" style="right:0%">向前
                </button>
            </div>

            <div v-cloak v-show="is_test_user==1&&now_stage_model!='load'&&now_stage_model!='over'">

                <button class="test_button" style="left: 0%;top:70%;">{{pageindex+1}}</button>

            </div>


            <!--测试用户按钮-->


        </div>


        <script     >

            var container = new Vue({
                el: '#container',
                data: {

                    nowTime:<?php echo $nowTime?>,//现在时间

                    user_id: "<?php echo $users_data->user_id;?>",//用户id
                    isHeikaVip: "<?php echo $users_data->isHeikaVip;?>",//是否黑卡VIP
                    is_test_user: "<?php echo $is_test_user;?>",//是否测试用户

                    post_id: "<?php echo $courses_data->post_id;?>",//课程ID

                    post_cat_id: "<?php echo $courses_data->post_cat_id;?>",//课程分类ID
                    post_cat_name: "<?php echo $courses_data->post_cat_name;?>",//课程分类名字

                    post_title: "<?php echo $courses_data->post_title;?>",//课程标题，


                    fail_num: 0,
                    course_ajax_url: "<?php echo $course_ajax_url;?>",//课程数据通讯地址
                    course_review_ajax_url: "<?php echo $course_review_ajax_url;?>",//记忆系统专属通讯地址
                    ajaxErrorNum: 0,
                    cat_url: "<?php echo $cat_url;?>",
                    today_review_url: "<?php echo $today_review_url;?>",
                    heika_task_url: "<?php echo $heika_task_url;?>",



                    loading_page_random_number:0,


                    course_status: "<?php echo $course_status;?>",//课程状态


                    audio_listener_int: 0,//音频监听

                    course_learning_stage_json:<?php echo $courses_data->course_learning_stage_json;?>,//课程关卡

                    course_audio_time_arr: [],//课程时间节点数组

                    course_word_explain_array: [],//课程所有单词释义数组


                    course_sentence_word_array: [],//所有句子单词集合

                    course_audio_src: "<?php echo $courses_data->course_audio_src;?>",//音频LRC

                    course_audio: "",//音频对象

                    course_audio_preload: 0,//音频加载参数

                    is_course_audio_canplay: false,//音频是否可以播放

                    is_course_audio_playing: false,//音频是否正在播放

                    course_audio_playbackspeed: "1.0x",


                    sound_right_src: "<?php echo $sound_right_src;?>",//正确音效地址
                    sound_right: "",
                    sound_wrong_src: "<?php echo $sound_wrong_src;?>",//错误音效地址
                    sound_wrong: "",


                    isWeixin: <?php echo $isWeixin;?>,
                    isLoadingOver: false,//是否加载完成
                    progressNumber: 12,//进度数字
                    tweenedProgressNumber: 12,//动画效果进度数字
                    isCourseOver: false,
                    isLocking: false,


                    now_stage_json: "",//当前关卡
                    now_stage_model: "load",//当前关卡的模式


                    review_now_stage_json: {},//回顾模式页面json
                    review_page_index: 0,//回顾模式页面指针
                    review_has_go_num: 1,//回顾模式已走数
                    review_go_num: 0,//回顾模式应走数


                    answer_status: "answering",//lack的习题状态
                    now_blank_words_arr: [],//lack的需填充单词组
                    now_pool_words_arr: [],//lack的单词池数组
                    this_stage_constant_pool_word_arr:[],//本题不变的选项池


                    spell_underline_str: "",
                    spell_show_str: "",

                    spell_clicked_zimu_arr: [],//单词拼写已点击字母


                    wordchoice_option_array: [],//单词选择题选项数组
                    wordchoice_standby_explain_array: ["n.卧室", "n.前面", "v.掉下", "n.糖", "n.卖肉的", "adj.潮湿的", "n.晚上", "n.售货员", "n.舌头", "n.消息", "n.城镇", "n.伦敦", "v.买", "v.需要", "v.离开", "n.巴黎", "v.修理", "n.飞行员", "n.大量"],


                    pageindex: 0,//页面指针


                    /*
                     *
                     * 统计用户行为需要参数
                     * */

                    maxpaged: <?php echo $course_maxpaged;?>,//总的最大翻页
                    this_time_maxpaged: 0,//本次最大翻页
                    is_user_finish_the_course: 0,//是否完成课程
                    stay_for_time: 0,//停留时间


                    slide_dir: "left",


                    startX: "",
                    startY: "",
                    endX: "",
                    endY: "",


                    /*
                     *
                     *
                     * 语音内容需要参数
                     * */



                    wx_voice_id: "",


                    voice_status: "no_movement",
                    voice_assessment_score: 0,
                    voice_assessment_text: "",
                    has_assessment: false,
                    assessment_timer: 0,

                    is_user_permit_to_record: true,//用户是否允许录音


                    go_over_pageindex_arr: [],//复习需要的指针群
                    go_over_index: 0,//复习指针

                    warning_text: "",

                    show_warning: false,


                    top_fixed_text: "",

                    show_top_fixed: false,


                    ask_is_continue: false,


                    mistake_word_arr: [],//错误单词数组
                    mistake_sentence_arr: [],//错误句子数组

                    mistake_times: 0,

                    test_user_show_time: '[00:00.00]',//测试用户显示时间
                    copy_button: '复制时间点',

                    is_ask_teacher: false,//是否在问老师
                    ask_teacher_text: "",//问老师文本
                    ask_teacher_can_submit: false//是否可以问老师


                },

                methods: {


                    course_init: function () {


                        /*
                         *
                         *
                         * 加载页面小贴士的随机数
                         * */

                        this.loading_page_random_number=Math.random();




                        /*
                         *
                         *
                         * 获取 course_audio_time_arr
                         * */


                        for (let i = 0; i < this.course_learning_stage_json.length; i++) {
                            if (this.course_learning_stage_json[i].classifyType == 'course') {

                                this.course_audio_time_arr.push(this.audio_time_to_timestamp(this.course_learning_stage_json[i].timestamp));

                            } else {
                                this.course_audio_time_arr.push("*");
                            }
                        }


                        //console.log(this.course_audio_time_arr);


                        console.log(this.course_learning_stage_json);


                        /*
                         *
                         * 记录用户停留时间
                         * */

                        var _this = this;


                        window.setInterval(function () {
                            _this.stay_for_time += 1;
                        }, 1000);


                        /*
                         *
                         * 每10秒记录一次用户行为
                         * */


                        window.setInterval(function () {
                            _this.save_user_listening_behavior();
                        }, 10000);


                        this.loadingAudio();//加载音频，并运作进度条

                        this.loadingImg();//加载图片

                        this.set_widget();//布局设置

                        this.data_preparation();//数据准备


                    },//进入课程

                    set_widget: function () {

                        var h;//高
                        var w;//宽
                        var htext;//高文本
                        var wtext;//宽文本


                        /*播放和暂停按钮，位置设置*/
                        var playAudioBtn = $("#playAudio");
                        var pauseAudioBtn = $("#pauseAudio");
                        h = (CLIENTHEIGHT / 5 - 60) / 2;
                        htext = h.toString() + "px";
                        w = (CLIENTWIDTH - 60) / 2;
                        wtext = w.toString() + "px";
                        playAudioBtn.css("top", htext);
                        playAudioBtn.css("left", wtext);
                        pauseAudioBtn.css("top", htext);
                        pauseAudioBtn.css("left", wtext);

                        /*复听按钮*/


                        var playbackspeedbtn = $("#playbackspeed");

                        h = (CLIENTHEIGHT / 5 - 30) / 2;
                        htext = h.toString() + "px";

                        w = (CLIENTWIDTH - 60) / 8;
                        wtext = w.toString() + "px";

                        playbackspeedbtn.css("top", htext);
                        playbackspeedbtn.css("left", wtext);


                        /*倍速按钮*/

                        var lockingBtn = $("#locking");
                        var unlockingBtn = $("#unlocking");
                        h = (CLIENTHEIGHT / 5 - 30) / 2;
                        htext = h.toString() + "px";

                        w = (CLIENTWIDTH - 60) / 8;
                        wtext = w.toString() + "px";

                        lockingBtn.css("top", htext);
                        lockingBtn.css("right", wtext);
                        unlockingBtn.css("top", htext);
                        unlockingBtn.css("right", wtext);


                        /*复习模式4个字体位置设置*/
                        var reviewText = $("#reviewText");
                        h = (CLIENTHEIGHT / 5 - 50) / 2;
                        htext = h.toString() + "px";
                        w = (CLIENTWIDTH - 110) / 2;
                        wtext = w.toString() + "px";
                        reviewText.css("top", htext);
                        reviewText.css("left", wtext);


                        var back_to_exe_in_reivew = $("#back_to_exe_in_reivew");

                        h = (CLIENTHEIGHT / 5 - 30) / 2;
                        htext = h.toString() + "px";

                        w = (CLIENTWIDTH - 60) / 8;
                        wtext = w.toString() + "px";
                        back_to_exe_in_reivew.css("top", htext);
                        back_to_exe_in_reivew.css("right", wtext);


                        /*警告窗口位置设置*/
                        var warning = $(".warning");
                        w = (CLIENTWIDTH - 160) / 2;
                        wtext = w.toString() + "px";
                        warning.css("left", wtext);


                        /*播放按钮永久呼吸灯*/
                        var o = 100;
                        var go = false;
                        window.setInterval(function () {


                            if (!go) {
                                o--
                            } else {
                                o++
                            }

                            pauseAudioBtn.css("opacity", o / 100);
                            reviewText.css("opacity", o / 100);

                            if (o <= 10) {
                                go = true;
                            }

                            if (o >= 90) {
                                go = false;
                            }


                        }, 20);


                        /*进度条*/
                        this.course_has_go(0);


                        var x = parseFloat(h * 0.26).toFixed(2);
                        $(".voice_user_voice").css("height", x.toString() + "px");

                        var y = parseFloat(h * 0.40).toFixed(2);
                        $(".voice_click").css("height", y.toString() + "px");
                        $(".voice_click_inner").css("height", y.toString() + "px");

                        var divs = $(".voice_click_inner").find("div");
                        for (var i = 0; i < divs.length; i++) {

                            $(divs[i]).css("height", y.toString() + "px");

                        }

                        var z = parseFloat(CLIENTWIDTH * 0.9 * 0.33 * 0.5).toFixed(2);

                        var imgs = $(".voice_click_inner").find("img");
                        for (var i = 0; i < imgs.length; i++) {

                            $(imgs[i]).css("width", z.toString() + "px");

                        }


                    },//按钮布局，动画效果,屏幕适配等

                    loadingImg: function () {

                        function preLoadImg(arr) {
                            for (var i = 0; i < arr.length; i++) {
                                var image = new Image();
                                image.src = arr[i];
                            }
                        }


                        var imgArr = [];

                        for (var i = 0; i < this.course_learning_stage_json.length; i++) {
                            if (this.course_learning_stage_json[i].img) {
                                imgArr.push(this.course_learning_stage_json[i].img);
                            }
                        }


                        preLoadImg(imgArr);


                    },//加载图片

                    loadingAudio: function () {

                        var _this = this;


                        /*
                         *
                         *
                         * 测试用户处理
                         * */


                        if (this.is_test_user) {
                            this.course_audio = new Audio(this.course_audio_src);
                            this.sound_right = new Audio(this.sound_right_src);
                            this.sound_wrong = new Audio(this.sound_wrong_src);

                            _this.progressNumber = 100;//进度条拉满

                            _this.is_course_audio_canplay = true;
                        }


                        /*
                         *
                         *
                         * 非测试用户处理
                         * */


                        if (!this.is_test_user) {

                            /*
                             *

                             * 各类音频准备
                             * */


                            this.course_audio = new Audio(this.course_audio_src);
                            this.sound_right = new Audio(this.sound_right_src);
                            this.sound_wrong = new Audio(this.sound_wrong_src);


                            this.course_audio.muted = true;
                            this.course_audio.play();


                            this.sound_right.muted = true;
                            this.sound_right.play();


                            this.sound_wrong.muted = true;
                            this.sound_wrong.play();


                            wx.ready(function () {


                                _this.course_audio.muted = true;
                                _this.course_audio.play();


                                _this.sound_right.muted = true;
                                _this.sound_right.play();


                                _this.sound_wrong.muted = true;
                                _this.sound_wrong.play();


                            });


                            /*检测音频播放情况*/

                            var i = 0;


                            this.course_audio_preload = window.setInterval(function () {

                                i++;//i每秒递增1


                                if (_this.course_audio.currentTime >= 1) {//音频自动播放的时间超过0

                                    _this.progressNumber = 100;//进度条拉满

                                    _this.is_course_audio_canplay = true;//音频可以播放

                                    _this.course_audio.pause();//关闭音频

                                    _this.course_audio.currentTime = 0;//播放请0

                                    _this.course_audio.muted = false;

                                    window.clearInterval(_this.course_audio_preload);

                                } else {


                                    if (i == 1) {
                                        _this.progressNumber = 33;

                                    } else if (i == 2) {

                                        _this.progressNumber = 38;

                                    } else if (i == 3 || i == 4) {
                                        _this.progressNumber = 56;

                                    } else if (i == 5) {

                                        _this.progressNumber = 68;

                                    } else if (i == 6) {
                                        _this.progressNumber = 74;
                                    } else if (i == 7) {
                                        _this.progressNumber = 91;
                                    } else {
                                        _this.progressNumber = 100;

                                        _this.course_audio.pause();//关闭音频

                                        _this.course_audio.currentTime = 0;//播放请0

                                        _this.course_audio.muted = false;

                                        window.clearInterval(_this.course_audio_preload);
                                    }

                                }


                            }, 1000)

                        }


                    },//加载音频

                    data_preparation: function () {


                        /*获得所有单词的释义，组成数组*/

                        for (var i = 0; i < this.course_learning_stage_json.length; i++) {

                            if (this.course_learning_stage_json[i].stageType == 'word') {

                                var word_explain = this.course_learning_stage_json[i].word_explain.replace(/\s*/g, "");//清理所有空格

                                if (this.course_word_explain_array.indexOf(word_explain) == -1) {//去掉重复释义
                                    this.course_word_explain_array.push(word_explain);
                                }

                            }
                        }


                        /*
                         *
                         * 将课程内所有句子的单词组成数组
                         *
                         * */


                        for (let i = 0; i < this.course_learning_stage_json.length; i++) {

                            if (this.course_learning_stage_json[i].english_sentence_no_dot) {


                                var e = this.course_learning_stage_json[i].english_sentence_no_dot;

                                var noDotWords = e.split(" ");


                                if (noDotWords.length > 0) {
                                    for (var j = 0; j < noDotWords.length; j++) {

                                        /*去掉重复单词，推入数组*/
                                        if (this.course_sentence_word_array.indexOf(noDotWords[j]) == -1) {//去掉重复释义
                                            this.course_sentence_word_array.push(noDotWords[j]);
                                        }
                                    }
                                }

                            }
                        }




                        /*
                         *
                         *随机生成单词习题，60%单词拼写，40%单词选择题
                         * */


                        for (let i = 0; i < this.course_learning_stage_json.length; i++) {

                            if (this.course_learning_stage_json[i].stageType == 'wordchoice') {

                                var n = Math.floor(Math.random() * 10) + 1;
                                if (n <= 5) {
                                    this.course_learning_stage_json[i].stageType = "spell";
                                }

                            }
                        }


                        //console.log("历史翻页纪录：" + this.maxpaged);

                        //console.log("总长度：" + this.course_learning_stage_json.length);


                        /*
                         *
                         *
                         * 最大翻页处理,当最大翻页在倒数第二页，改为倒数第5页
                         * */


                        if (this.maxpaged >= this.course_learning_stage_json.length - 1) {
                            this.maxpaged = this.course_learning_stage_json.length - 4;

                        }

                    },//数据准备


                    play_sound_effect: function (sound) {
                        sound.muted = false;
                        sound.play();

                    },//播放音效

                    ready_to_into_course: function () {


                        this.course_audio.pause();//音频暂停
                        this.course_audio.currentTime = 0;//音频当前时间为0
                        this.course_audio.muted = false;//关闭音频静音

                        /* window.setTimeout(
                         function () {
                         _this.isLoadingOver = true;//加载完成
                         _this.into_course_page();//渲染页面

                         }, 500);*/

                        this.isLoadingOver = true;//加载完成
                        this.into_course_page();//渲染页面


                    },//加载完毕后5秒内进入课程


                    /*
                     *
                     * 复习关卡组设计1，将单词选择全都替换为单词拼写，并打乱所有习题
                     * */


                    handle_go_over_pageindex_arr_one: function () {

                        /*
                         * 将 this.course_learning_stage_json 中的单词选择习题 替换成单词拼写训练
                         *
                         * */

                        for (let i = 0; i < this.course_learning_stage_json.length; i++) {
                            if (this.course_learning_stage_json[i].stageType == "wordchoice") {
                                this.course_learning_stage_json[i].stageType = "spell"
                            }
                        }


                    },

                    into_course_page: function () {


                        /*
                         *
                         * 复习模式的处理
                         * */

                        if (this.course_status == 1) {



                            /*
                             * 将习题卡片的序号，并入复习组 this.go_over_pageindex_arr
                             *
                             * */


                            for (let i = 0; i < this.course_audio_time_arr.length; i++) {
                                if (this.course_audio_time_arr[i] == "*") {
                                    this.go_over_pageindex_arr.push(i);
                                }
                            }


                            /*
                             *打乱复习组 go_over_pageindex_arr 的内容
                             *
                             * */

                            this.go_over_pageindex_arr = this.shuffle(this.go_over_pageindex_arr);


                            /*
                             * 最终关卡,相当于结束页面，放入复习组末尾
                             *
                             * */

                            var maxstage = this.course_learning_stage_json.length;
                            this.go_over_pageindex_arr.push(maxstage);


                            /*
                             *
                             *
                             * 页面指针设置
                             * */
                            this.pageindex = this.go_over_pageindex_arr[this.go_over_index];//将复习指针

                        }



                        /*
                         * 初学模式中的一些处理
                         * */


                        else if (this.course_status == 0) {


                            /*
                             *
                             * 页面指针设为0
                             * */


                            this.pageindex = 0;

                            /*
                             *
                             * 当最大翻页大约3时，询问是否继续
                             * */

                            if (this.maxpaged > 3) {
                                this.ask_is_continue = true;//询问是否继续
                            }


                        }



                        /*
                         * 初学模式中，当最大翻页大于3时，询问是否继续
                         * */

                        else {
                            this.pageindex = 0;
                        }


                        this.now_stage_json = this.course_learning_stage_json[this.pageindex];//当前的关卡数据


                    },//进入课程


                    continue_learning: function () {

                        this.ask_is_continue = false;//不询问继续

                        this.pageindex = this.maxpaged;

                    },//继续学习

                    is_current_stage: function (key) {
                        if (key == this.pageindex) {
                            return true
                        }
                        return false;
                    },//判断，是否为当前关卡

                    audio_play: function () {


                        this.test_user_show_time = "播放中";//测试用户查看时间
                        this.copy_button = "";


                        this.course_audio.muted = false;//关闭音频静音

                        this.audio_listener(true);//音频监听

                        if (!this.is_course_audio_canplay) {

                            $$loading("加载中");//显示加载中
                            this.course_audio.play();//播放音频


                            var _this = this;


                            this.course_audio_preload = window.setInterval(function () {
                                //console.log(_this.course_audio.currentTime);

                                if (_this.course_audio.currentTime >= 0.2) {//音频自动播放的时间超过0

                                    $$closeLoading();


                                    _this.is_course_audio_canplay = true;//音频可以播放
                                    _this.is_course_audio_playing = true;
                                    window.clearInterval(_this.course_audio_preload);

                                }


                            }, 100)


                        } else {
                            this.course_audio.play();//播放
                            this.is_course_audio_playing = true;//可播放
                        }

                    },//播放音频

                    audio_pause: function () {
                        this.course_audio.pause();
                        this.is_course_audio_playing = false;

                        this.audio_listener(false);

                        var t = (this.course_audio.currentTime / 60).toFixed(4).toString();
                        var a = t.split(".");
                        var integer = parseInt(a[0]);
                        var integerStr = "";
                        var float = (this.course_audio.currentTime % 60).toFixed(2);
                        var floatStr = "";


                        if (float > 0 && float < 10) {
                            floatStr = "0" + float.toString();

                        } else if (float >= 10) {
                            floatStr = float.toString();

                        } else {
                            floatStr = "00";
                        }


                        if (integer > 0 && integer < 10) {
                            integerStr = "0" + integer.toString();

                        } else if (integer >= 10) {
                            integerStr = integer.toString();

                        } else {
                            integerStr = "00";
                        }

                        var n = "[" + integerStr + ":" + floatStr + "]";


                        this.test_user_show_time = n;//测试用户查看时间
                        this.copy_button = "复制时间点";


                        console.log(n);

                    },//暂停音频

                    audio_listener: function (isOpen) {
                        var _this = this;


                        if (isOpen) {

                            console.log("audio_listener:open");


                            this.audio_listener_int = window.setInterval(function () {


                                var overTime = 0;//下一个时间节点

                                /*获取下一个时间节点*/

                                for (var i = _this.pageindex + 1; i < _this.course_audio_time_arr.length; i++) {
                                    if (_this.course_audio_time_arr[i] != "*") {
                                        overTime = _this.course_audio_time_arr[i];
                                        break;
                                    }

                                }

                                if (overTime == 0) {
                                    overTime = _this.course_audio.duration - 0.3;

                                }


                                if (_this.course_audio.currentTime >= overTime) {

                                    if (!_this.isLocking) {//非复读状态

                                        if (_this.pageindex <= _this.course_audio_time_arr.length - 1) {
                                            _this.pageindex += 1;

                                        } else {
                                            _this.now_stage_model = 'over';//课程结束

                                        }
                                    } else {//复读状态
                                        _this.course_audio.currentTime = _this.course_audio_time_arr[_this.pageindex];

                                        console.log(_this.course_audio.currentTime);

                                    }


                                }


                            }, 100);

                        } else {

                            console.log("audio_listener:close");
                            //console.log("close");
                            window.clearInterval(this.audio_listener_int);
                        }


                    },//监听音频

                    change_audio_playbackspeed: function () {

                        if (this.course_audio_playbackspeed == "1.0x") {
                            //this.course_audio.defaultPlaybackRate=10;
                            this.course_audio.playbackRate = 1.5;
                            this.course_audio.defaultPlaybackRate = 1.5;
                            this.course_audio_playbackspeed = "2.0x";

                            console.log(this.course_audio.defaultPlaybackRate);
                        } else {
                            // this.course_audio.defaultPlaybackRate=1;
                            this.course_audio.playbackRate = 1;

                            this.course_audio.defaultPlaybackRate = 1;
                            this.course_audio_playbackspeed = "1.0x";
                            console.log(this.course_audio.defaultPlaybackRate);
                        }

                    },//音频播放速度更改

                    locking_click: function () {

                        if (this.isLocking) {
                            this.isLocking = false;

                        } else {
                            this.isLocking = true;
                        }
                        //console.log(this.isLocking);

                    },

                    go_play_word_voice: function (e) {

                        var d = $(e.currentTarget);
                        d.find("span")[0].className = "glyphicon glyphicon-volume-up";


                        window.setTimeout(function () {
                            d.find("span")[0].className = "glyphicon glyphicon-volume-down";
                        }, 500);


                        var word = this.now_stage_json.learn_word;


                        var audio = document.createElement("audio");
                        audio.src = "https://dict.youdao.com/dictvoice?audio=" + word;
                        audio.play();
                    },//单词读音播放

                    course_has_go: function (pageindex) {

                        var num = pageindex + 1;

                        var coursePage_progress_has_go = $(".coursePage_progress_has_go");//课程进度条

                        var percent = (num / this.course_learning_stage_json.length * 100).toFixed(2);
                        var percentText = percent.toString() + "%";
                        coursePage_progress_has_go.css("width", percentText);

                    },//课程进度

                    set_current_time_from_timestamp: function (timestamp) {
                        this.pageindex = this.course_audio_time_arr.indexOf(timestamp);//时间指针调整，找到下一个关卡的时间在时间数组的位置
                        //this.course_audio.currentTime = this.course_audio_time_arr[this.pageindex];//调整当前时间
                    },//通过时间或设置当前音频时间

                    get_true_timestamp: function (str) {
                        var t = str.slice(1, -1);
                        var tarr = t.split(":");
                        return parseInt(tarr[0]) * 60 + parseFloat(tarr[1]);

                    },

                    user_move_forward: function (e) {

                        var btn = $(e.currentTarget);

                        btn.css("background", "#364B9A");
                        btn.css("color", "white");

                        var _this = this;


                        window.setTimeout(function () {


                            btn.css("background", "white");
                            btn.css("color", "#364B9A");

                            _this.isLocking = false;//复读解锁

                            if (_this.pageindex < _this.course_learning_stage_json.length) {
                                _this.slide_dir = "left";//滚动方向向左
                                _this.audio_pause();
                                _this.pageindex += 1;
                            }


                        }, 50);


                    },//用户向前移动课程


                    is_show_skip_btn: function () {


                        if (this.now_stage_model == "exe" || this.now_stage_model == "course") {

                            //非复习状态下
                            if (this.course_status != 1) {

                                if (this.course_status != 0) {
                                    //课程状态是等待复习，那么随时可以调用


                                    return true;


                                } else {

                                    if (this.now_stage_json.classifyType == "course") {

                                        return true;

                                    }

                                    if (this.now_stage_json.classifyType == "exercise") {

                                        //当前页面指针小于等于最大翻页，可以显示

                                        if (this.pageindex + 1 <= this.maxpaged) {
                                            return true;
                                        }

                                    }


                                    //课程状态是新学

                                }

                            }

                        }


                        return false;


                    },


                    user_move_back: function (e) {

                        var btn = $(e.currentTarget);

                        btn.css("background", "#364B9A");
                        btn.css("color", "white");

                        var _this = this;


                        window.setTimeout(function () {
                            btn.css("background", "white");
                            btn.css("color", "#364B9A");

                            _this.isLocking = false;//复读解锁


                            if (_this.pageindex > 0) {

                                _this.slide_dir = "right";//滚动方向向左

                                _this.audio_pause();


                                _this.pageindex -= 1;

                            } else {
                                //提示，已到底
                            }
                        }, 50);


                    },//用户向后移动课程


                    can_show_ask_teacher_btn: function () {

                        if (this.now_stage_model == 'exe') {

                            if (this.answer_status == 'go_right' || this.answer_status == "wrong" || this.answer_status == "no_review") {

                                return true
                            }

                        }

                        return false;

                    },


                    go_ask_teacher: function () {

                        this.is_ask_teacher = true;
                        this.audio_pause();


                    },//开启老师提问对话框

                    reset_ask_teacher: function () {
                        this.ask_teacher_text = "";
                        this.ask_teacher_can_submit = false;

                    },


                    close_ask_teacher: function () {
                        this.is_ask_teacher = false;//关闭窗口
                        //this.ask_teacher_text="";//提交问题清0
                        //this.ask_teacher_can_submit=false;//是否可提交

                    },


                    listen_ask_teacher_text: function () {

                        //ask_teacher_text>30，设为可提交
                        if (this.ask_teacher_text.length > 15) {
                            this.ask_teacher_can_submit = true;
                        } else {
                            this.ask_teacher_can_submit = false;
                        }


                    },


                    submit_ask_teacher_text: function () {


                        console.log(this.now_stage_json);

                        //提交问题


                        var user_id = this.user_id;//用户ID


                        var pageindex = this.pageindex;//指针

                        var ask_teacher_text = this.ask_teacher_text;//问题文本

                        var post_id = this.post_id;//课程ID

                        var post_cat_id = this.post_cat_id;//课程目录ID

                        var post_cat_name = this.post_cat_name;//课程目录名称

                        var post_title = this.post_title;//课程标题

                        var stageType = this.now_stage_json.stageType;//卡片类型


                        var card_bindKnowledge = (this.now_stage_json.bindKnowledge == undefined) ? "" : this.now_stage_json.bindKnowledge;//卡片类型

                        var card_review_go_num = (this.now_stage_json.review_go_num == undefined) ? "" : this.now_stage_json.review_go_num;//复习行走


                        var card_question_stem_1 = (this.now_stage_json.question_stem_1 == undefined) ? "" : this.now_stage_json.question_stem_1;//选择题题干1
                        var card_question_stem_2 = (this.now_stage_json.question_stem_2 == undefined) ? "" : this.now_stage_json.question_stem_2;//选择题题干2


                        var card_right_option = (this.now_stage_json.right_option == undefined) ? "" : this.now_stage_json.right_option;//正确答案

                        var card_option_1 = (this.now_stage_json.option_1 == undefined) ? "" : this.now_stage_json.option_1;//选项1

                        var card_option_2 = (this.now_stage_json.option_2 == undefined) ? "" : this.now_stage_json.option_2;//选项2

                        var card_option_3 = (this.now_stage_json.option_3 == undefined) ? "" : this.now_stage_json.option_3;//选项3


                        var card_learn_word = (this.now_stage_json.learn_word == undefined) ? "" : this.now_stage_json.learn_word;//学习单词
                        var card_word_explain = (this.now_stage_json.word_explain == undefined) ? "" : this.now_stage_json.word_explain;//单词解释

                        var card_english_sentence = (this.now_stage_json.english_sentence == undefined) ? "" : this.now_stage_json.english_sentence;//英语句型
                        var card_chinese_sentence = (this.now_stage_json.chinese_sentence == undefined) ? "" : this.now_stage_json.chinese_sentence;//中文句型


                        this.close_ask_teacher();//关闭窗口


                        //传输数据


                        this.save_ask_teacher_text_ajax(
                            {
                                user_id: user_id,
                                action: 'save_ask_teacher_text',
                                post_id: post_id,
                                pageindex: pageindex,
                                ask_teacher_text: ask_teacher_text,
                                post_cat_id: post_cat_id,
                                post_cat_name: post_cat_name,
                                post_title: post_title,

                                stageType: stageType,
                                card_bindKnowledge: card_bindKnowledge,
                                card_review_go_num: card_review_go_num,

                                card_question_stem_1: card_question_stem_1,
                                card_question_stem_2: card_question_stem_2,
                                card_right_option: card_right_option,
                                card_option_1: card_option_1,
                                card_option_2: card_option_2,
                                card_option_3: card_option_3,

                                card_learn_word: card_learn_word,
                                card_word_explain: card_word_explain,

                                card_english_sentence: card_english_sentence,
                                card_chinese_sentence: card_chinese_sentence

                            }, function (res) {
                                //结果
                                console.log(res);


                                if (res) {
                                    $$alert("提示", "您的提问已成功提交！有效问题将在“我的提问”中展示。", function () {
                                    });

                                }
                            });


                        //重置数据


                        this.reset_ask_teacher();//重置数据

                        //console.log("提交");

                    },


                    save_ask_teacher_text_ajax: function (data, callback) {

                        var _this = this;

                        $$loading("加载中");


                        var doAjax = function () {
                            //ajax函数
                            $.ajax({
                                url: _this.course_ajax_url,
                                type: 'post',
                                data: data,
                                success: function (data, status) {
                                    _this.ajaxErrorNum = 0;//ajax错误状态归0

                                    $$closeLoading();


                                    //将结果转为字符串，去掉收尾空格
                                    var res = data.toString().trim();
                                    callback(res);

                                },
                                error: function () {
                                    _this.ajaxErrorNum += 1;//ajax错误状态递增
                                    if (_this.ajaxErrorNum >= 4) {
                                        _this.ajaxErrorNum = 0;//ajax错误状态归0
                                    } else {
                                        doAjax();//第二次以上执行
                                    }
                                }
                            });

                        };

                        doAjax();//执行AJAX
                    },


                    get_slide_dir: function () {
                        if (this.slide_dir == "left") {
                            return "slide-left";
                        }

                        return "slide-right";
                    },//获取滚动方向


                    exercise_init: function () {

                        //console.log("习题初始化");

                        var i = 0;


                        if (this.now_stage_json.stageType == 'lack') {


                            this.lack_init();


                        } else if (this.now_stage_json.stageType == 'spell') {


                            this.spell_init();

                        } else if (this.now_stage_json.stageType == 'wordchoice') {
                            //console.log("单词选择初始化");

                            this.wordchoice_init();

                        }

                    },


                    exercise_restart: function () {


                        this.review_data_clean();//复习参数重置

                        this.exercise_data_clean();//练习参数重置
                        this.exercise_init();//练习初始化

                    },//习题重做

                    exercise_data_clean: function () {

                        //console.log("清除练习数据");


                        this.voice_status = "no_movement";//语音页面动作
                        this.voice_assessment_score = 0;//语音评价得分
                        this.voice_assessment_text = "";//语音评价显示文字
                        this.has_assessment = false;//是否已评价
                        this.assessment_timer = 0;//语音评价计时器


                        this.answer_status = "answering";//lack的习题状态
                        this.now_blank_words_arr = [];//lack的需填充单词组
                        this.now_pool_words_arr = [];//lack的单词池数组
                        this.this_stage_constant_pool_word_arr=[];//本题不变的选项池


                        this.spell_show_str = "";
                        this.spell_underline_str = "";//
                        this.spell_clicked_zimu_arr = [];//spell按过的字母组

                        this.wordchoice_option_array = [];//单词选择题选项数组

                        var choice_options = $(".choice_foot").children("div");
                        for (var i = 0; i < choice_options.length; i++) {
                            $(choice_options[i]).attr("style", "background:#526ED1");
                        }

                    },//清理练习题参数


                    lack_init: function () {


                        /*
                         *
                         * 本句 hasDotWords， 有标点.
                         * */


                        var hasDotWords = this.now_stage_json.english_sentence_has_dot.split(" ");//本句 hasDotWords 有标点


                        /*
                         *
                         * 本句 noDotWords， 没有标点标点.
                         * */
                        var noDotWords = this.now_stage_json.english_sentence_no_dot.split(" ");
                        //console.log(noDotWords);


                        /*
                         *
                         * 将单词变成____  标点符号不变。 生成数组 this.now_blank_words_arr
                         * */

                        for (var i = 0; i < hasDotWords.length; i++) {
                            var isSame = false;

                            for (var j = 0; j < noDotWords.length; j++) {
                                if (noDotWords[j] == hasDotWords[i]) {
                                    this.now_blank_words_arr.push("_____");
                                    isSame = true;
                                    break;
                                }
                            }

                            if (!isSame) {
                                this.now_blank_words_arr.push(hasDotWords[i]);
                            }
                        }





                        /*
                         *
                         * 将没有标点的 noDotwords 变成选项池，并从总选项池适当选择单词加入选项池，并打乱选项池
                         * */


                        this.now_pool_words_arr = noDotWords.slice();//复制选项组


                        var course_sentence_word_array = this.course_sentence_word_array.slice();//复制总选项池


                        /*
                         * 当单词池的单词少于10个时，需要补充单词
                         *
                         * */


                        if (this.now_pool_words_arr.length < 10) {


                            var needwordnum = 10 - this.now_pool_words_arr.length;//需要补充单词数
                            //alert("需要补充单词数："+needwordnum);


                            /*
                             *
                             * 所需单词数必须小于等于总选项池长度
                             * */

                            if (needwordnum <= course_sentence_word_array.length) {

                                for (var l = 0; l < needwordnum; l++) {




                                    /*
                                     *
                                     * 当总选项池长度大于0时，随机挑选一个单词，若单词与本句选项池的单词都不一样，加入本句选项池
                                     * */




                                    if(course_sentence_word_array.length>0){


                                        /*
                                         *
                                         * 随机的key
                                         * */


                                        var key = Math.floor(Math.random() * course_sentence_word_array.length);


                                        /*
                                         * 随机的单词
                                         * */

                                        var value = course_sentence_word_array[key];



                                        console.log("随机到的单词："+value);
                                        console.log("随机到的key："+key);

                                        /*
                                         *
                                         * 当随机的单词和本句单词池有相同单词时，则需要重新循环，并剔除该单词
                                         *
                                         * 当随机的单词和本句单词池没有相同单词时，则推入本句单词池。删除该单词
                                         * */


                                        var is_now_pool_words_arr_has_this_value=false;//单词池是否拥有该单词

                                        /*
                                         *
                                         * 循环比对本句选项池与随机的单词值
                                         * */

                                        for (var k = 0; k < this.now_pool_words_arr.length; k++) {
                                            if(this.now_pool_words_arr[k]==value){
                                                is_now_pool_words_arr_has_this_value=true;
                                                break;
                                            }
                                        }


                                        console.log("本句单词池是否有该单词："+is_now_pool_words_arr_has_this_value);


                                        if(is_now_pool_words_arr_has_this_value){



                                            //删除
                                            course_sentence_word_array.splice(key, 1);

                                            console.log("剔除单词："+value);
                                            console.log("剔除单词key："+key);

                                            console.log("-----------");

                                            //重新循环
                                            l--;

                                        }else{
                                            //推入

                                            this.now_pool_words_arr.push(value);


                                            //删除
                                            course_sentence_word_array.splice(key, 1);

                                            console.log("选项池加入并剔除单词："+value);
                                            console.log("选项池加入并剔除单词key："+key);
                                            console.log("-----------")
                                        }

                                    }else{

                                        console.log("总选项池已没有单词");
                                        console.log("-----------");
                                        break;

                                    }





                                }



                            }


                        }


                        /*
                         *
                         * 打乱本句选项池
                         * */


                        this.now_pool_words_arr.sort(function () {
                            return 0.5 - Math.random()
                        });

                        this.this_stage_constant_pool_word_arr=this.now_pool_words_arr.concat();


                        console.log("----选项池单词-----");
                        console.log(this.now_pool_words_arr);
                        console.log("----选项池单词-----");




                        console.log("----不变选项池单词-----");
                        console.log(this.this_stage_constant_pool_word_arr);
                        console.log("----不变选项池单词-----");



                    },

                    lack_click_word: function (key, word) {


                        /*
                         * now_pool_words_arr  是下方选项池
                         *
                         * 选项池删除，
                         * */


                        this.now_pool_words_arr.splice(key, 1);//单词池删除这个单词

                        console.log("----选项池单词-----");
                        console.log(this.now_pool_words_arr);
                        console.log("----选项池单词-----");

                        console.log("----不变选项池单词-----");
                        console.log(this.this_stage_constant_pool_word_arr);
                        console.log("----不变选项池单词-----");


                        /*
                         *
                         * 将题干池 this.now_blank_words_arr 中 最早出现的 _____ 替换为单词
                         * */


                        for (var k = 0; k < this.now_blank_words_arr.length; k++) {
                            if (this.now_blank_words_arr[k] == "_____") {
                                this.$set(this.now_blank_words_arr, k, word);
                                break
                            }
                        }

                        console.log("----题干池单词-----");
                        console.log(this.now_blank_words_arr);
                        console.log("----题干池单词-----");


                    },//lack 点击单词

                    lack_detele_word: function (e) {


                        /*效果开始*/

                        var btn = $(e.currentTarget);

                        btn.css("background", "white");
                        btn.css("color", "#364B9A");


                        window.setTimeout(function () {
                            btn.css("background", "#364B9A");
                            btn.css("color", "white");
                        }, 50);

                        /*效果结束*/


                        /*
                         * 先寻找特定单词或字母
                         * */



                        var ishasword = false;//是否有该单词  flag
                        var findKey = 0;//位置

                        var noDotWords = [];//没有标点符号的单词组

                        if (this.now_stage_json.stageType == 'lack') {

                            //句子缺词填空
                            //noDotWords = this.now_stage_json.english_sentence_no_dot.split(" ");

                            //直接从不变选项组中获得单词数组

                            noDotWords =this.this_stage_constant_pool_word_arr.concat();

                        } else {

                            //单词缺词填空

                            for (var i = 0; i < this.now_stage_json.learn_word.length; i++) {

                                noDotWords.push(this.now_stage_json.learn_word.substring(i, i + 1));
                            }


                        }


                        for (var x = 0; x < this.now_blank_words_arr.length; x++) {
                            for (var y = 0; y < noDotWords.length; y++) {
                                if (this.now_blank_words_arr[x] == noDotWords[y]) {
                                    findKey = x;
                                    ishasword = true;
                                    break
                                }
                            }
                        }


                        if (!!ishasword) {

                            this.now_pool_words_arr.push(this.now_blank_words_arr[findKey]);
                            this.$set(this.now_blank_words_arr, findKey, "_____");
                        }


                        console.log("----选项池单词-----");
                        console.log(this.now_pool_words_arr);
                        console.log("----选项池单词-----");


                        console.log("----题干池单词-----");
                        console.log(this.now_blank_words_arr);
                        console.log("----题干池单词-----");


                    },//lack 删除单词

                    go_play_lack_sentence: function (e) {

                        var d = $(e.currentTarget);
                        d.find("span")[0].className = "glyphicon glyphicon-volume-up";


                        window.setTimeout(function () {
                            d.find("span")[0].className = "glyphicon glyphicon-volume-down";
                        }, 500);


                        var s = this.now_stage_json.english_sentence_no_dot;
                


                        var audio = document.createElement("audio");
                        audio.src = "https://dict.youdao.com/dictvoice?audio=" + s;
                        audio.play();
                    },//句子原声播放


                    check_review: function (bindKnowledge) {

                        var isHasReview = false;
                        for (var i = 0; i < this.course_learning_stage_json.length; i++) {

                            if (this.course_learning_stage_json[i].purport == bindKnowledge) {
                                isHasReview = true;
                                break;
                            }
                        }
                        return isHasReview;

                    },//绑定知识点检测


                    spell_init: function () {

                        var i = 0;


                        var word = this.now_stage_json.learn_word;


                        if (word.length <= 5) {



                            /*将单词字母逐个加入选项池*/
                            for (i = 0; i < word.length; i++) {
                                this.now_pool_words_arr.push(word.substring(i, i + 1))
                            }


                        } else {


                            var lastword = word;//lastword 是剩余字母


                            var jiequ = "";//截取字母


                            /*
                             *
                             * 随机字母选项
                             * */


                            for (i = 0; i < word.length; i++) {

                                if (lastword.length <= 3) {//当剩余字母小于3位时，不进行截取字母操作
                                    this.now_pool_words_arr.push(lastword);//截取字母添加到选项池数组
                                    break;
                                }


                                if (Math.random() >= 0.6) {
                                    jiequ = lastword.substring(0, 1);//从剩余字母中，截取1个字母
                                    lastword = lastword.substring(1, lastword.length);//剩余字母 去掉前1个字母
                                } else {
                                    jiequ = lastword.substring(0, 2);//从剩余字母中，截取2个字母
                                    lastword = lastword.substring(2, lastword.length);//剩余字母 去掉前两个字母
                                }


                                this.now_pool_words_arr.push(jiequ);//截取字母添加到选项池数组


                                jiequ = "";

                            }


                        }


                        for (i = 0; i < word.length; i++) {
                            this.spell_underline_str += "_";//下划线
                        }

                        this.spell_show_str = this.spell_clicked_zimu_arr.join("") + this.spell_underline_str;//显示=已选字母+下划线


                        this.now_pool_words_arr.sort(function () {
                            return 0.5 - Math.random()
                        });//选项组打乱顺序


                    },


                    spell_click_word: function (key, word) {


                        var click_zimu = this.now_pool_words_arr[key];//获取用户选择的字母

                        this.spell_clicked_zimu_arr.push(click_zimu);//加入已选字母


                        this.now_pool_words_arr.splice(key, 1);//选项池删除这个字母


                        this.spell_underline_str = this.spell_underline_str.substring(0, this.spell_underline_str.length - (click_zimu.length));//下划线缩短

                        this.spell_show_str = this.spell_clicked_zimu_arr.join("") + this.spell_underline_str;//显示=已选字母+下划线


                        //console.log(this.spell_show_str);


                    },//spell 点击单词

                    spell_detele_word: function (e) {


                        var btn = $(e.currentTarget);

                        btn.css("background", "white");
                        btn.css("color", "#364B9A");


                        window.setTimeout(function () {
                            btn.css("background", "#364B9A");
                            btn.css("color", "white");
                        }, 50);


                        if (this.spell_clicked_zimu_arr.length > 0) {
                            var delete_zimu = this.spell_clicked_zimu_arr.pop();//删除字母

                            this.now_pool_words_arr.push(delete_zimu);//回归选项池


                            //下划线增加
                            for (var i = 0; i < delete_zimu.length; i++) {

                                this.spell_underline_str += "_"
                            }


                            this.spell_show_str = this.spell_clicked_zimu_arr.join("") + this.spell_underline_str;//显示=已选字母+下划线

                        }


                    },

                    wordchoice_init: function () {


                        var now_explain = this.now_stage_json.word_explain.replace(/\s*/g, "");//清理所有空格;


                        this.wordchoice_option_array.push(now_explain);//选项组推入当前单词释义


                        /*
                         *
                         * 整理本课释义组
                         * */


                        var word_explain_array = this.course_word_explain_array.concat();//本课单词释义数组.

                        var delete_index;//删除指针

                        delete_index = word_explain_array.indexOf(now_explain);//在所有单词释义数组中，找到与当前单词相同的释义
                        if (delete_index != -1) {
                            word_explain_array.splice(delete_index, 1);//删除同释义元素
                        }


                        /*
                         *
                         * 整理备用释义组。
                         * */


                        var wordchoice_standby_explain_array = this.wordchoice_standby_explain_array.concat();//备用释义组


                        delete_index = wordchoice_standby_explain_array.indexOf(now_explain);//在备用释义组中，找到与当前单词相同的释义
                        if (delete_index != -1) {
                            wordchoice_standby_explain_array.splice(delete_index, 1);//删除同释义元素
                        }


                        /*
                         *
                         *
                         * 若本课释义大于5,在本课释义组中挑选
                         *
                         * */


                        if (word_explain_array.length >= 5) {

                            for (var i = 0; i < 2; i++) {

                                var index = Math.floor((Math.random() * word_explain_array.length));//随机挑选一个释义


                                this.wordchoice_option_array.push(word_explain_array[index]);//push进选项组

                                word_explain_array.splice(index, 1);//删除该释义

                            }


                        } else {


                            for (i = 0; i < 2; i++) {

                                index = Math.floor((Math.random() * wordchoice_standby_explain_array.length));//随机挑选一个释义


                                this.wordchoice_option_array.push(wordchoice_standby_explain_array[index]);//push进选项组

                                wordchoice_standby_explain_array.splice(index, 1);//删除该释义

                            }


                        }


                        /*
                         *
                         * 选项重排
                         * */


                        this.wordchoice_option_array.sort(function () {
                            return Math.random() > .5 ? -1 : 1;
                        });


                    },

                    wordchoice_click: function (e) {

                        var now_explain = this.now_stage_json.word_explain.replace(/\s*/g, "");

                        if (this.answer_status == "answering") {
                            var _this = this;
                            var target = $(e.currentTarget);
                            var user_answer_explain = target[0].innerText.replace(/\s*/g, "");


                            if (user_answer_explain == now_explain) {

                                this.play_sound_effect(this.sound_right);
                                target.attr("style", "background:limegreen");
                                this.answer_status = "answering_end";


                                window.setTimeout(function () {
                                    _this.answer_status = "right";
                                    target.attr("style", "background:''");
                                    _this.stage_walk();

                                }, 1000);
                                //答对

                            } else {


                                this.play_sound_effect(this.sound_wrong);//错误提示音


                                var isHasReview = this.check_review(this.now_stage_json.bindKnowledge);//检查该题有没有绑定知识点

                                if (this.now_stage_json.review_go_num == 0 || !this.now_stage_json.bindKnowledge || !isHasReview) {

                                    //没有找到绑定知识点


                                    target.attr("style", "background:#FB667A");
                                    this.answer_status = "answering_end";//状态变为answering_end


                                    /*
                                     * 1秒后显示正确答案  显示操作按钮
                                     * */


                                    window.setTimeout(function () {

                                        _this.answer_status = "no_review";

                                        target.attr("style", "background:''");

                                    }, 1000);

                                } else {
                                    target.attr("style", "background:#FB667A");
                                    this.answer_status = "answering_end";
                                    window.setTimeout(function () {
                                        _this.answer_status = "wrong";
                                        target.attr("style", "background:''");
                                    }, 1000);
                                }


                            }


                        }

                    },


                    choice_click: function (e) {

                        if (this.answer_status == "answering") {
                            var _this = this;
                            var target = $(e.currentTarget);
                            var user_answer_key = parseInt(target.attr("answer_key"));

                            var right_answer_key = parseInt(this.now_stage_json.right_option);

                            if (right_answer_key == user_answer_key) {

                                this.play_sound_effect(this.sound_right);
                                this.answer_status = "right";

                                target.attr("style", "background:limegreen");

                                window.setTimeout(function () {
                                    _this.stage_walk();

                                }, 1000);
                                //答对

                            } else {
                                this.play_sound_effect(this.sound_wrong);
                                var isHasReview = this.check_review(this.now_stage_json.bindKnowledge);


                                if (this.now_stage_json.review_go_num == 0 || !this.now_stage_json.bindKnowledge || !isHasReview) {
                                    this.answer_status = "no_review";
                                } else {
                                    this.answer_status = "wrong";
                                }

                                target.attr("style", "background:#FB667A");

                                var right_answer_div = $($(".choice_foot").children("div")[right_answer_key]);
                                right_answer_div.attr("style", "background:limegreen");

                                //console.log(right_answer_div);
                            }
                        }

                    },


                    /*



                     错误记录函数





                     * */


                    mistake_record: function () {
                        //alert("记录此题");
                        //alert(this.now_stage_json.stageType);


                        switch (this.now_stage_json.stageType) {
                            case "wordchoice":
                                //alert(this.now_stage_json.learn_word);

                                if (this.mistake_word_arr.indexOf(this.now_stage_json.learn_word) == -1) {

                                    this.mistake_word_arr.push(this.now_stage_json.learn_word);

                                }

                                console.log("错误单词组：");
                                console.log(this.mistake_word_arr);
                                break;
                            case "lack":
                                //alert(this.now_stage_json.english_sentence_no_dot);


                                if (this.mistake_sentence_arr.indexOf(this.now_stage_json.english_sentence_no_dot) == -1) {

                                    this.mistake_sentence_arr.push(this.now_stage_json.english_sentence_no_dot);

                                }

                                console.log("错误句组：");
                                console.log(this.mistake_sentence_arr);

                                break;
                            case "spell":
                                //alert(this.now_stage_json.learn_word);

                                if (this.mistake_word_arr.indexOf(this.now_stage_json.learn_word) == -1) {

                                    this.mistake_word_arr.push(this.now_stage_json.learn_word);

                                }

                                console.log("错误单词组：");
                                console.log(this.mistake_word_arr);

                                break;
                        }

                    },


                    voice_start_record: function () {

                        this.voice_status = "begin_record_cant_stop";


                        var _this = this;

                        wx.startRecord({
                            success: function () {//用户授权录音


                                _this.is_user_permit_to_record = true;//用户不允许录音


                                window.setTimeout(function () {
                                    _this.voice_status = "begin_record_can_stop";
                                }, 1500)
                            },
                            cancel: function () {

                                _this.is_user_permit_to_record = false;//用户不允许录音


                            }
                        });

                    },

                    voice_stop_record: function () {

                        this.voice_status = "end_record_no_assessment";//音频状态：结束录音但没有评价


                        var _this = this;

                        var assessInt = window.setInterval(function () {
                            _this.assessment_timer += 1;

                            if (_this.assessment_timer >= 8) {
                                _this.has_assessment = true;//评价结束
                                _this.voice_assessment_score = 30;//分数=30
                                _this.voice_assessment_text = "<60";//显示分数小于60
                                _this.voice_status = "end_record_has_assessment";//状态设为，录音结束有评价
                                //alert(2);
                                window.clearInterval(assessInt);

                            }

                        }, 1000);//检测语音识别时间，若超过8秒没有返回结果，则自动设置评价


                        wx.stopRecord({
                            success: function (res) {
                                _this.wx_voice_id = res.localId;


                                wx.uploadVoice({
                                    localId: _this.wx_voice_id, // 需要上传的音频的本地ID，由stopRecord接口获得
                                    isShowProgressTips: 0, // 默认为1，显示进度提示
                                    success: function (res) {


                                        var serverId = res.serverId; // 返回音频的服务器端ID
                                        //alert(serverId);

                                        _this.voice_speech_assessment_ajax({
                                            media_id: serverId,//media_id
                                            action: 'speech_assessment',
                                            user_id: _this.user_id,
                                            question_stem_1: _this.now_stage_json.question_stem_1//题干
                                        }, function (res) {

                                            //alert(res);

                                            if (!_this.has_assessment) {
                                                _this.has_assessment = true;//语音有评价
                                                _this.voice_assessment_score = parseInt(res);//获得得分

                                                if (_this.voice_assessment_score <= 0) {
                                                    _this.voice_assessment_text = "";

                                                } else if (_this.voice_assessment_score > 0 && _this.voice_assessment_score <= 60) {
                                                    _this.voice_assessment_text = "<60";//评分


                                                    wx.playVoice({
                                                        localId: _this.wx_voice_id // 需要播放的音频的本地ID，由stopRecord接口获得
                                                    });//播放原音
                                                } else {
                                                    _this.voice_assessment_text = _this.voice_assessment_score.toString();


                                                    wx.playVoice({
                                                        localId: _this.wx_voice_id // 需要播放的音频的本地ID，由stopRecord接口获得
                                                    });//播放原音
                                                }


                                                _this.voice_status = "end_record_has_assessment";


                                            }

                                            window.clearInterval(assessInt);


                                        }, function () {

                                            if (!_this.has_assessment) {
                                                _this.has_assessment = true;

                                                _this.voice_assessment_score = 0;
                                                _this.voice_assessment_text = "";


                                                _this.voice_status = "end_record_has_assessment";


                                            }


                                            window.clearInterval(assessInt);

                                        });


                                    }
                                });
                            }
                        });


                    },

                    voice_speech_assessment_ajax: function (data, callback, errorFun) {
                        var _this = this;


                        var doAjax = function () {
                            //ajax函数
                            $.ajax({
                                url: _this.course_ajax_url,
                                type: 'post',
                                data: data,
                                success: function (data, status) {
                                    var res = data.toString().trim();
                                    callback(res);

                                },
                                error: function () {
                                    errorFun()
                                }
                            });

                        };

                        doAjax();//执行AJAX

                    },

                    ori_voice_go: function () {

                        this.voice_status = "ori_voice_playing";
                        var _this = this;

                        var sen = this.now_stage_json.question_stem_1.replace(/[^\w\s]/g, '').replace(/\b(\w+)\b/g, ' $1').trim();//去掉标点并在每个单词前加空格
                        // 对英语句子进行URL编码，处理空格和特殊字符
                        var encodedSentence = encodeURIComponent(sen);
                        var url = "https://dict.youdao.com/dictvoice?audio=" + this.now_stage_json.question_stem_1;
                        console.log(url);
                        var audio = new Audio(url);
                        audio.play();




                        var audio_listener = window.setInterval(function () {

                            if (audio.paused) {
                                _this.voice_status = "no_movement";

                                window.clearInterval(audio_listener);
                            }
                        }, 100);

                    },//播放句子原声

                    voice_play_user_voice: function () {

                        var _this = this;


                        wx.playVoice({
                            localId: _this.wx_voice_id
                        });//播放用语音频

                        wx.onVoicePlayEnd({
                            success: function (res) {


                            }
                        });//监听音频结束


                    },

                    go_to_review_knowledge: function () {


                        /*
                         *  review_now_stage_json: {},//回顾模式页面json
                         review_page_index: 0,//回顾模式页面指针
                         review_has_go_num: 1,//回顾模式已走数
                         review_go_num: 0,//回顾模式应走数
                         *
                         * */

                        this.now_stage_model = 'review';//模式转换为 review


                        this.review_go_num = parseInt(this.now_stage_json.review_go_num);//行走步数


                        for (var i = 0; i < this.course_learning_stage_json.length; i++) {
                            if (this.course_learning_stage_json[i].purport == this.now_stage_json.bindKnowledge) {
                                this.review_page_index = i;//回顾模式页面指针
                                break;
                            }
                        }//获得review_begin_page_index


                        this.review_now_stage_json = this.course_learning_stage_json[this.review_page_index];//回顾模式页面json


                        this.course_audio.currentTime = this.course_audio_time_arr[this.review_page_index];//时间指针调整，找到下一个关卡的时间在时间数组的位置


                        this.course_audio.play();//播放音频

                        var _this = this;

                        var audio_listener = window.setInterval(function () {
                            //时间监听

                            var overTime = 0;

                            for (var i = _this.review_page_index + 1; i < _this.course_audio_time_arr.length; i++) {
                                if (_this.course_audio_time_arr[i] != "*") {
                                    overTime = _this.course_audio_time_arr[i];
                                    break;
                                }

                            }

                            if (overTime == 0) {
                                overTime = _this.course_audio.duration - 0.3;

                            }

                            //console.log(overTime);


                            if (_this.course_audio.currentTime >= overTime) {

                                if (_this.review_has_go_num >= _this.review_go_num) {
                                    //已走步数 与该走步数
                                    //console.log("到了");//结束
                                    _this.back_to_exe();
                                    window.clearInterval(audio_listener);//清理监听

                                } else {
                                    _this.review_page_index += 1;//页面指针+1
                                    _this.review_has_go_num += 1;//已走步数
                                    _this.review_now_stage_json = _this.course_learning_stage_json[_this.review_page_index];//当前关卡信息

                                }


                            }


                        }, 100);


                    },//进入回顾模式

                    back_to_exe: function () {

                        this.now_stage_json = this.course_learning_stage_json[this.pageindex];
                        this.now_stage_model = "exe";

                        this.audio_pause();//音频停止


                        this.review_data_clean();//复习参数重置

                        this.exercise_data_clean();//练习参数重置
                        this.exercise_init();//练习初始化

                    },//从回顾模式返回练习模式


                    review_data_clean: function () {

                        //console.log("清除复习数据");

                        this.review_now_stage_json = {};
                        this.review_page_index = 0;
                        this.review_has_go_num = 1;
                        this.review_go_num = 0;

                    },//清理回顾模式数据


                    course_restart: function () {

                        this.close_top_fixed();//关闭顶部信息

                        this.course_status = 2;//保存成功后将状态调为2，以免重复更新复习计划

                        this.isCourseOver = false;//弹出结束对话框

                        this.pageindex = 0;

                    },

                    go_back_to_cat: function () {

                        window.location.href = this.cat_url

                    },

                    go_back_to_today_review: function () {

                        window.location.href = this.today_review_url;

                    },


                    go_back_to_heika_page: function () {

                        window.location.href = this.heika_task_url;

                    },

                    save_user_review_mes: function () {

                        var _this = this;


                        this.save_review_mes_ajax({
                            user_id: _this.user_id,
                            isHeikaVip: _this.isHeikaVip,
                            course_status: _this.course_status,
                            nowTime: _this.nowTime,
                            action: 1,
                            post_id: _this.post_id,
                            model: _this.post_cat_id

                        }, function (res) {


                            res = JSON.parse(res);//转为json对象

                            console.log(res);

                            if (res.is_finish_heika_daily_learning_task == 1) {
                                //已完成任务

                                if (res.is_inform_user == 1) {
                                    _this.come_top_fixed("今日任务已完成");
                                }


                            } else {

                                switch (res.heika_daily_learning_task_content) {


                                    case "LEARN_TWO":

                                        _this.come_top_fixed("今日任务：学习课数(" + res.learn_num + "/2) ");

                                        break;

                                    case "LEARN_ONE_REVIEW_ONE":

                                        if (res.learn_num >= 1) {
                                            _this.come_top_fixed("今日任务：学习课数(1/1)  复习课数(0/1)");
                                        }

                                        if (res.review_num >= 1) {
                                            _this.come_top_fixed("今日任务：学习课数(0/1)  复习课数(1/1)");
                                        }


                                        break;
                                    case "FINISH_ALL_REVIEWING_CONTENT":

                                        _this.come_top_fixed("今日任务：还需复习" + res.last_today_review + "课");

                                        break;

                                }


                            }


                            _this.isCourseOver = true;//弹出结束对话框
                            _this.audio_pause();//音频停止

                        });
                    },//保存学习数据

                    save_review_mes_ajax: function (data, callback) {
                        var _this = this;


                        $$loading("加载中");

                        var doAjax = function () {
                            //ajax函数
                            $.ajax({
                                url: _this.course_review_ajax_url,
                                type: 'post',
                                data: data,
                                success: function (data, status) {
                                    _this.ajaxErrorNum = 0;//ajax错误状态归0
                                    $$closeLoading();


                                    //将结果转为字符串，去掉收尾空格
                                    var res = data.toString().trim();
                                    callback(res);

                                },
                                error: function () {
                                    _this.ajaxErrorNum += 1;//ajax错误状态递增
                                    if (_this.ajaxErrorNum >= 4) {
                                        _this.ajaxErrorNum = 0;//ajax错误状态归0
                                        $$closeLoading();

                                        $$confirm("网络错误", "网络不通畅，无法保存数据，请检查网络！是否再次保存？", function () {

                                            $$loading("加载中");//显示加载图案

                                            doAjax();//第二次以上执行
                                        });
                                    } else {
                                        doAjax();//第二次以上执行
                                    }
                                }
                            });

                        };

                        doAjax();//执行AJAX

                    },


                    save_user_course_rate_of_progress: function () {

                        var _this = this;

                        this.save_course_rate_of_progress_ajax({
                            user_id: _this.user_id,
                            action: 'save_course_rate_of_progress',
                            post_id: _this.post_id,
                            maxpaged: _this.maxpaged
                        }, function (res) {
                            console.log(res)
                        });
                    },//保存课程进度


                    save_course_rate_of_progress_ajax: function (data, callback) {
                        var _this = this;


                        var doAjax = function () {
                            //ajax函数
                            $.ajax({
                                url: _this.course_ajax_url,
                                type: 'post',
                                data: data,
                                success: function (data, status) {
                                    _this.ajaxErrorNum = 0;//ajax错误状态归0


                                    //将结果转为字符串，去掉收尾空格
                                    var res = data.toString().trim();
                                    callback(res);

                                },
                                error: function () {
                                    _this.ajaxErrorNum += 1;//ajax错误状态递增
                                    if (_this.ajaxErrorNum >= 4) {
                                        _this.ajaxErrorNum = 0;//ajax错误状态归0
                                    } else {
                                        doAjax();//第二次以上执行
                                    }
                                }
                            });

                        };

                        doAjax();//执行AJAX

                    },


                    /*
                     *
                     *
                     *
                     *
                     * 记录用户行为，每20秒一次数据通讯
                     * */

                    save_user_listening_behavior: function () {

                        var _this = this;

                        //统计最大翻页，进入课程时间，是否完成课程，停留时长


                        $.ajax({
                            url: _this.course_ajax_url,
                            type: 'post',
                            data: {
                                user_id: _this.user_id,//用户ID
                                post_id: _this.post_id,//课程ID
                                maxpaged: _this.this_time_maxpaged,//本次最大翻页
                                entertime: _this.nowTime,//进入课程时间
                                is_user_finish_the_course: _this.is_user_finish_the_course,//是否完成课程
                                stay_for_time: _this.stay_for_time,//停留时长
                                action: "user_listening_behavior"
                            },
                            success: function (data, status) {
                                console.log(data);

                            }
                        });


                    },


                    stage_walk: function () {


                        if (this.course_status == 1) {
                            this.go_over_index += 1;
                            this.pageindex = this.go_over_pageindex_arr[this.go_over_index];

                        } else {
                            this.pageindex += 1;
                        }

                    },

                    strSimilarity2Number: function (s, t) {
                        var n = s.length, m = t.length, d = [];
                        var i, j, s_i, t_j, cost;
                        if (n == 0) return m;
                        if (m == 0) return n;
                        for (i = 0; i <= n; i++) {
                            d[i] = [];
                            d[i][0] = i;
                        }
                        for (j = 0; j <= m; j++) {
                            d[0][j] = j;
                        }
                        for (i = 1; i <= n; i++) {
                            s_i = s.charAt(i - 1);
                            for (j = 1; j <= m; j++) {
                                t_j = t.charAt(j - 1);
                                if (s_i == t_j) {
                                    cost = 0;
                                } else {
                                    cost = 1;
                                }
                                d[i][j] = this.Minimum(d[i - 1][j] + 1, d[i][j - 1] + 1, d[i - 1][j - 1] + cost);
                            }
                        }
                        return d[n][m];
                    },


                    strSimilarity2Percent: function (s, t) {
                        var l = s.length > t.length ? s.length : t.length;
                        var d = this.strSimilarity2Number(s, t);
                        return (1 - d / l).toFixed(4);
                    },//两个字符串匹配相似度


                    /*
                     *
                     *
                     * 数组打乱重拍
                     * */

                    shuffle: function (arr) {
                        var length = arr.length,
                            randomIndex,
                            temp;
                        while (length) {
                            randomIndex = Math.floor(Math.random() * (length--));
                            temp = arr[randomIndex];
                            arr[randomIndex] = arr[length];
                            arr[length] = temp
                        }
                        return arr;
                    },

                    /*
                     *
                     * 将课程关卡中的时间换成时间戳
                     * */

                    audio_time_to_timestamp: function (audio_time) {


                        audio_time = audio_time.substring(1, audio_time.length - 1);//去掉[]


                        var audio_time_arr = audio_time.split(":");

                        return parseFloat((parseInt(audio_time_arr[0] * 60) + parseFloat(audio_time_arr[1])).toFixed(3));


                    },

                    Minimum: function (a, b, c) {
                        return a < b ? (a < c ? a : c) : (b < c ? b : c);
                    },

                    come_warning: function (text) {

                        this.warning_text = text;

                        this.show_warning = true;

                        var _this = this;

                        window.setTimeout(function () {
                            _this.show_warning = false;

                        }, 2000)

                    },


                    come_top_fixed: function (text) {

                        this.top_fixed_text = text;

                        this.show_top_fixed = true;

                        var _this = this;

                        window.setTimeout(function () {
                            _this.show_top_fixed = false;

                        }, 6000)

                    },


                    close_top_fixed: function () {

                        this.show_top_fixed = false;


                    },


                    test_forward: function () {

                        if (this.pageindex < this.course_learning_stage_json.length) {


                            this.slide_dir = "left";//滚动方向向左

                            this.audio_pause();


                            this.pageindex += 1;
                        }

                    },

                    test_back: function () {


                        if (this.pageindex > 0) {

                            this.slide_dir = "right";//滚动方向向左

                            this.audio_pause();


                            this.pageindex -= 1;

                        }

                    },

                    test_forward_in_review: function () {

                        this.go_over_index += 1;
                        this.pageindex = this.go_over_pageindex_arr[this.go_over_index];

                        console.log(this.go_over_pageindex_arr);
                        console.log("当前：" + this.pageindex.toString());

                    },

                    test_back_in_review: function () {
                        if (this.go_over_index > 0) {
                            this.go_over_index -= 1;
                            this.pageindex = this.go_over_pageindex_arr[this.go_over_index];

                        }

                        console.log(this.go_over_pageindex_arr);
                        console.log("当前：" + this.pageindex.toString());


                    },

                    copy_test_user_show_time: function () {


                        var t = $("#test_pause_time").html();
                        var oInput = document.createElement('input');
                        oInput.value = t;
                        document.body.appendChild(oInput);
                        oInput.select();
                        document.execCommand("Copy");
                        oInput.className = 'oInput';
                        oInput.style.display = 'none';
                        alert('复制成功');


                        //$("#test_pause_time")

                    }

                },

                computed: {},

                watch: {

                    isLoadingOver: function (n, o) {
                        if (n) {
                            this.now_stage_model = "course";
                        }

                    },//监听加载是否结束

                    progressNumber: function (newProgressNumber, oldProgressNumber) {
                        var _this = this;

                        /*百分比数字显示位置*/
                        var span = $(this.$refs.loadingPage_body_progress_span);
                        var right = "";

                        /*进度条显示位置*/

                        var img = $(this.$refs.loadingPage_body_progress_img);
                        var width = newProgressNumber.toString() + "%";

                        /*数字与进度条平滑增长特效*/


                        var timer = window.setInterval(function () {

                            _this.tweenedProgressNumber += 1;

                            span.html(_this.tweenedProgressNumber.toString() + "%");

                            right = (100 - _this.tweenedProgressNumber).toString() + "%";
                            span.css("marginRight", right);

                            width = _this.tweenedProgressNumber.toString() + "%";
                            img.css("width", width);

                            if (_this.tweenedProgressNumber >= newProgressNumber - 1) {
                                window.clearInterval(timer);
                            }


                        }, 10);


                        /*当进度条抵达100时，加载结束，准备课程*/


                        if (newProgressNumber >= 100) {

                            this.ready_to_into_course();//课程准备

                        }


                    },

                    pageindex: function (newValue, oldValue) {

                        console.log("pageindex:" + newValue);


                        /*
                         *
                         * 重置老师提问
                         *
                         * */


                        this.reset_ask_teacher();


                        /*
                         *
                         * 处理最大翻页
                         * */


                        if (this.course_status == 0) {

                            if (newValue > this.maxpaged) {

                                this.maxpaged = newValue;//翻页=pageindex

                                //每3页记录一次最大翻页
                                if (this.maxpaged % 3 == 0) {
                                    console.log("进度记录：" + this.maxpaged);
                                    console.log("最大翻页：" + this.maxpaged);

                                    if (newValue < this.course_learning_stage_json.length) {
                                        //结束页不需要记录
                                        this.save_user_course_rate_of_progress();//保存课程数据
                                    }

                                }
                            }

                        }


                        /*
                         * 处理本次最大翻页
                         *
                         * */


                        if (newValue > this.this_time_maxpaged) {
                            this.this_time_maxpaged = newValue;
                        }


                        /*
                         *
                         * 处理进度条
                         * */


                        this.course_has_go(newValue);


                        /*
                         *
                         * 处理页面
                         * */


                        if (newValue < this.course_learning_stage_json.length) {

                            /*
                             * 指针没有在最后，渲染页面
                             *
                             * course_audio_time_arr 是时间戳数组，元素值为 * 说明不需要播放音频，为习题
                             * */


                            var _this = this;

                            var timestamp = this.course_audio_time_arr[newValue];//时间戳

                            this.now_stage_json = this.course_learning_stage_json[newValue];//获得当前的关卡数据

                            if (timestamp == "*") {

                                /*进入习题*/


                                this.now_stage_model = "exe";//关卡模式改为习题


                                this.mistake_times = 0;//习题错误次数归0
                                console.log("此题错误数:" + this.mistake_times);

                                this.audio_pause();//音频停止

                                this.review_data_clean();//复习参数重置

                                this.exercise_data_clean();//练习参数重置

                                this.exercise_init();//练习初始化


                            } else {
                                this.now_stage_model = "course";//进入课程模式

                                if (this.is_course_audio_playing) {
                                    //当音频在播放的情况下，不需要再设置音频时间。

                                } else {
                                    //音频不播放的情况下，需要重新设置音频时间

                                    this.course_audio.currentTime = timestamp;//设置音频时间
                                    this.audio_play();//音频播放

                                }

                                window.setTimeout(function () {
                                    _this.slide_dir = 'left';
                                }, 500);//调回正确的滚动方向
                            }


                        } else {

                            this.save_user_listening_behavior();//记录用户行为


                            /*
                             * 课程结束
                             * */


                            this.now_stage_model = "over";


                            /*
                             *
                             * 用户行为：是否完课 is_user_finish_the_course
                             * */

                            this.is_user_finish_the_course = 1;


                            if (this.course_status == 0 || this.course_status == 1) {
                                //0表示没有学习过， 1表示今日需复习


                                this.save_user_review_mes();//记忆系统数据保存


                            } else {

                                this.isCourseOver = true;//弹出结束对话框
                                this.audio_pause();//音频停止

                            }


                        }


                    },//监听页面指针

                    now_blank_words_arr: function (n, o) {
                        var _this = this;
                        var hasBlank = false;//是否有空格
                        var rightAnswerStr = "";//正确单词
                        var isHasReview = this.check_review(this.now_stage_json.bindKnowledge);//检查是否有复习内容

                        if (this.now_stage_json.stageType == 'lack') {

                            rightAnswerStr = this.now_stage_json.english_sentence_has_dot;


                            for (var l = 0; l < n.length; l++) {
                                if (n[l] == "_____") {
                                    hasBlank = true;
                                    break
                                }
                            }


                            if (!hasBlank) {
                                //答对
                                if (n.join(" ") == rightAnswerStr) {
                                    this.play_sound_effect(this.sound_right);
                                    this.answer_status = "right";
                                    window.setTimeout(function () {
                                        _this.stage_walk();//关卡行走
                                    }, 1000)


                                } else {
                                    this.play_sound_effect(this.sound_wrong);
                                    this.answer_status = "wrong";

                                    if (this.now_stage_json.review_go_num == 0 || !this.now_stage_json.bindKnowledge || !isHasReview) {
                                        window.setTimeout(function () {
                                            _this.answer_status = "no_review";
                                        }, 1000)
                                    } else {
                                        window.setTimeout(function () {
                                            _this.answer_status = "go_right";
                                        }, 1000)
                                    }


                                }


                            }
                        }

                    },

                    spell_show_str: function (n, o) {

                        if (this.now_stage_json.stageType == 'spell') {

                            var isHasReview = this.check_review(this.now_stage_json.bindKnowledge);


                            var _this = this;
                            var rightAnswerStr = this.now_stage_json.learn_word;
                            var hasBlank = false;

                            if (n.indexOf("_") != -1) {
                                hasBlank = true;
                            }


                            if (!hasBlank) {
                                //答对
                                if (n == rightAnswerStr) {
                                    this.play_sound_effect(_this.sound_right);
                                    this.answer_status = "right";
                                    window.setTimeout(function () {
                                        _this.stage_walk();//关卡行走
                                    }, 1000)

                                } else {

                                    this.play_sound_effect(_this.sound_wrong);
                                    this.answer_status = "wrong";


                                    if (this.now_stage_json.review_go_num == 0 || !this.now_stage_json.bindKnowledge || !isHasReview) {
                                        window.setTimeout(function () {
                                            _this.answer_status = "no_review";
                                        }, 1000)
                                    } else {
                                        window.setTimeout(function () {
                                            _this.answer_status = "go_right";
                                        }, 1000)
                                    }


                                }


                            }


                        }

                    }

                }
            });

            /*
             * 开始课程
             * */


            container.course_init();


            /*
             * 禁止屏幕休眠
             * */


            var noSleep = new NoSleep();

            function enableNoSleep() {
                noSleep.enable();
                document.removeEventListener('click', enableNoSleep, false);
            }

            // Enable wake lock.
            // (must be wrapped in a user input event handler e.g. a mouse or touch handler)
            document.addEventListener('click', enableNoSleep, false);

            // ...

            // Disable wake lock at some point in the future.
            // (does not need to be wrapped in any user input event handler)
            noSleep.disable();


        </script>


        <?php


    } else {

        ?>

        <style>
            .goVip {
                width: 100%;
                position: fixed;
                top: 20%;
                left: 0px;
            }

            .goVip div {
                width: 100%;
                float: left;
                text-align: center;
                color: gray;
                font-size: 14px;

            }

            .goVip img {
                width: 40%;
                display: block;
                margin-left: 30%;
                float: left;

            }

            .goVip button {
                width: 50%;
                float: left;
                display: block;
                border: 0px;
                background-color: limegreen;
                color: white;
                font-size: 16px;
                margin-top: 40px;
                margin-left: 25%;
                height: 40px;
                border-radius: 10px;
                text-align: center;
                line-height: 40px;
            }
        </style>


        <!--提示升级黑卡-->
        <div class="goVip">
            <div>
                <img src="/img/goVip.png"/>
            </div>
            <div>该课程需要开通VIP权限</div>
            <div style="margin-top: 10px"><?php echo $vipaction_text; ?></div>
            <div>


                <a href="<?php echo $vipaction_url; ?>">
                    <button>开通黑卡VIP</button>
                </a>
            </div>
        </div>
        <!--提示升级黑卡-->


        <?php


        //不是VIP


    }
}


get_footer();//底部
?>




