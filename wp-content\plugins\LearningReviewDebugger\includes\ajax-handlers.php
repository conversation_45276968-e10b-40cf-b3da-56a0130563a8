<?php
/**
 * Learning Review Debugger AJAX处理函数
 * 
 * 这个文件包含所有与学习复习调试相关的AJAX处理函数，负责处理来自调试器页面的请求。
 * 主要功能包括用户数据获取、复习计划管理、学习进度调试等。
 * 
 * 迁移说明：
 * 此文件从 wp-content/themes/shuimitao.online/plugins/functions/LearningReviewDebuggerAjax.php 迁移而来
 * 迁移到插件内部，确保功能独立于主题，提高代码的可维护性
 * 
 * @package LearningReviewDebugger
 * @version 2.0.0
 * <AUTHOR>
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

// 记录插件AJAX处理器加载
error_log('[LearningReviewDebugger] AJAX处理器开始加载');

/**
 * 获取用户基本数据的AJAX处理函数
 * 
 * 返回用户的各种元数据，包括复习时间、黑卡学习任务内容、打卡详情等
 */
function lrd_admin_review_get_user_data() {
    error_log('[LearningReviewDebugger] lrd_admin_review_get_user_data AJAX请求开始处理');
    
    // 验证用户权限
    if (!current_user_can('manage_options')) {
        error_log('[LearningReviewDebugger] 权限验证失败 - 用户无管理员权限');
        echo 0;
        wp_die();
    }
    
    $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
    error_log('[LearningReviewDebugger] 查询用户ID: ' . $user_id);
    
    // 验证参数
    if ($user_id <= 0) {
        error_log('[LearningReviewDebugger] 用户ID无效: ' . $user_id);
        echo 0;
        wp_die();
    }
    
    // 获取用户信息
    $user = get_user_by('id', $user_id);
    if (!$user) {
        error_log('[LearningReviewDebugger] 用户不存在: ' . $user_id);
        echo 0;
        wp_die();
    }
    
    error_log('[LearningReviewDebugger] 用户信息获取成功: ' . $user->display_name);
    
    // 获取用户订单信息
    $user_orders = get_user_meta($user_id, 'user_orders', true);
    $user_orders = $user_orders ? $user_orders : "";
    
    // 获取返现次数
    $cash_back_times = get_user_meta($user_id, 'cash_back_times', true);
    $cash_back_times = $cash_back_times ? $cash_back_times : "";
    
    // 获取学习天数返现机会
    $total_learning_days_cash_back_chance = get_user_meta($user_id, 'total_learning_days_cash_back_chance', true);
    $total_learning_days_cash_back_chance = $total_learning_days_cash_back_chance ? $total_learning_days_cash_back_chance : 0;
    
    // 获取复习时间和黑卡数据更新时间
    $alter_review_time = get_user_meta($user_id, 'alter_review_time', true);
    $alter_review_time = $alter_review_time ? $alter_review_time : 0;
    
    $alter_heika_data_time = get_user_meta($user_id, 'alter_heika_data_time', true);
    $alter_heika_data_time = $alter_heika_data_time ? $alter_heika_data_time : 0;
    
    // 获取黑卡学习任务内容
    $heika_daily_learning_task_content = get_user_meta($user_id, 'heika_daily_learning_task_content', true);
    $heika_daily_learning_task_content = $heika_daily_learning_task_content ? $heika_daily_learning_task_content : "";
    
    // 获取黑卡学习任务总数据
    $heika_daily_learning_task_content_total = get_user_meta($user_id, 'heika_daily_learning_task_content_total', true);
    $heika_daily_learning_task_content_total = $heika_daily_learning_task_content_total ? $heika_daily_learning_task_content_total : "";
    
    // 获取黑卡打卡详情
    $heika_check_in_detail = get_user_meta($user_id, 'heika_check_in_detail', true);
    $heika_check_in_detail = $heika_check_in_detail ? $heika_check_in_detail : "";
    
    // 获取学习详情
    $current_user_learn_detail = get_user_meta($user_id, 'current_user_learn_detail', true);
    $current_user_learn_detail = $current_user_learn_detail ? $current_user_learn_detail : "";
    
    // 获取今日学习详情
    $current_user_today_learn_detail = get_user_meta($user_id, 'current_user_today_learn_detail', true);
    $current_user_today_learn_detail = $current_user_today_learn_detail ? $current_user_today_learn_detail : "";
    
    // 获取复习计划
    $review_mes = get_user_meta($user_id, 'review_mes', true);
    $review_mes = $review_mes ? $review_mes : "";
    
    // 获取课程进度
    $course_rate_of_progress = get_user_meta($user_id, 'course_rate_of_progress', true);
    $course_rate_of_progress = $course_rate_of_progress ? $course_rate_of_progress : "";
    
    // 获取分类访问时间
    $cat_view_time = get_user_meta($user_id, 'cat_view_time', true);
    $cat_view_time = $cat_view_time ? $cat_view_time : "";
    
    // 构建返回数据
    $response = array(
        'user_id' => $user_id,
        'user_nickname' => $user->display_name,
        'user_orders' => $user_orders,
        'cash_back_times' => $cash_back_times,
        'total_learning_days_cash_back_chance' => $total_learning_days_cash_back_chance,
        'alter_review_time' => $alter_review_time,
        'alter_heika_data_time' => $alter_heika_data_time,
        'heika_daily_learning_task_content' => $heika_daily_learning_task_content,
        'heika_daily_learning_task_content_total' => $heika_daily_learning_task_content_total,
        'heika_check_in_detail' => $heika_check_in_detail,
        'current_user_learn_detail' => $current_user_learn_detail,
        'current_user_today_learn_detail' => $current_user_today_learn_detail,
        'review_mes' => $review_mes,
        'course_rate_of_progress' => $course_rate_of_progress,
        'cat_view_time' => $cat_view_time
    );
    
    error_log('[LearningReviewDebugger] 用户数据查询完成，返回字段数: ' . count($response));
    
    // 返回JSON格式的数据
    echo json_encode($response);
    wp_die();
}
// 注册AJAX处理函数
add_action('wp_ajax_admin_review_get_user_data', 'lrd_admin_review_get_user_data');
error_log('[LearningReviewDebugger] admin_review_get_user_data AJAX action已注册');

/**
 * 记录分类访问的AJAX处理函数
 */
function lrd_admin_record_category_visit() {
    error_log('[LearningReviewDebugger] lrd_admin_record_category_visit AJAX请求开始处理');
    
    // 验证用户权限
    if (!current_user_can('manage_options')) {
        error_log('[LearningReviewDebugger] 权限验证失败 - 用户无管理员权限');
        echo 0;
        wp_die();
    }
    
    $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
    $cat_id = isset($_POST['cat_id']) ? intval($_POST['cat_id']) : 0;
    
    error_log('[LearningReviewDebugger] 记录分类访问 - 用户ID: ' . $user_id . ', 分类ID: ' . $cat_id);
    
    // 验证参数
    if ($user_id <= 0 || $cat_id <= 0) {
        error_log('[LearningReviewDebugger] 参数验证失败 - 用户ID: ' . $user_id . ', 分类ID: ' . $cat_id);
        echo 0;
        wp_die();
    }
    
    // 获取用户信息
    $user = get_user_by('id', $user_id);
    if (!$user) {
        error_log('[LearningReviewDebugger] 用户不存在: ' . $user_id);
        echo 0;
        wp_die();
    }
    
    // 获取当前分类访问时间数据
    $cat_view_time = get_user_meta($user_id, 'cat_view_time', true);
    $cat_view_time = $cat_view_time ? $cat_view_time : "";
    
    // 解析现有数据
    $cat_view_array = array();
    if (!empty($cat_view_time)) {
        $cat_view_array = json_decode($cat_view_time, true);
        if (!is_array($cat_view_array)) {
            $cat_view_array = array();
        }
    }
    
    // 添加新的访问记录
    $current_time = current_time("timestamp");
    $cat_view_array[] = array(
        'cat_id' => $cat_id,
        'visit_time' => $current_time
    );
    
    // 保存更新后的数据
    $new_cat_view_time = json_encode($cat_view_array);
    $result = update_user_meta($user_id, 'cat_view_time', $new_cat_view_time);
    
    error_log('[LearningReviewDebugger] 分类访问记录更新结果: ' . ($result ? '成功' : '失败'));
    error_log('[LearningReviewDebugger] 新增访问记录 - 分类ID: ' . $cat_id . ', 时间: ' . date('Y-m-d H:i:s', $current_time));
    
    echo $result ? 1 : 0;
    wp_die();
}
// 注册AJAX处理函数
add_action('wp_ajax_admin_record_category_visit', 'lrd_admin_record_category_visit');
error_log('[LearningReviewDebugger] admin_record_category_visit AJAX action已注册');

/**
 * 重置分类访问时间的AJAX处理函数
 */
function lrd_admin_reset_category_visit() {
    error_log('[LearningReviewDebugger] lrd_admin_reset_category_visit AJAX请求开始处理');
    
    // 验证用户权限
    if (!current_user_can('manage_options')) {
        error_log('[LearningReviewDebugger] 权限验证失败 - 用户无管理员权限');
        echo 0;
        wp_die();
    }
    
    $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
    error_log('[LearningReviewDebugger] 重置分类访问时间 - 用户ID: ' . $user_id);
    
    // 验证参数
    if ($user_id <= 0) {
        error_log('[LearningReviewDebugger] 用户ID无效: ' . $user_id);
        echo 0;
        wp_die();
    }
    
    // 获取用户信息
    $user = get_user_by('id', $user_id);
    if (!$user) {
        error_log('[LearningReviewDebugger] 用户不存在: ' . $user_id);
        echo 0;
        wp_die();
    }
    
    // 重置分类访问时间为空
    $result = update_user_meta($user_id, 'cat_view_time', '');
    
    error_log('[LearningReviewDebugger] 分类访问时间重置结果: ' . ($result ? '成功' : '失败'));
    
    echo $result ? 1 : 0;
    wp_die();
}
// 注册AJAX处理函数
add_action('wp_ajax_admin_reset_category_visit', 'lrd_admin_reset_category_visit');
error_log('[LearningReviewDebugger] admin_reset_category_visit AJAX action已注册');

/**
 * 获取用户复习信息的AJAX处理函数
 */
function lrd_admin_review_get_user_review_mes() {
    error_log('[LearningReviewDebugger] lrd_admin_review_get_user_review_mes AJAX请求开始处理');

    // 验证用户权限
    if (!current_user_can('manage_options')) {
        error_log('[LearningReviewDebugger] 权限验证失败 - 用户无管理员权限');
        echo 0;
        wp_die();
    }

    $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
    error_log('[LearningReviewDebugger] 查询用户复习信息 - 用户ID: ' . $user_id);

    // 验证参数
    if ($user_id <= 0) {
        error_log('[LearningReviewDebugger] 用户ID无效: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 获取用户信息
    $user = get_user_by('id', $user_id);
    if (!$user) {
        error_log('[LearningReviewDebugger] 用户不存在: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 获取复习信息
    $review_mes = get_user_meta($user_id, 'review_mes', true);
    $review_mes = $review_mes ? $review_mes : "";

    error_log('[LearningReviewDebugger] 复习信息查询完成，数据长度: ' . strlen($review_mes));

    echo $review_mes;
    wp_die();
}
// 注册AJAX处理函数
add_action('wp_ajax_admin_review_get_user_review_mes', 'lrd_admin_review_get_user_review_mes');
error_log('[LearningReviewDebugger] admin_review_get_user_review_mes AJAX action已注册');

/**
 * 重置复习时间的AJAX处理函数
 */
function lrd_reset_alter_review_time() {
    error_log('[LearningReviewDebugger] lrd_reset_alter_review_time AJAX请求开始处理');

    // 验证用户权限
    if (!current_user_can('manage_options')) {
        error_log('[LearningReviewDebugger] 权限验证失败 - 用户无管理员权限');
        echo 0;
        wp_die();
    }

    $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
    error_log('[LearningReviewDebugger] 重置复习时间 - 用户ID: ' . $user_id);

    // 验证参数
    if ($user_id <= 0) {
        error_log('[LearningReviewDebugger] 用户ID无效: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 获取用户信息
    $user = get_user_by('id', $user_id);
    if (!$user) {
        error_log('[LearningReviewDebugger] 用户不存在: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 重置复习时间为0
    $result = update_user_meta($user_id, 'alter_review_time', 0);

    error_log('[LearningReviewDebugger] 复习时间重置结果: ' . ($result ? '成功' : '失败'));

    echo $result ? 1 : 0;
    wp_die();
}
// 注册AJAX处理函数
add_action('wp_ajax_reset_alter_review_time', 'lrd_reset_alter_review_time');
error_log('[LearningReviewDebugger] reset_alter_review_time AJAX action已注册');

/**
 * 重置黑卡数据时间的AJAX处理函数
 */
function lrd_reset_alter_heika_data_time() {
    error_log('[LearningReviewDebugger] lrd_reset_alter_heika_data_time AJAX请求开始处理');

    // 验证用户权限
    if (!current_user_can('manage_options')) {
        error_log('[LearningReviewDebugger] 权限验证失败 - 用户无管理员权限');
        echo 0;
        wp_die();
    }

    $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
    error_log('[LearningReviewDebugger] 重置黑卡数据时间 - 用户ID: ' . $user_id);

    // 验证参数
    if ($user_id <= 0) {
        error_log('[LearningReviewDebugger] 用户ID无效: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 获取用户信息
    $user = get_user_by('id', $user_id);
    if (!$user) {
        error_log('[LearningReviewDebugger] 用户不存在: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 重置黑卡数据时间为0
    $result = update_user_meta($user_id, 'alter_heika_data_time', 0);

    error_log('[LearningReviewDebugger] 黑卡数据时间重置结果: ' . ($result ? '成功' : '失败'));

    echo $result ? 1 : 0;
    wp_die();
}
// 注册AJAX处理函数
add_action('wp_ajax_reset_alter_heika_data_time', 'lrd_reset_alter_heika_data_time');
error_log('[LearningReviewDebugger] reset_alter_heika_data_time AJAX action已注册');

/**
 * 重置用户学习详情的AJAX处理函数
 */
function lrd_reset_current_user_learn_detail() {
    error_log('[LearningReviewDebugger] lrd_reset_current_user_learn_detail AJAX请求开始处理');

    // 验证用户权限
    if (!current_user_can('manage_options')) {
        error_log('[LearningReviewDebugger] 权限验证失败 - 用户无管理员权限');
        echo 0;
        wp_die();
    }

    $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
    error_log('[LearningReviewDebugger] 重置用户学习详情 - 用户ID: ' . $user_id);

    // 验证参数
    if ($user_id <= 0) {
        error_log('[LearningReviewDebugger] 用户ID无效: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 获取用户信息
    $user = get_user_by('id', $user_id);
    if (!$user) {
        error_log('[LearningReviewDebugger] 用户不存在: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 重置学习详情为空
    $result = update_user_meta($user_id, 'current_user_learn_detail', '');

    error_log('[LearningReviewDebugger] 用户学习详情重置结果: ' . ($result ? '成功' : '失败'));

    echo $result ? 1 : 0;
    wp_die();
}
// 注册AJAX处理函数
add_action('wp_ajax_reset_current_user_learn_detail', 'lrd_reset_current_user_learn_detail');
error_log('[LearningReviewDebugger] reset_current_user_learn_detail AJAX action已注册');

/**
 * 重置用户今日学习详情的AJAX处理函数
 */
function lrd_reset_current_user_today_learn_detail() {
    error_log('[LearningReviewDebugger] lrd_reset_current_user_today_learn_detail AJAX请求开始处理');

    // 验证用户权限
    if (!current_user_can('manage_options')) {
        error_log('[LearningReviewDebugger] 权限验证失败 - 用户无管理员权限');
        echo 0;
        wp_die();
    }

    $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
    error_log('[LearningReviewDebugger] 重置用户今日学习详情 - 用户ID: ' . $user_id);

    // 验证参数
    if ($user_id <= 0) {
        error_log('[LearningReviewDebugger] 用户ID无效: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 获取用户信息
    $user = get_user_by('id', $user_id);
    if (!$user) {
        error_log('[LearningReviewDebugger] 用户不存在: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 重置今日学习详情为空
    $result = update_user_meta($user_id, 'current_user_today_learn_detail', '');

    error_log('[LearningReviewDebugger] 用户今日学习详情重置结果: ' . ($result ? '成功' : '失败'));

    echo $result ? 1 : 0;
    wp_die();
}
// 注册AJAX处理函数
add_action('wp_ajax_reset_current_user_today_learn_detail', 'lrd_reset_current_user_today_learn_detail');
error_log('[LearningReviewDebugger] reset_current_user_today_learn_detail AJAX action已注册');

/**
 * 重置用户黑卡打卡详情的AJAX处理函数
 */
function lrd_reset_current_user_heika_check_in_detail() {
    error_log('[LearningReviewDebugger] lrd_reset_current_user_heika_check_in_detail AJAX请求开始处理');

    // 验证用户权限
    if (!current_user_can('manage_options')) {
        error_log('[LearningReviewDebugger] 权限验证失败 - 用户无管理员权限');
        echo 0;
        wp_die();
    }

    $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
    error_log('[LearningReviewDebugger] 重置用户黑卡打卡详情 - 用户ID: ' . $user_id);

    // 验证参数
    if ($user_id <= 0) {
        error_log('[LearningReviewDebugger] 用户ID无效: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 获取用户信息
    $user = get_user_by('id', $user_id);
    if (!$user) {
        error_log('[LearningReviewDebugger] 用户不存在: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 重置黑卡打卡详情为空
    $result = update_user_meta($user_id, 'heika_check_in_detail', '');

    error_log('[LearningReviewDebugger] 用户黑卡打卡详情重置结果: ' . ($result ? '成功' : '失败'));

    echo $result ? 1 : 0;
    wp_die();
}
// 注册AJAX处理函数
add_action('wp_ajax_reset_current_user_heika_check_in_detail', 'lrd_reset_current_user_heika_check_in_detail');
error_log('[LearningReviewDebugger] reset_current_user_heika_check_in_detail AJAX action已注册');

/**
 * 重置用户黑卡日常学习任务内容的AJAX处理函数
 */
function lrd_reset_current_user_heika_daily_learning_task_content() {
    error_log('[LearningReviewDebugger] lrd_reset_current_user_heika_daily_learning_task_content AJAX请求开始处理');

    // 验证用户权限
    if (!current_user_can('manage_options')) {
        error_log('[LearningReviewDebugger] 权限验证失败 - 用户无管理员权限');
        echo 0;
        wp_die();
    }

    $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
    error_log('[LearningReviewDebugger] 重置用户黑卡日常学习任务内容 - 用户ID: ' . $user_id);

    // 验证参数
    if ($user_id <= 0) {
        error_log('[LearningReviewDebugger] 用户ID无效: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 获取用户信息
    $user = get_user_by('id', $user_id);
    if (!$user) {
        error_log('[LearningReviewDebugger] 用户不存在: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 重置黑卡日常学习任务内容为空
    $result = update_user_meta($user_id, 'heika_daily_learning_task_content', '');

    error_log('[LearningReviewDebugger] 用户黑卡日常学习任务内容重置结果: ' . ($result ? '成功' : '失败'));

    echo $result ? 1 : 0;
    wp_die();
}
// 注册AJAX处理函数
add_action('wp_ajax_reset_current_user_heika_daily_learning_task_content', 'lrd_reset_current_user_heika_daily_learning_task_content');
error_log('[LearningReviewDebugger] reset_current_user_heika_daily_learning_task_content AJAX action已注册');

/**
 * 重置用户黑卡日常学习任务总数据的AJAX处理函数
 */
function lrd_reset_current_user_heika_daily_learning_task_content_total() {
    error_log('[LearningReviewDebugger] lrd_reset_current_user_heika_daily_learning_task_content_total AJAX请求开始处理');

    // 验证用户权限
    if (!current_user_can('manage_options')) {
        error_log('[LearningReviewDebugger] 权限验证失败 - 用户无管理员权限');
        echo 0;
        wp_die();
    }

    $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
    error_log('[LearningReviewDebugger] 重置用户黑卡日常学习任务总数据 - 用户ID: ' . $user_id);

    // 验证参数
    if ($user_id <= 0) {
        error_log('[LearningReviewDebugger] 用户ID无效: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 获取用户信息
    $user = get_user_by('id', $user_id);
    if (!$user) {
        error_log('[LearningReviewDebugger] 用户不存在: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 重置黑卡日常学习任务总数据为空
    $result = update_user_meta($user_id, 'heika_daily_learning_task_content_total', '');

    error_log('[LearningReviewDebugger] 用户黑卡日常学习任务总数据重置结果: ' . ($result ? '成功' : '失败'));

    echo $result ? 1 : 0;
    wp_die();
}
// 注册AJAX处理函数
add_action('wp_ajax_reset_current_user_heika_daily_learning_task_content_total', 'lrd_reset_current_user_heika_daily_learning_task_content_total');
error_log('[LearningReviewDebugger] reset_current_user_heika_daily_learning_task_content_total AJAX action已注册');

/**
 * 清除复习信息的AJAX处理函数
 */
function lrd_admin_review_clear_review_mes() {
    error_log('[LearningReviewDebugger] lrd_admin_review_clear_review_mes AJAX请求开始处理');

    // 验证用户权限
    if (!current_user_can('manage_options')) {
        error_log('[LearningReviewDebugger] 权限验证失败 - 用户无管理员权限');
        echo 0;
        wp_die();
    }

    $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
    error_log('[LearningReviewDebugger] 清除复习信息 - 用户ID: ' . $user_id);

    // 验证参数
    if ($user_id <= 0) {
        error_log('[LearningReviewDebugger] 用户ID无效: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 获取用户信息
    $user = get_user_by('id', $user_id);
    if (!$user) {
        error_log('[LearningReviewDebugger] 用户不存在: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 清除复习信息
    $result = update_user_meta($user_id, 'review_mes', '');

    error_log('[LearningReviewDebugger] 复习信息清除结果: ' . ($result ? '成功' : '失败'));

    echo $result ? 1 : 0;
    wp_die();
}
// 注册AJAX处理函数
add_action('wp_ajax_admin_review_clear_review_mes', 'lrd_admin_review_clear_review_mes');
error_log('[LearningReviewDebugger] admin_review_clear_review_mes AJAX action已注册');

/**
 * 获取当前时间戳的AJAX处理函数
 */
function lrd_get_current_timestamp() {
    error_log('[LearningReviewDebugger] lrd_get_current_timestamp AJAX请求开始处理');

    // 使用WordPress的current_time函数获取当前时间戳
    $current_timestamp = current_time("timestamp");

    error_log('[LearningReviewDebugger] 当前时间戳: ' . $current_timestamp . ' (' . date('Y-m-d H:i:s', $current_timestamp) . ')');

    // 返回时间戳
    echo $current_timestamp;
    wp_die();
}
// 注册AJAX处理函数
add_action('wp_ajax_get_current_timestamp', 'lrd_get_current_timestamp');
add_action('wp_ajax_nopriv_get_current_timestamp', 'lrd_get_current_timestamp');
error_log('[LearningReviewDebugger] get_current_timestamp AJAX action已注册');

/**
 * 清理用户所有数据的AJAX处理函数
 */
function lrd_admin_review_clean_user_all_data() {
    error_log('[LearningReviewDebugger] lrd_admin_review_clean_user_all_data AJAX请求开始处理');

    // 验证用户权限
    if (!current_user_can('manage_options')) {
        error_log('[LearningReviewDebugger] 权限验证失败 - 用户无管理员权限');
        echo 0;
        wp_die();
    }

    $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
    error_log('[LearningReviewDebugger] 清理用户所有数据 - 用户ID: ' . $user_id);

    // 验证参数
    if ($user_id <= 0) {
        error_log('[LearningReviewDebugger] 用户ID无效: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 获取用户信息
    $user = get_user_by('id', $user_id);
    if (!$user) {
        error_log('[LearningReviewDebugger] 用户不存在: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 定义要清理的用户元数据字段
    $meta_keys_to_clean = array(
        'review_mes',
        'course_rate_of_progress',
        'current_user_learn_detail',
        'current_user_today_learn_detail',
        'heika_check_in_detail',
        'heika_daily_learning_task_content',
        'heika_daily_learning_task_content_total',
        'alter_review_time',
        'alter_heika_data_time',
        'cat_view_time'
    );

    $cleaned_count = 0;

    // 逐个清理元数据
    foreach ($meta_keys_to_clean as $meta_key) {
        $result = update_user_meta($user_id, $meta_key, '');
        if ($result) {
            $cleaned_count++;
            error_log('[LearningReviewDebugger] 清理字段成功: ' . $meta_key);
        } else {
            error_log('[LearningReviewDebugger] 清理字段失败: ' . $meta_key);
        }
    }

    error_log('[LearningReviewDebugger] 用户数据清理完成 - 成功清理字段数: ' . $cleaned_count . '/' . count($meta_keys_to_clean));

    echo $cleaned_count > 0 ? 1 : 0;
    wp_die();
}
// 注册AJAX处理函数
add_action('wp_ajax_admin_review_clean_user_all_data', 'lrd_admin_review_clean_user_all_data');
add_action('wp_ajax_nopriv_admin_review_clean_user_all_data', 'lrd_admin_review_clean_user_all_data');
error_log('[LearningReviewDebugger] admin_review_clean_user_all_data AJAX action已注册');

/**
 * 重置用户复习信息和课程进度的AJAX处理函数
 */
function lrd_admin_reset_user_review_mes_and_course_rate_of_progress() {
    error_log('[LearningReviewDebugger] lrd_admin_reset_user_review_mes_and_course_rate_of_progress AJAX请求开始处理');

    // 验证用户权限
    if (!current_user_can('manage_options')) {
        error_log('[LearningReviewDebugger] 权限验证失败 - 用户无管理员权限');
        echo 0;
        wp_die();
    }

    $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
    error_log('[LearningReviewDebugger] 重置用户复习信息和课程进度 - 用户ID: ' . $user_id);

    // 验证参数
    if ($user_id <= 0) {
        error_log('[LearningReviewDebugger] 用户ID无效: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 获取用户信息
    $user = get_user_by('id', $user_id);
    if (!$user) {
        error_log('[LearningReviewDebugger] 用户不存在: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 重置复习信息和课程进度
    $result1 = update_user_meta($user_id, 'review_mes', '');
    $result2 = update_user_meta($user_id, 'course_rate_of_progress', '');

    $success = $result1 && $result2;
    error_log('[LearningReviewDebugger] 复习信息重置: ' . ($result1 ? '成功' : '失败'));
    error_log('[LearningReviewDebugger] 课程进度重置: ' . ($result2 ? '成功' : '失败'));

    echo $success ? 1 : 0;
    wp_die();
}
// 注册AJAX处理函数
add_action('wp_ajax_admin_reset_user_review_mes_and_course_rate_of_progress', 'lrd_admin_reset_user_review_mes_and_course_rate_of_progress');
error_log('[LearningReviewDebugger] admin_reset_user_review_mes_and_course_rate_of_progress AJAX action已注册');

error_log('[LearningReviewDebugger] AJAX处理器第五部分加载完成');

?>
