<?php
/*
Plugin Name: Learning Review Debugger
Plugin URI: https://shuimitao.online/
Description: 用于调试和测试学习复习计划系统的工具，独立于主题运行
Version: 2.0.0
Author: showlin
Author URI: https://shuimitao.online/
License: GPL
Text Domain: learning-review-debugger

更新日志：
v2.0.0 - 2025-07-09
- 重大重构：从主题迁移到独立插件
- 添加所有AJAX处理函数，使用lrd_前缀避免冲突
- 完全独立于主题运行，提高稳定性
- 增强日志记录，便于调试和监控
- 保持原有AJAX action名称，确保前端兼容
*/

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

// 记录插件加载
error_log('[LearningReviewDebugger] 插件开始加载 - 版本 2.0.0 - 独立运行');

// 加载AJAX处理函数
// 迁移说明：原本在主题中的AJAX处理函数现在集成到插件内部
// 这样确保功能独立于主题，提高代码的可维护性和稳定性
require_once plugin_dir_path(__FILE__) . 'includes/ajax-handlers.php';
error_log('[LearningReviewDebugger] AJAX处理器文件已加载');

// 加载测试文件（仅在调试模式下）
if (defined('WP_DEBUG') && WP_DEBUG) {
    require_once plugin_dir_path(__FILE__) . 'test-functions.php';
    error_log('[LearningReviewDebugger] 测试文件已加载');
}

add_action('admin_menu', 'lrd_set_menu');

function lrd_set_menu() {
    error_log('[LearningReviewDebugger] 注册管理菜单');
    add_options_page(
        '学习复习调试器',
        '学习复习调试器',
        'administrator',
        'learning-review-debugger-settings',
        'lrd_render_setting_page'
    );
}

function lrd_render_setting_page() {
    error_log('[LearningReviewDebugger] 加载设置页面');
    include('includes/page.php');
}

error_log('[LearningReviewDebugger] 插件加载完成');

?>



