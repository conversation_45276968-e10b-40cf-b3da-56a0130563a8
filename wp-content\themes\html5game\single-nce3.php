<?php
/*

Template Name Posts: nce3

*/


if (isset($_GET['action']) && $_GET['action']) {
    session_start();
    session_unset();
}

pcVisitHandle();

global $post;
$postData = new postData($post);


$userData = new userDataTest();



$reviewMesHandle = new reviewMesHandle($userData->user_id, current_time("timestamp"),$userData->isHeikaVip);//初始化记忆系统对象


$is_course_unvip_listen = ($postData->is_unvip_listen) ? 1 : 0;


$postStatus = $reviewMesHandle->get_now_post_course_status($postData->postId);





if (!empty($_GET['model'])) {
    $learnModel = $_GET['model'];
} else {
    $learnModel = "menu";
}



$postData->distinguishText();//解析lrc获取英文，中文，和时间数组
$postData->distingWordMp3text();
$postData->distingGammarMp3text();
$postData->distinGammarContent();
$postData->distinGammarExplain();
$postData->distinReadKey();
$postData->getenArrNoDot();//获得无标点的英语句子数组
$postData->getenArrHasDot();//获取有标点的英语句子数组

$postData->getWordsArr();


$cat_object = get_the_category();//当前目录
$catData = new catData($cat_object);//分类页对象





$isLesGuidance = get_user_meta($userData->user_id, "isLesGuidance", true);
if (!$isLesGuidance) {
    update_user_meta($userData->user_id, "isLesGuidance", 1);
} else {
    $isLesGuidance = 1;
}


$backToReview=0;
if(isset($_GET['backToReview']) && $_GET['backToReview']){
    $backToReview=1;
    $backUrl = home_url() . "/?page_id=6681";
}else{
    $backUrl = home_url() . "/?cat=" . $catData->catId;
}





$webUrl = home_url();
$anotherAjax=home_url()."/?page_id=13794";

$course_review_ajax_url = $webUrl . get_option("g_course_review_ajax_url");//记忆系统专属通讯地址

$nowTime = current_time("timestamp");

$vipaction_text = get_option("g_vipaction_text");//VIP活动标语
$vipaction_url = get_option("g_vipaction_single_pro_url");//主推商品页






require_once 'includes/single/nce13/darkheader.php';
require_once 'includes/single/nce13/top.php';
?>


<style>

    .loadingPage {
        width: 100%;
        display: block;
    }

    .menuPage {
        width: 100%;
        display: none;

    }

    .playerPage {
        width: 100%;
        display: none;
    }

    .talkingPage {
        width: 100%;
        display: none;
    }



    .wordcardPage {
        width: 100%;
        display: none;
    }

    .gammarPage {
        width: 100%;
        display: none;

    }

    .wordexePage {
        width: 100%;
        display: none;
    }


</style>




<article>
    <?php

    echo '<div class="loadingPage">';
    require_once 'includes/single/nce13/loadingPage.php';
    echo '</div>';

    echo '<div class="menuPage">';
    require_once 'includes/single/nce13/menu.php';
    echo '</div>';

    echo '<div class="playerPage">';
    require_once 'includes/single/nce13/player.php';
    echo '</div>';


    echo '<div class="talkingPage">';
    require_once 'includes/single/nce13/talking.php';
    echo '</div>';

    echo '<div class="wordcardPage">';
    require_once 'includes/single/nce13/wordcard.php';
    echo '</div>';


    echo '<div class="wordexePage">';
    require_once 'includes/single/nce13/wordexe.php';
    echo '</div>';

    echo '<div class="gammarPage">';
    require_once 'includes/single/nce13/gammar.php';
    echo '</div>';

    echo '<div class="myTestPage">';
    require_once 'includes/single/nce13/myTest.php';
    echo '</div>';


    require_once 'includes/single/nce13/senTest.php';
    require_once 'includes/single/nce13/element.php';
    require_once "includes/single/nce13/guidance.php";


    ?>

</article>


<?php



/*微信分享设置
 * */
register_enqueue_script('weixinConfig2', '/js/weixinConfig2.js?z0.8');
$weixin_array = array(
    'postId' => $postData->postId,
    'imgsrc' => $postData->postFirstImg,
    'shareTitle' => $postData->postShareTitle,
    'webUrl' => $webUrl);

wp_enqueue_script('weixinConfig2');
wp_localize_script('weixinConfig2', 'weixin', $weixin_array);



if (!$userData->isLogin) {



    get_login_page();//叫出登录按钮


} else {

    if ($userData->isHeikaVip || $userData->isOldVip || $is_course_unvip_listen) {


        $selectUrl = home_url() . "/?page_id=13794";






        register_enqueue_script('vars', '/js/nce13/vars.js?1.7');
        $vars_array = array(
            "learnModel" => $learnModel,//学习模式
            "selectUrl" => $selectUrl,//字典查询地址
            "backUrl" => $backUrl,//跳转地址
            'webUrl' => $webUrl,//域名地址
            "anotherAjax"=>$anotherAjax,//另一个ajax地址
            "course_review_ajax_url"=>$course_review_ajax_url,//另一个ajax地址

            "fatherCatName"=>$catData->catName,//父目录名称
            "fatherCatId" => $catData->catId,//父目录ID


            "dicLackNum" => $userData->listenLackNum,//缺词
            "isLesGuidance" => $isLesGuidance,//是否引导
            "isLogin" => $userData->isLogin,//是否登录


            "user_id" => $userData->user_id,
            "isHeikaVip" => $userData->isHeikaVip,
            "nowTime" => $nowTime,



            "user_book_word_arr" => $userData->user_book_word,
            "user_book_word_ctrans_arr" => $userData->user_book_word_ctrans,
            "user_book_id" => $userData->user_default_book,
            "user_book_name" => $userData->user_default_book_name,
            'is_user_know_final_test'=>$userData->is_user_know_final_test,//用户是否知道最终测试


            "postStatus" => $postStatus,//当前状态，
            'wordArr' => $postData->wordArr,
            'wordOriArr' => $postData->wordOriArr,
            'wordPhoneticArr' => $postData->wordPhoneticArr,
            'wordSenEnArr' => $postData->wordSenEnArr,
            'wordSenCnArr' => $postData->wordSenCnArr,
            'wordSenEnArrHasDot' => $postData->wordSenEnArrHasDot,
            'wordSenEnArrNoDot' => $postData->wordSenEnArrNoDot,
            'wordCnTrans' => $postData->wordCnTrans,
            'wordEeTrans' => $postData->wordEeTrans,
            'wordExaArr' => $postData->wordExaArr,
            'ajaxUrl' => $postData->ajaxUrl,
            'enArrHasDot' => $postData->enArrHasDot,
            'enArrNoDot' => $postData->enArrNoDot,
            'cnArr' => $postData->cnArr,
            'enArr' => $postData->enArr,
            'audiosrc' => $postData->postMp3src,
            'wordaudiosrc' => $postData->postWordMp3src,
            'wordaudioTimeArr' => $postData->postWordTimeArr,
            'gammaraudiosrc' => $postData->gammarMp3src,
            'gammarMp3TextArr' => $postData->gammarMp3TextArr,
            'gammarSenEnArr' => $postData->gammarSenEnArr,
            'gammarSenCnArr' => $postData->gammarSenCnArr,
            'gammarSenEnArrHasDot' => $postData->gammarSenEnArrHasDot,
            'gammarSenEnArrNoDot' => $postData->gammarSenEnArrNoDot,
            "gammarTimeArr" => $postData->gammarTimeArr,
            "gammarExplainArr" => $postData->gammarExplainArr,
            'isAdmin' => $postData->isAdmin,
            'postId' => $postData->postId,
            'postTitle'=>$postData->postTitle,
            'imgsrc' => $postData->postFirstImg,
            'shareTitle' => $postData->postShareTitle,
            'readKeyArr' => $postData->readKeyArr
        );
        wp_enqueue_script('vars');
        wp_localize_script('vars', 'object', $vars_array);


        register_enqueue_script('menu', '/js/nce13/menu.js?1.2');
        wp_enqueue_script('menu');


        register_enqueue_script('wordcard', '/js/nce13/wordcard.js?1.2');
        wp_enqueue_script('wordcard');

        register_enqueue_script('wordexe', '/js/nce13/wordexe.js?1.2');
        wp_enqueue_script('wordexe');

        register_enqueue_script('myTest', '/js/nce13/myTest.js?1.2');
        wp_enqueue_script('myTest');

        register_enqueue_script('gammar', '/js/nce13/gammar.js?1.2');
        wp_enqueue_script('gammar');

        register_enqueue_script('player', '/js/nce13/player.js?1.2');
        wp_enqueue_script('player');


        register_enqueue_script('senTest', '/js/nce13/senTest.js?1.2');
        wp_enqueue_script('senTest');

        register_enqueue_script('talking', '/js/nce13/talking.js?1.2');
        wp_enqueue_script('talking');

        register_enqueue_script('app', '/js/nce13/app.js?1.4');
        wp_enqueue_script('app');
    } else {
        // 非VIP用户直接跳转到VIP购买页面 - 与single.php和single-20210604.php保持一致
        $redirect_url = home_url() . $vipaction_url;
        ?>
        <script type="text/javascript">
            window.location.href = '<?php echo $redirect_url; ?>';
        </script>
        <?php
        exit; // 确保不执行后续代码
    }
}


get_footer();//底部
?>




