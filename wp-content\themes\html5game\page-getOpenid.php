<?php
/*
Template Name:getOpenid
*/

if (empty($_GET['code']) || empty($_GET['state'])) {
    session_start();
    session_unset();
    header('Location:' . home_url());
    exit();
}



session_start();
$_SESSION['isWechatCheck'] = true;//设置这个session isWechatCheck为ture 说明通过了微信检查

//是否为userinfo
$isUserInfo = false;


//微信公众号APPID和APPSECRET
$wechat = new wechat();

$code = $_GET['code'];
//echo $code;

//通过code获取网页授权access_token
$url = 'https://api.weixin.qq.com/sns/oauth2/access_token?appid=' . $wechat->service_appid . '&secret=' . $wechat->service_appsecret . '&code=' . $code . '&grant_type=authorization_code';
//echo $url;
//curl方法
$res = file_get_contents($url);
$get_access_token_from_code_json_obj = json_decode($res, true);




$weburl = home_url();//网站域名


if (!$get_access_token_from_code_json_obj['errcode']) {
    $old_access = $get_access_token_from_code_json_obj['access_token'];//老的登录access_token
    $refresh_token = $get_access_token_from_code_json_obj['refresh_token'];//刷新的refresh_token

    /*获取刷新新的登录access_token的url*/
    $refresh_access_token_url = "https://api.weixin.qq.com/sns/oauth2/refresh_token?appid=";
    $refresh_access_token_url .= $wechat->service_appid . "&grant_type=refresh_token&refresh_token=" . $refresh_token;

    $refresh_access_token_obj = json_decode(file_get_contents($refresh_access_token_url));//获取新的登录access_token的对象
    //var_dump($refresh_token_obj);
    // var_dump($refresh_token_obj);
    if (!$refresh_access_token_obj->errcode) {

        /*
         * 判断授权类型 是否为snsapi_userinfo或snsapi_base
         * */

        $auth_type = explode(',', $refresh_access_token_obj->scope);
        for ($i = 0; $i < count($auth_type); $i++) {
            if ($auth_type[$i] == 'snsapi_userinfo') {
                $isUserInfo = true;
                break;
            }
        }

        if ($isUserInfo) {


            echo"正在授权登录";

            $nowmal_access_token = get_option("main_wechat_access_token");//微信access_token
            $openid = $refresh_access_token_obj->openid;
            $get_user_info_url = "https://api.weixin.qq.com/cgi-bin/user/info?access_token=";
            $get_user_info_url .= $nowmal_access_token . "&openid=" . $openid . "&lang=zh_CN";
            $user_info_obj = json_decode(file_get_contents($get_user_info_url));


            //myDump($user_info_obj);



            if ($user_info_obj->subscribe != 0) {
                //已关注的情况


                $user_login = $user_info_obj->unionid;//用户账号
                $user_id = get_user_id($user_login);//用户ID

                if ($user_id) {
                    wp_set_current_user($user_id);//设置当前用户
                    wp_set_auth_cookie($user_id);//设置当前cookie
                    do_action('wp_login', $user_login);//do action


                    update_user_meta($user_id, 'user_last_login_time', current_time("timestamp"));//记录登录时间

                    $openid_to_hlyyin = get_user_meta($user_id, "openid_to_hlyyin", true);//hlyyin的openid
                    if (!$openid_to_hlyyin) {
                        update_user_meta($user_id, "openid_to_hlyyin", $openid);//更新hlyyin openid
                    }


                    update_user_meta($user_id, 'user_avatar', $user_info_obj->headimgurl);//更新头像

                } else {
                    $user_id=auto_register_user($user_info_obj);//自动注册后返回user_id


                    auto_signon($user_login,$user_id);//自动登录

                }
            }else{
                //未关注的情况
                //https://xgn.shuimitao.online/?page_id=71667


                header('Location:' . $weburl."/?page_id=71667");
                exit();





            }













        } else {
            $nowmal_access_token = get_option("main_wechat_access_token");//微信access_token
            $openid = $refresh_access_token_obj->openid;
            $get_user_info_url = "https://api.weixin.qq.com/cgi-bin/user/info?access_token=";
            $get_user_info_url .= $nowmal_access_token . "&openid=" . $openid . "&lang=zh_CN";
            $user_info_obj = json_decode(file_get_contents($get_user_info_url));
            //var_dump($user_info_obj);

            if ($user_info_obj->subscribe != 0) {
                $user_login = $user_info_obj->unionid;//用户账号
                $user_id = get_user_id($user_login);//用户ID

                if ($user_id) {
                    wp_set_current_user($user_id);//设置当前用户
                    wp_set_auth_cookie($user_id);//设置当前cookie
                    do_action('wp_login', $user_login);//do action


                    update_user_meta($user_id, 'user_last_login_time', current_time("timestamp"));//记录登录时间

                    $openid_to_hlyyin = get_user_meta($user_id, "openid_to_hlyyin", true);//hlyyin的openid
                    if (!$openid_to_hlyyin) {
                        update_user_meta($user_id, "openid_to_hlyyin", $openid);//更新hlyyin openid
                    }


                    update_user_meta($user_id, 'user_avatar', $user_info_obj->headimgurl);//更新头像

                } else {
                    $user_id=auto_register_user($user_info_obj);//自动注册后返回user_id


                    auto_signon($user_login,$user_id);//自动登录

                }
            }


        }

    }
}




$state = $_GET['state'];
if ($state != "home") {
    $stateArr = explode('|', $state);
    $web_page_type = $stateArr[0];
    $web_page_id = $stateArr[1];

} else {
    $web_page_type = "home";
    $web_page_id = 0;
}


if ($web_page_type == "post") {
    $go_url = $weburl . '/?p=' . $web_page_id;

} elseif ($web_page_type == "page") {
    $go_url = $weburl . '/?page_id=' . $web_page_id;
} elseif ($web_page_type == "cat") {
    $go_url = $weburl . '/?cat=' . $web_page_id;

} else {
    $go_url = $weburl . "/";

}

header('Location:' . $go_url);
exit();





