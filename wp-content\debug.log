[08-Jul-2025 08:04:16 UTC] [ThemeFunctions] [初始化] 开始加载HTML5Game主题functions.php
[08-Jul-2025 08:04:16 UTC] [ThemeFunctions] [配置] 有赞商城URL配置完成
[08-Jul-2025 08:04:16 UTC] [ThemeFunctions] [类加载] 开始加载核心类文件
[08-Jul-2025 08:04:16 UTC] [ThemeFunctions] [类加载] wechat.class.php 加载完成
[08-Jul-2025 08:04:16 UTC] [ThemeFunctions] [类加载] courseData.class.php 加载完成
[08-Jul-2025 08:04:16 UTC] [ThemeFunctions] [类加载] postData.class.php 加载完成
[08-Jul-2025 08:04:16 UTC] [ThemeFunctions] [类加载] userData.class.php 加载完成
[08-Jul-2025 08:04:16 UTC] [ThemeFunctions] [类加载] userDataTest.class.php 加载完成
[08-Jul-2025 08:04:16 UTC] [ThemeFunctions] [类加载] userTotalData.class.php 加载完成
[08-Jul-2025 08:04:16 UTC] [ThemeFunctions] [类加载] reviewMesHandle.class.php 加载完成
[08-Jul-2025 08:04:16 UTC] [ThemeFunctions] [类加载] reviewMesHandleTest.class.php 加载完成
[08-Jul-2025 08:04:16 UTC] [ThemeFunctions] [类加载] heikaHandle.class.php 加载完成
[08-Jul-2025 08:04:16 UTC] [ThemeFunctions] [类加载] catData.class.php 加载完成
[08-Jul-2025 08:04:16 UTC] [ThemeFunctions] [扩展加载] 开始加载主题扩展功能
[08-Jul-2025 08:04:16 UTC] [ThemeFunctions] [扩展加载] theme_options.php 加载完成
[08-Jul-2025 08:04:16 UTC] [ThemeFunctions] [扩展加载] simple-term-meta.php 加载完成
[08-Jul-2025 08:04:16 UTC] [ThemeFunctions] [扩展加载] post_field_fun.php 加载完成
[08-Jul-2025 08:04:16 UTC] [ThemeFunctions] [扩展加载] cat_field_fun.php 加载完成
[08-Jul-2025 08:04:16 UTC] [ThemeFunctions] [扩展加载] ajax.php 加载完成
[08-Jul-2025 08:04:16 UTC] [ThemeFunctions] [API加载] AipSpeech.php (百度语音API) 文件缺失，已跳过加载
[08-Jul-2025 08:04:16 UTC] [ThemeFunctions] [定时任务] 定时任务模块已启用，微信access_token将每10分钟自动刷新
[08-Jul-2025 08:04:16 UTC] [ThemeFunctions] [初始化] 所有核心文件加载完成，开始定义工具函数
[08-Jul-2025 08:04:16 UTC] [ThemeFunctions] [WordPress设置] 已隐藏前端管理工具栏
[08-Jul-2025 08:04:16 UTC] [ThemeFunctions] [WordPress钩子] 访问计数功能已注册到wp_head
[08-Jul-2025 08:04:16 UTC] [ThemeFunctions] [WordPress过滤器] Cookie过期时间自定义功能已注册
[08-Jul-2025 08:04:16 UTC] [DomainDebug] ========== WordPress域名选项检查 ==========
[08-Jul-2025 08:04:16 UTC] [DomainDebug] siteurl: https://xgn.shuimitao.online
[08-Jul-2025 08:04:16 UTC] [DomainDebug] home: https://xgn.shuimitao.online
[08-Jul-2025 08:04:16 UTC] [DomainDebug] upload_url_path: (空值)
[08-Jul-2025 08:04:16 UTC] [DomainDebug] upload_path: (空值)
[08-Jul-2025 08:04:16 UTC] [DomainDebug] ========================================
[08-Jul-2025 08:04:16 UTC] [CustomCategoryTemplates] [初始化] 前台环境，注册模板过滤器
[08-Jul-2025 08:04:16 UTC] [ThemeFunctions] [功能模块] 自定义分类模板功能已启用 (整合自Custom Category Templates插件)
[08-Jul-2025 08:04:16 UTC] [CustomPostTemplates] [初始化] 开始注册自定义文章模板钩子
[08-Jul-2025 08:04:16 UTC] [CustomPostTemplates] [钩子注册] 文章模板相关钩子已注册完成
[08-Jul-2025 08:04:16 UTC] [ThemeFunctions] [功能模块] 自定义文章模板功能已启用 (整合自Custom Post Templates插件)
[08-Jul-2025 08:04:16 UTC] 微信支付订单插件 - [证书工具] 警告：平台证书保存路径设置成功，但保存到数据库选项失败或未改变 (值未改变)：/www/wwwroot/shuimitao.online/wp-content/uploads/wechat-certs/wechatpay_platform_certs.pem
[08-Jul-2025 08:04:17 UTC] [MobileDetector] [步骤 1] 检测User-Agent: mozilla/5.0 (iphone; cpu iphone os 15_0 like mac os x) applewebkit/605.1.15 (khtml, like gecko) vers...
[08-Jul-2025 08:04:17 UTC] [MobileDetector] [步骤 2] 设备检测结果 - PC:N Mac:Y iPhone:Y Android:N iPad:N
[08-Jul-2025 08:04:17 UTC] [MobileDetector] [结果] 检测为Mac设备（视为移动）
[08-Jul-2025 08:04:17 UTC] [PCVisitHandler] [步骤 1] 访问检测 - 移动设备:Y 编辑权限:N
[08-Jul-2025 08:04:17 UTC] [PCVisitHandler] [结果] 访问检查通过，允许继续访问
[08-Jul-2025 08:04:17 UTC] [WeixinDetector] [检测] 用户使用微信浏览器访问
[08-Jul-2025 08:04:17 UTC] [WechatAuthURL] [步骤 1] 开始生成微信授权登录链接，用户来源: 
[08-Jul-2025 08:04:17 UTC] [WechatAuthURL] [步骤 1.4] 检测到首页或其他页面类型，使用默认state: home
[08-Jul-2025 08:04:17 UTC] [WechatAuthURL] [步骤 2] 设置授权作用域: snsapi_userinfo
[08-Jul-2025 08:04:17 UTC] [WechatAuthURL] [步骤 3] 构造回调URL: https://xgn.shuimitao.online/?page_id=104465
[08-Jul-2025 08:04:17 UTC] [WechatAuthURL] [步骤 4] URL编码后的回调地址: https%3A%2F%2Fxgn.shuimitao.online%2F%3Fpage_id%3D104465
[08-Jul-2025 08:04:17 UTC] [WechatAuthURL] [步骤 5] 生成完整授权链接: https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxe25d1a22ac376189&redirect_uri=https%3A%2F%2Fxgn.shuimitao.online%2F%3Fpage_id%3D104465&response_type=code&scope=snsapi_userinfo&state=home#wechat_redirect
[08-Jul-2025 08:04:17 UTC] [WechatAuthURL] [完成] 微信授权链接生成成功
[08-Jul-2025 08:04:22 UTC] [ThemeFunctions] [初始化] 开始加载HTML5Game主题functions.php
[08-Jul-2025 08:04:22 UTC] [ThemeFunctions] [配置] 有赞商城URL配置完成
[08-Jul-2025 08:04:22 UTC] [ThemeFunctions] [类加载] 开始加载核心类文件
[08-Jul-2025 08:04:22 UTC] [ThemeFunctions] [类加载] wechat.class.php 加载完成
[08-Jul-2025 08:04:22 UTC] [ThemeFunctions] [类加载] courseData.class.php 加载完成
[08-Jul-2025 08:04:22 UTC] [ThemeFunctions] [类加载] postData.class.php 加载完成
[08-Jul-2025 08:04:22 UTC] [ThemeFunctions] [类加载] userData.class.php 加载完成
[08-Jul-2025 08:04:22 UTC] [ThemeFunctions] [类加载] userDataTest.class.php 加载完成
[08-Jul-2025 08:04:22 UTC] [ThemeFunctions] [类加载] userTotalData.class.php 加载完成
[08-Jul-2025 08:04:22 UTC] [ThemeFunctions] [类加载] reviewMesHandle.class.php 加载完成
[08-Jul-2025 08:04:22 UTC] [ThemeFunctions] [类加载] reviewMesHandleTest.class.php 加载完成
[08-Jul-2025 08:04:22 UTC] [ThemeFunctions] [类加载] heikaHandle.class.php 加载完成
[08-Jul-2025 08:04:22 UTC] [ThemeFunctions] [类加载] catData.class.php 加载完成
[08-Jul-2025 08:04:22 UTC] [ThemeFunctions] [扩展加载] 开始加载主题扩展功能
[08-Jul-2025 08:04:22 UTC] [ThemeFunctions] [扩展加载] theme_options.php 加载完成
[08-Jul-2025 08:04:22 UTC] [ThemeFunctions] [扩展加载] simple-term-meta.php 加载完成
[08-Jul-2025 08:04:22 UTC] [ThemeFunctions] [扩展加载] post_field_fun.php 加载完成
[08-Jul-2025 08:04:22 UTC] [ThemeFunctions] [扩展加载] cat_field_fun.php 加载完成
[08-Jul-2025 08:04:22 UTC] [ThemeFunctions] [扩展加载] ajax.php 加载完成
[08-Jul-2025 08:04:22 UTC] [ThemeFunctions] [API加载] AipSpeech.php (百度语音API) 文件缺失，已跳过加载
[08-Jul-2025 08:04:22 UTC] [ThemeFunctions] [定时任务] 定时任务模块已启用，微信access_token将每10分钟自动刷新
[08-Jul-2025 08:04:22 UTC] [ThemeFunctions] [初始化] 所有核心文件加载完成，开始定义工具函数
[08-Jul-2025 08:04:22 UTC] [ThemeFunctions] [WordPress设置] 已隐藏前端管理工具栏
[08-Jul-2025 08:04:22 UTC] [ThemeFunctions] [WordPress钩子] 访问计数功能已注册到wp_head
[08-Jul-2025 08:04:22 UTC] [ThemeFunctions] [WordPress过滤器] Cookie过期时间自定义功能已注册
[08-Jul-2025 08:04:22 UTC] [DomainDebug] ========== WordPress域名选项检查 ==========
[08-Jul-2025 08:04:22 UTC] [DomainDebug] siteurl: https://xgn.shuimitao.online
[08-Jul-2025 08:04:22 UTC] [DomainDebug] home: https://xgn.shuimitao.online
[08-Jul-2025 08:04:22 UTC] [DomainDebug] upload_url_path: (空值)
[08-Jul-2025 08:04:22 UTC] [DomainDebug] upload_path: (空值)
[08-Jul-2025 08:04:22 UTC] [DomainDebug] ========================================
[08-Jul-2025 08:04:22 UTC] [CustomCategoryTemplates] [初始化] 前台环境，注册模板过滤器
[08-Jul-2025 08:04:22 UTC] [ThemeFunctions] [功能模块] 自定义分类模板功能已启用 (整合自Custom Category Templates插件)
[08-Jul-2025 08:04:22 UTC] [CustomPostTemplates] [初始化] 开始注册自定义文章模板钩子
[08-Jul-2025 08:04:22 UTC] [CustomPostTemplates] [钩子注册] 文章模板相关钩子已注册完成
[08-Jul-2025 08:04:22 UTC] [ThemeFunctions] [功能模块] 自定义文章模板功能已启用 (整合自Custom Post Templates插件)
[08-Jul-2025 08:04:22 UTC] 微信支付订单插件 - [证书工具] 警告：平台证书保存路径设置成功，但保存到数据库选项失败或未改变 (值未改变)：/www/wwwroot/shuimitao.online/wp-content/uploads/wechat-certs/wechatpay_platform_certs.pem
[08-Jul-2025 08:04:23 UTC] [ThemeFunctions] [初始化] 开始加载HTML5Game主题functions.php
[08-Jul-2025 08:04:23 UTC] [ThemeFunctions] [配置] 有赞商城URL配置完成
[08-Jul-2025 08:04:23 UTC] [ThemeFunctions] [类加载] 开始加载核心类文件
[08-Jul-2025 08:04:23 UTC] [ThemeFunctions] [类加载] wechat.class.php 加载完成
[08-Jul-2025 08:04:23 UTC] [ThemeFunctions] [类加载] courseData.class.php 加载完成
[08-Jul-2025 08:04:23 UTC] [ThemeFunctions] [类加载] postData.class.php 加载完成
[08-Jul-2025 08:04:23 UTC] [ThemeFunctions] [类加载] userData.class.php 加载完成
[08-Jul-2025 08:04:23 UTC] [ThemeFunctions] [类加载] userDataTest.class.php 加载完成
[08-Jul-2025 08:04:23 UTC] [ThemeFunctions] [类加载] userTotalData.class.php 加载完成
[08-Jul-2025 08:04:23 UTC] [ThemeFunctions] [类加载] reviewMesHandle.class.php 加载完成
[08-Jul-2025 08:04:23 UTC] [ThemeFunctions] [类加载] reviewMesHandleTest.class.php 加载完成
[08-Jul-2025 08:04:23 UTC] [ThemeFunctions] [类加载] heikaHandle.class.php 加载完成
[08-Jul-2025 08:04:23 UTC] [ThemeFunctions] [类加载] catData.class.php 加载完成
[08-Jul-2025 08:04:23 UTC] [ThemeFunctions] [扩展加载] 开始加载主题扩展功能
[08-Jul-2025 08:04:23 UTC] [ThemeFunctions] [扩展加载] theme_options.php 加载完成
[08-Jul-2025 08:04:23 UTC] [ThemeFunctions] [扩展加载] simple-term-meta.php 加载完成
[08-Jul-2025 08:04:23 UTC] [ThemeFunctions] [扩展加载] post_field_fun.php 加载完成
[08-Jul-2025 08:04:23 UTC] [ThemeFunctions] [扩展加载] cat_field_fun.php 加载完成
[08-Jul-2025 08:04:23 UTC] [ThemeFunctions] [扩展加载] ajax.php 加载完成
[08-Jul-2025 08:04:23 UTC] [ThemeFunctions] [API加载] AipSpeech.php (百度语音API) 文件缺失，已跳过加载
[08-Jul-2025 08:04:23 UTC] [ThemeFunctions] [定时任务] 定时任务模块已启用，微信access_token将每10分钟自动刷新
[08-Jul-2025 08:04:23 UTC] [ThemeFunctions] [初始化] 所有核心文件加载完成，开始定义工具函数
[08-Jul-2025 08:04:23 UTC] [ThemeFunctions] [WordPress设置] 已隐藏前端管理工具栏
[08-Jul-2025 08:04:23 UTC] [ThemeFunctions] [WordPress钩子] 访问计数功能已注册到wp_head
[08-Jul-2025 08:04:23 UTC] [ThemeFunctions] [WordPress过滤器] Cookie过期时间自定义功能已注册
[08-Jul-2025 08:04:23 UTC] [DomainDebug] ========== WordPress域名选项检查 ==========
[08-Jul-2025 08:04:23 UTC] [DomainDebug] siteurl: https://xgn.shuimitao.online
[08-Jul-2025 08:04:23 UTC] [DomainDebug] home: https://xgn.shuimitao.online
[08-Jul-2025 08:04:23 UTC] [DomainDebug] upload_url_path: (空值)
[08-Jul-2025 08:04:23 UTC] [DomainDebug] upload_path: (空值)
[08-Jul-2025 08:04:23 UTC] [DomainDebug] ========================================
[08-Jul-2025 08:04:23 UTC] [CustomCategoryTemplates] [初始化] 前台环境，注册模板过滤器
[08-Jul-2025 08:04:23 UTC] [ThemeFunctions] [功能模块] 自定义分类模板功能已启用 (整合自Custom Category Templates插件)
[08-Jul-2025 08:04:23 UTC] [CustomPostTemplates] [初始化] 开始注册自定义文章模板钩子
[08-Jul-2025 08:04:23 UTC] [CustomPostTemplates] [钩子注册] 文章模板相关钩子已注册完成
[08-Jul-2025 08:04:23 UTC] [ThemeFunctions] [功能模块] 自定义文章模板功能已启用 (整合自Custom Post Templates插件)
[08-Jul-2025 08:04:23 UTC] 微信支付订单插件 - [证书工具] 警告：平台证书保存路径设置成功，但保存到数据库选项失败或未改变 (值未改变)：/www/wwwroot/shuimitao.online/wp-content/uploads/wechat-certs/wechatpay_platform_certs.pem
[08-Jul-2025 08:04:23 UTC] [WechatLoginRegistration] [开始] 微信登录注册页面处理开始
[08-Jul-2025 08:04:23 UTC] [WechatLoginRegistration] [步骤 1] 参数验证通过 - code: 021Nv50w3O2de534hO0w3yUaEt0Nv504, state: home
[08-Jul-2025 08:04:23 UTC] [WechatLoginRegistration] [步骤 2] 基础配置获取完成 - 网站域名: https://xgn.shuimitao.online, state: home
[08-Jul-2025 08:04:23 UTC] [WechatLoginRegistration] [步骤 2.1] 检测到首页访问，无需解析state参数
[08-Jul-2025 08:04:23 UTC] [WechatLoginRegistration] [步骤 3] 微信配置获取完成 - AppID: wxe25d1a22ac376189, code: 021Nv50w3O2de534hO0w3yUaEt0Nv504
[08-Jul-2025 08:04:23 UTC] [WechatLoginRegistration] [步骤 3.1] 构造access_token请求URL: https://api.weixin.qq.com/sns/oauth2/access_token?appid=wxe25d1a22ac376189&secret=552f7cbd67e9ba1cb31591eb6932e8ad&code=021Nv50w3O2de534hO0w3yUaEt0Nv504&grant_type=authorization_code
[08-Jul-2025 08:04:23 UTC] [WechatLoginRegistration] [步骤 3.2] access_token API响应: {"access_token":"94_ixOMMwsZzYN4thAkyH4U6sykPYP4XJEX6iZW2D6e5fKCYl4gSHH_Nu9lT077iQr4D8HbOi0teddYMUnLomHQUQ9T1RQ3w9mBoVOrBHD-93k","expires_in":7200,"refresh_token":"94_L9QM3xa1VxthHvibtWDKHe98HwYKdQzSzcBq9GPpVoMaV_ag4SCZ-PwER1SeNxHpWc14ljXxDcfub5hjn_z9MSIRSIZM5KEd53qw42zKfYg","openid":"o2neYjrUkO4uXjmwDlKpymYL3s10","scope":"snsapi_userinfo","unionid":"ofYdIxBV4_kFFzgSam_anszved-U"}
[08-Jul-2025 08:04:23 UTC] PHP Warning:  Undefined array key "errcode" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/page-wechat_login_and_registration.php on line 112
[08-Jul-2025 08:04:23 UTC] [WechatLoginRegistration] [步骤 4] access_token获取成功，无错误代码，开始用户登录注册流程
[08-Jul-2025 08:04:23 UTC] [WechatLoginRegistration] [步骤 5.1] 提取refresh_token: 94_L9QM3xa1VxthHvibtWDKHe98HwYKdQzSzcBq9GPpVoMaV_ag4SCZ-PwER1SeNxHpWc14ljXxDcfub5hjn_z9MSIRSIZM5KEd53qw42zKfYg
[08-Jul-2025 08:04:23 UTC] [WechatLoginRegistration] [步骤 5.2] 构造refresh_token请求URL: https://api.weixin.qq.com/sns/oauth2/refresh_token?appid=wxe25d1a22ac376189&grant_type=refresh_token&refresh_token=94_L9QM3xa1VxthHvibtWDKHe98HwYKdQzSzcBq9GPpVoMaV_ag4SCZ-PwER1SeNxHpWc14ljXxDcfub5hjn_z9MSIRSIZM5KEd53qw42zKfYg
[08-Jul-2025 08:04:24 UTC] [WechatLoginRegistration] [步骤 5.3] refresh_token API响应: {"openid":"o2neYjrUkO4uXjmwDlKpymYL3s10","access_token":"94_ixOMMwsZzYN4thAkyH4U6sykPYP4XJEX6iZW2D6e5fKCYl4gSHH_Nu9lT077iQr4D8HbOi0teddYMUnLomHQUQ9T1RQ3w9mBoVOrBHD-93k","expires_in":7200,"refresh_token":"94_L9QM3xa1VxthHvibtWDKHe98HwYKdQzSzcBq9GPpVoMaV_ag4SCZ-PwER1SeNxHpWc14ljXxDcfub5hjn_z9MSIRSIZM5KEd53qw42zKfYg","scope":"snsapi_userinfo"}
[08-Jul-2025 08:04:24 UTC] PHP Warning:  Undefined property: stdClass::$errcode in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/page-wechat_login_and_registration.php on line 140
[08-Jul-2025 08:04:24 UTC] [WechatLoginRegistration] [步骤 6.1] refresh_token成功，openid: o2neYjrUkO4uXjmwDlKpymYL3s10
[08-Jul-2025 08:04:24 UTC] [WechatLoginRegistration] [步骤 6.2] 构造用户信息请求URL: https://api.weixin.qq.com/cgi-bin/user/info?access_token=93_KHDsvtUPTcS7azMQPT8ADaQ5aij5bHrpXaD68biAgAselC7yQteWNybkyKrGGgaXfyF9eUcv5MEbsQfjjSWQvmaXJxEAeQle57uRtvSuV8ZSxWSSVY5k2IF0PM0JMRbAAAMPU&openid=o2neYjrUkO4uXjmwDlKpymYL3s10&lang=zh_CN
[08-Jul-2025 08:04:24 UTC] PHP Warning:  Undefined property: stdClass::$nickname in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/page-wechat_login_and_registration.php on line 151
[08-Jul-2025 08:04:24 UTC] PHP Warning:  Undefined property: stdClass::$subscribe in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/page-wechat_login_and_registration.php on line 151
[08-Jul-2025 08:04:24 UTC] [WechatLoginRegistration] [步骤 6.3] 用户信息获取完成，用户昵称: , 关注状态: 
[08-Jul-2025 08:04:24 UTC] PHP Warning:  Undefined property: stdClass::$subscribe in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/page-wechat_login_and_registration.php on line 162
[08-Jul-2025 08:04:24 UTC] [WechatLoginRegistration] [步骤 7.1] 用户未关注公众号，跳转到引导关注页面
[08-Jul-2025 08:04:24 UTC] [ThemeFunctions] [初始化] 开始加载HTML5Game主题functions.php
[08-Jul-2025 08:04:24 UTC] [ThemeFunctions] [配置] 有赞商城URL配置完成
[08-Jul-2025 08:04:24 UTC] [ThemeFunctions] [类加载] 开始加载核心类文件
[08-Jul-2025 08:04:24 UTC] [ThemeFunctions] [类加载] wechat.class.php 加载完成
[08-Jul-2025 08:04:24 UTC] [ThemeFunctions] [类加载] courseData.class.php 加载完成
[08-Jul-2025 08:04:24 UTC] [ThemeFunctions] [类加载] postData.class.php 加载完成
[08-Jul-2025 08:04:24 UTC] [ThemeFunctions] [类加载] userData.class.php 加载完成
[08-Jul-2025 08:04:24 UTC] [ThemeFunctions] [类加载] userDataTest.class.php 加载完成
[08-Jul-2025 08:04:24 UTC] [ThemeFunctions] [类加载] userTotalData.class.php 加载完成
[08-Jul-2025 08:04:24 UTC] [ThemeFunctions] [类加载] reviewMesHandle.class.php 加载完成
[08-Jul-2025 08:04:24 UTC] [ThemeFunctions] [类加载] reviewMesHandleTest.class.php 加载完成
[08-Jul-2025 08:04:24 UTC] [ThemeFunctions] [类加载] heikaHandle.class.php 加载完成
[08-Jul-2025 08:04:24 UTC] [ThemeFunctions] [类加载] catData.class.php 加载完成
[08-Jul-2025 08:04:24 UTC] [ThemeFunctions] [扩展加载] 开始加载主题扩展功能
[08-Jul-2025 08:04:24 UTC] [ThemeFunctions] [扩展加载] theme_options.php 加载完成
[08-Jul-2025 08:04:24 UTC] [ThemeFunctions] [扩展加载] simple-term-meta.php 加载完成
[08-Jul-2025 08:04:24 UTC] [ThemeFunctions] [扩展加载] post_field_fun.php 加载完成
[08-Jul-2025 08:04:24 UTC] [ThemeFunctions] [扩展加载] cat_field_fun.php 加载完成
[08-Jul-2025 08:04:24 UTC] [ThemeFunctions] [扩展加载] ajax.php 加载完成
[08-Jul-2025 08:04:24 UTC] [ThemeFunctions] [API加载] AipSpeech.php (百度语音API) 文件缺失，已跳过加载
[08-Jul-2025 08:04:24 UTC] [ThemeFunctions] [定时任务] 定时任务模块已启用，微信access_token将每10分钟自动刷新
[08-Jul-2025 08:04:24 UTC] [ThemeFunctions] [初始化] 所有核心文件加载完成，开始定义工具函数
[08-Jul-2025 08:04:24 UTC] [ThemeFunctions] [WordPress设置] 已隐藏前端管理工具栏
[08-Jul-2025 08:04:24 UTC] [ThemeFunctions] [WordPress钩子] 访问计数功能已注册到wp_head
[08-Jul-2025 08:04:24 UTC] [ThemeFunctions] [WordPress过滤器] Cookie过期时间自定义功能已注册
[08-Jul-2025 08:04:24 UTC] [DomainDebug] ========== WordPress域名选项检查 ==========
[08-Jul-2025 08:04:24 UTC] [DomainDebug] siteurl: https://xgn.shuimitao.online
[08-Jul-2025 08:04:24 UTC] [DomainDebug] home: https://xgn.shuimitao.online
[08-Jul-2025 08:04:24 UTC] [DomainDebug] upload_url_path: (空值)
[08-Jul-2025 08:04:24 UTC] [DomainDebug] upload_path: (空值)
[08-Jul-2025 08:04:24 UTC] [DomainDebug] ========================================
[08-Jul-2025 08:04:24 UTC] [CustomCategoryTemplates] [初始化] 前台环境，注册模板过滤器
[08-Jul-2025 08:04:24 UTC] [ThemeFunctions] [功能模块] 自定义分类模板功能已启用 (整合自Custom Category Templates插件)
[08-Jul-2025 08:04:24 UTC] [CustomPostTemplates] [初始化] 开始注册自定义文章模板钩子
[08-Jul-2025 08:04:24 UTC] [CustomPostTemplates] [钩子注册] 文章模板相关钩子已注册完成
[08-Jul-2025 08:04:24 UTC] [ThemeFunctions] [功能模块] 自定义文章模板功能已启用 (整合自Custom Post Templates插件)
[08-Jul-2025 08:04:24 UTC] 微信支付订单插件 - [证书工具] 警告：平台证书保存路径设置成功，但保存到数据库选项失败或未改变 (值未改变)：/www/wwwroot/shuimitao.online/wp-content/uploads/wechat-certs/wechatpay_platform_certs.pem
[08-Jul-2025 08:04:26 UTC] [ThemeFunctions] [初始化] 开始加载HTML5Game主题functions.php
[08-Jul-2025 08:04:26 UTC] [ThemeFunctions] [配置] 有赞商城URL配置完成
[08-Jul-2025 08:04:26 UTC] [ThemeFunctions] [类加载] 开始加载核心类文件
[08-Jul-2025 08:04:26 UTC] [ThemeFunctions] [类加载] wechat.class.php 加载完成
[08-Jul-2025 08:04:26 UTC] [ThemeFunctions] [类加载] courseData.class.php 加载完成
[08-Jul-2025 08:04:26 UTC] [ThemeFunctions] [类加载] postData.class.php 加载完成
[08-Jul-2025 08:04:26 UTC] [ThemeFunctions] [类加载] userData.class.php 加载完成
[08-Jul-2025 08:04:26 UTC] [ThemeFunctions] [类加载] userDataTest.class.php 加载完成
[08-Jul-2025 08:04:26 UTC] [ThemeFunctions] [类加载] userTotalData.class.php 加载完成
[08-Jul-2025 08:04:26 UTC] [ThemeFunctions] [类加载] reviewMesHandle.class.php 加载完成
[08-Jul-2025 08:04:26 UTC] [ThemeFunctions] [类加载] reviewMesHandleTest.class.php 加载完成
[08-Jul-2025 08:04:26 UTC] [ThemeFunctions] [类加载] heikaHandle.class.php 加载完成
[08-Jul-2025 08:04:26 UTC] [ThemeFunctions] [类加载] catData.class.php 加载完成
[08-Jul-2025 08:04:26 UTC] [ThemeFunctions] [扩展加载] 开始加载主题扩展功能
[08-Jul-2025 08:04:26 UTC] [ThemeFunctions] [扩展加载] theme_options.php 加载完成
[08-Jul-2025 08:04:26 UTC] [ThemeFunctions] [扩展加载] simple-term-meta.php 加载完成
[08-Jul-2025 08:04:26 UTC] [ThemeFunctions] [扩展加载] post_field_fun.php 加载完成
[08-Jul-2025 08:04:26 UTC] [ThemeFunctions] [扩展加载] cat_field_fun.php 加载完成
[08-Jul-2025 08:04:26 UTC] [ThemeFunctions] [扩展加载] ajax.php 加载完成
[08-Jul-2025 08:04:26 UTC] [ThemeFunctions] [API加载] AipSpeech.php (百度语音API) 文件缺失，已跳过加载
[08-Jul-2025 08:04:26 UTC] [ThemeFunctions] [定时任务] 定时任务模块已启用，微信access_token将每10分钟自动刷新
[08-Jul-2025 08:04:26 UTC] [ThemeFunctions] [初始化] 所有核心文件加载完成，开始定义工具函数
[08-Jul-2025 08:04:26 UTC] [ThemeFunctions] [WordPress设置] 已隐藏前端管理工具栏
[08-Jul-2025 08:04:26 UTC] [ThemeFunctions] [WordPress钩子] 访问计数功能已注册到wp_head
[08-Jul-2025 08:04:26 UTC] [ThemeFunctions] [WordPress过滤器] Cookie过期时间自定义功能已注册
[08-Jul-2025 08:04:26 UTC] [DomainDebug] ========== WordPress域名选项检查 ==========
[08-Jul-2025 08:04:26 UTC] [DomainDebug] siteurl: https://xgn.shuimitao.online
[08-Jul-2025 08:04:26 UTC] [DomainDebug] home: https://xgn.shuimitao.online
[08-Jul-2025 08:04:26 UTC] [DomainDebug] upload_url_path: (空值)
[08-Jul-2025 08:04:26 UTC] [DomainDebug] upload_path: (空值)
[08-Jul-2025 08:04:26 UTC] [DomainDebug] ========================================
[08-Jul-2025 08:04:26 UTC] [CustomCategoryTemplates] [初始化] 后台环境，注册分类管理钩子
[08-Jul-2025 08:04:26 UTC] [CustomCategoryTemplates] [钩子注册] 分类表单字段和保存操作钩子已注册
[08-Jul-2025 08:04:26 UTC] [ThemeFunctions] [功能模块] 自定义分类模板功能已启用 (整合自Custom Category Templates插件)
[08-Jul-2025 08:04:26 UTC] [CustomPostTemplates] [初始化] 开始注册自定义文章模板钩子
[08-Jul-2025 08:04:26 UTC] [CustomPostTemplates] [钩子注册] 文章模板相关钩子已注册完成
[08-Jul-2025 08:04:26 UTC] [ThemeFunctions] [功能模块] 自定义文章模板功能已启用 (整合自Custom Post Templates插件)
[08-Jul-2025 08:04:26 UTC] 微信支付订单插件 - [证书工具] 警告：平台证书保存路径设置成功，但保存到数据库选项失败或未改变 (值未改变)：/www/wwwroot/shuimitao.online/wp-content/uploads/wechat-certs/wechatpay_platform_certs.pem
[08-Jul-2025 08:04:26 UTC] [CustomPostTemplates] [后台初始化] 支持的文章类型: post
[08-Jul-2025 08:04:26 UTC] [CustomPostTemplates] [元数据框] 已为文章类型 "post" 添加模板选择框
[08-Jul-2025 08:04:26 UTC] [WxJsSDKSign] [开始] 微信JS-SDK签名生成开始
[08-Jul-2025 08:04:26 UTC] [WxJsSDKSign] [步骤 1] 获取access_token: 93_KHDsvtUPTcS7azMQP...
[08-Jul-2025 08:04:26 UTC] [WxJsSDKSign] [步骤 2] 构造jsapi_ticket请求URL: https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=93_KHDsvtUPTcS7azMQPT8ADaQ5aij5bHrpXaD68biAgAselC7yQteWNybkyKrGGgaXfyF9eUcv5MEbsQfjjSWQvmaXJxEAeQle57uRtvSuV8ZSxWSSVY5k2IF0PM0JMRbAAAMPU&type=jsapi
[08-Jul-2025 08:04:26 UTC] [WxJsSDKSign] [步骤 2] jsapi_ticket API响应: {"errcode":42001,"errmsg":"access_token expired rid: 686cd10a-6319173e-471e850f"}
[08-Jul-2025 08:04:26 UTC] [WxJsSDKSign] [错误] jsapi_ticket获取失败
