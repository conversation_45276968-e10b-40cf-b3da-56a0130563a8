<?php

class userTotalData
{


    public $isLogin = false;
    public $isGuidance = 0;
    public $user_id;
    public $isAdmin = false;
    public $user_login;
    public $user_avatar;
    public $user_nickname;
    public $user_sex;


    public $user_viptime;//vip到期时间
    public $is_viptime_over_fifteen = false;//用户剩余VIP时间是否大于15天
    public $is_viptime_over_thirty = false;//用户剩余VIP时间是否大于30天
    public $is_viptime_over_120 = false;//用户剩余VIP时间是否大于30天
    public $is_viptime_over_100 = false;//用户剩余VIP时间是否大于30天
    public $user_save_viptime;//用户自主保存的VIP时间
    public $user_last_stop_vip_time;//上次停用VIP时间

    public $heika_json = "";//黑卡VIPjson
    public $heika_arr = array();//黑卡VIParr

    public $user_orders_json = "";
    public $user_orders_arr = array();


    public $cash_back_times = 0;


    public $total_learning_days_cash_back_chance = 0;//用户累积学习返现机会
    public $continuously_learning_days_cash_back_chance = 0;//用户连续学习返现机会


    public $isHeikaVip = 0;
    public $isOldVip = 0;
    public $isVip = 0;

    public $vip_mes_arr = array();


    private $is_test_user = false;//是否为测试用户


    public $alter_review_time = 0;//自动更新复习计划时间
    public $todayReviewArr = array();//今天需复习的课程
    public $finishReviewArr = array();//已经完成复习的课程
    public $notTodayReviewArr = array();//不是今天复习的课程
    public $review_now_time = 0;//用户学课或复习时间戳


    private $g_review_num = 0;//每天复习默认数，只给用户显示复习的课程数
    private $g_myTest_fail_num = 0;//测试错误基数，用户旧版本，不在更新
    private $yesterday_review_over_num = 0;//用户复习溢出的数字，今天本来复习10节课，由于复习数的限制，只让用户看到5节课，那么溢出数字就是10-5
    private $review_mes_arr = array();//复习计划数组


    public $heika_daily_learning_task_content = "LEARN_TWO";//日常任务内容


    public $user_cat_view_time = "";//用户访问课程表时间
    public $user_cat_view_arr = array();//数组


    //服务号 appid 与 appsecret
    public $service_appid = '';
    public $service_appsecret = '';


    //服务号接口access_token
    private $service_wechat_access_token = "";

    //支付号 appid 与 appsecret
    public $pay_appid = '';
    public $pay_appsecret = '';


    /*
     *
     * 不再更新和维护参数
     * */


    public $isFirstLearn = false;//是否第一册学习单词本
    public $user_post_info;
    public $user_fav_cat;
    public $user_fav_cat_arr = array();
    public $user_special_pay = 0;
    public $user_level = 0;//用户英语能力
    public $user_tagsArr = array();
    public $user_openid_to_weiyouxiin;
    public $user_sign_time = 0;
    public $user_sign_num = 0;
    public $user_first_log_remind_vip = 0;
    public $user_first_pay = 0;
    public $user_default_book;//默认单词本
    public $user_default_book_name = "";//默认单词本name
    public $user_undefault_book_arr = array();//非默认单词本数组,可供选择作为默认单词本
    public $user_book_word = array();
    public $user_book_word_ctrans = array();
    public $listenLackNum = 10;//逐句听写扣词数
    public $user_share_time = 0;//用户分享页面时间戳
    public $user_share_num = 0;
    public $user_isknow_youzan;
    public $is_user_know_final_test = 0;//用户是否知道综合抽查
    public $user_drawdailyvip_time = 0;//用户领取日常VIP时间戳
    public $user_drawlearnvip_time = 0;//用户领取学课或复习VIP时间戳


    public function __construct($source_of_registered_users = "")
    {

        //$source_of_registered_users 为注册用户来源


        $this->service_appid = get_option("g_weixin_appid");//微信服务号appid
        $this->service_appsecret = get_option("g_weixin_appsecret");//微信服务号secret


        $this->service_wechat_access_token = get_option("main_wechat_access_token");//服务号access_token


        $this->pay_appid = get_option("g_weixin_pay_appid");//微信支付号appid
        $this->pay_appsecret = get_option("g_weixin_pay_appsecret");//微信支付号secret


        $this->checkWexinUserAndSignOn($source_of_registered_users);//微信登录检查

    }


    /*
     *
     * 微信登录检查
     *
     * */


    private function checkWexinUserAndSignOn($source_of_registered_users = "")
    {

        /*
         *
         * 未登录的状态下
         * */
        if (!is_user_logged_in()) {


            /*
             * 微信浏览器状态下
             * */

            if (is_weixin()) {


                $wechat_auth_userinfo_url = $this->wechat_auth_userinfo_url($source_of_registered_users);//微信授权登录链接


                header('Location:' . $wechat_auth_userinfo_url);//跳转至微信登录链接


                exit();


            }

        } else {
            $this->isLogin = true;//已登录
            $this->getUserData();//获取用户信息

            $this->vip_mes_arr = $this->judge_is_vip("hlyyin");//vip信息


        }
    }



    /**
     * @功能概述: 生成微信网页授权登录链接
     *           根据当前页面类型构造不同的state参数，生成符合微信OAuth2.0规范的授权链接
     *           用于实现用户在微信浏览器中的自动注册与登录功能
     *
     * @param {string} $source_of_registered_users - 注册用户来源标识，用于统计和标记用户来源渠道
     *
     * @return {string} 完整的微信授权登录URL，包含所有必需的OAuth2.0参数
     *
     * @执行流程:
     *   1. 根据当前页面类型（文章/页面/分类/首页）生成对应的state参数
     *   2. 构造微信授权回调URL，使用动态域名确保域名匹配
     *   3. 组装完整的微信OAuth2.0授权链接
     *   4. 返回可直接跳转的授权URL
     */
    private function wechat_auth_userinfo_url($source_of_registered_users = '')
    {
        $log_prefix = '[WechatAuthURL] ';
        global $post;

        error_log($log_prefix . '[步骤 1] 开始生成微信授权登录链接，用户来源: ' . $source_of_registered_users);

        $state = ""; // 状态参数，格式："页面类型|页面ID|来源渠道"

        /**
         * @分步说明: 根据当前页面类型生成state参数
         *
         *   1. 页面类型检测与state参数构造
         *       1.1. 检测是否为单篇文章页面，构造 'post|文章ID|来源' 格式
         *       1.2. 检测是否为单独页面，构造 'page|页面ID|来源' 格式
         *       1.3. 检测是否为分类页面，构造 'cat|分类ID|来源' 格式
         *       1.4. 其他情况默认为首页，使用 'home' 作为state参数
         */
        if (is_single()) {
            // 步骤 1.1: 文章页面处理
            $post_ID = $post->ID;
            $state = 'post|' . $post_ID . "|" . $source_of_registered_users;
            error_log($log_prefix . '[步骤 1.1] 检测到文章页面，文章ID: ' . $post_ID . '，state参数: ' . $state);

        } else if (is_page()) {
            // 步骤 1.2: 单独页面处理
            $post_ID = $post->ID;
            $state = 'page|' . $post_ID . "|" . $source_of_registered_users;
            error_log($log_prefix . '[步骤 1.2] 检测到单独页面，页面ID: ' . $post_ID . '，state参数: ' . $state);

        } else if (is_category()) {
            // 步骤 1.3: 分类页面处理
            $cat_object = get_the_category();
            $cat_ID = $cat_object[0]->term_id;
            $state = 'cat|' . $cat_ID . "|" . $source_of_registered_users;
            error_log($log_prefix . '[步骤 1.3] 检测到分类页面，分类ID: ' . $cat_ID . '，state参数: ' . $state);

        } else {
            // 步骤 1.4: 默认首页处理
            $state = 'home';
            error_log($log_prefix . '[步骤 1.4] 检测到首页或其他页面类型，使用默认state: ' . $state);
        }

        // 步骤 2: 设置授权作用域为snsapi_userinfo，获取用户详细信息
        $scope = 'snsapi_userinfo'; // 用于获取用户基本信息，需要用户手动授权
        error_log($log_prefix . '[步骤 2] 设置授权作用域: ' . $scope);

        // 步骤 3: 构造微信授权回调URL，使用动态域名确保与当前域名匹配
        $wechat_login_and_registration_url = home_url() . "/?page_id=104465"; // 微信登录处理页面
        error_log($log_prefix . '[步骤 3] 构造回调URL: ' . $wechat_login_and_registration_url);

        // 步骤 4: URL编码回调地址，符合微信OAuth2.0规范
        $redirect_url = urlencode($wechat_login_and_registration_url);
        error_log($log_prefix . '[步骤 4] URL编码后的回调地址: ' . $redirect_url);

        // 步骤 5: 组装完整的微信OAuth2.0授权链接
        $url = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=';
        $url .= $this->service_appid . '&redirect_uri=';
        $url .= $redirect_url . '&response_type=code&scope=' . $scope . '&state=';
        $url .= $state . '#wechat_redirect'; // 必须包含#wechat_redirect参数

        error_log($log_prefix . '[步骤 5] 生成完整授权链接: ' . $url);
        error_log($log_prefix . '[完成] 微信授权链接生成成功');

        return $url;
    }

    /*
     *
     * 获取用户信息
     * */


    private function getUserData()
    {

        global $current_user;
        //获取该用户Id
        $this->user_id = $current_user->ID;


        //用户登录账号
        $this->user_login = $current_user->user_login;
        //用户信息
        $userArr = get_user_info($this->user_id);
        //用户头像
        $this->user_avatar = $userArr['user_avatar'] ? $userArr['user_avatar'] : '/img/avatar_male.png';
        //用户昵称
        $this->user_nickname = $userArr['user_nickname'] ? $userArr['user_nickname'] : '匿名用户';
        //用户性别


        $this->user_default_book = ($userArr['user_default_book']) ? $userArr['user_default_book'] : 0;
        if ($this->user_default_book) {
            $this->user_default_book_name = get_post($this->user_default_book)->post_title;
        }

        $this->user_post_info = ($userArr['user_post_info']) ? $userArr['user_post_info'] : "";


        $this->user_fav_cat = ($userArr['user_fav_cat']) ? $userArr['user_fav_cat'] : "";//我收藏的分类
        if ($this->user_fav_cat) {
            if (strpos($this->user_fav_cat, "#")) {
                $this->user_fav_cat_arr = explode("#", $this->user_fav_cat);

            } else {
                $this->user_fav_cat_arr = array($this->user_fav_cat);
            }

        }


        $this->cash_back_times = ($userArr['cash_back_times']) ? $userArr['cash_back_times'] : 0;


        //累积返现机会

        $this->total_learning_days_cash_back_chance = ($userArr['total_learning_days_cash_back_chance']) ? $userArr['total_learning_days_cash_back_chance'] : 0;


        //累积返现机会
        $this->continuously_learning_days_cash_back_chance = ($userArr['continuously_learning_days_cash_back_chance']) ? $userArr['continuously_learning_days_cash_back_chance'] : 0;


        $this->user_openid_to_weiyouxiin = ($userArr['user_openid_to_weiyouxiin']) ? $userArr['user_openid_to_weiyouxiin'] : "";
        $this->user_sign_time = ($userArr['user_sign_time']) ? $userArr['user_sign_time'] : 0;
        $this->user_sign_num = ($userArr['user_sign_num']) ? $userArr['user_sign_num'] : 0;
        $this->user_first_log_remind_vip = ($userArr['user_first_log_remind_vip']) ? $userArr['user_first_log_remind_vip'] : 0;
        $this->user_special_pay = ($userArr['user_special_pay']) ? $userArr['user_special_pay'] : 0;
        $this->user_first_pay = ($userArr['first_pay']) ? $userArr['first_pay'] : 0;


        $this->user_share_num = ($userArr['user_share_num']) ? $userArr['user_share_num'] : 0;//用户连续分享天数

        $this->user_share_time = ($userArr['user_share_time']) ? $userArr['user_share_time'] : 0;//用户分享页面时间戳

        $this->user_drawdailyvip_time = ($userArr['user_drawdailyvip_time']) ? $userArr['user_drawdailyvip_time'] : 0;//用户领取日常VIP时间戳

        $this->review_now_time = ($userArr['review_now_time']) ? $userArr['review_now_time'] : 0;//用户学课或复习时间戳

        $this->user_drawlearnvip_time = ($userArr['user_drawlearnvip_time']) ? $userArr['user_drawlearnvip_time'] : 0;//用户领取学课或复习VIP时间戳

        $this->alter_review_time = ($userArr['alter_review_time']) ? $userArr['alter_review_time'] : 0;//用户复习计划处理时间

        $this->yesterday_review_over_num = ($userArr['today_review_over_num']) ? $userArr['today_review_over_num'] : 0;//用户复习溢出的数字，每日统计

        $this->user_isknow_youzan = ($userArr['user_isknow_youzan']) ? $userArr['user_isknow_youzan'] : 0;//用户是否知道有赞


        $this->user_viptime = ($userArr['user_viptime']) ? $userArr['user_viptime'] : 0;//用户VIP时间

        $this->user_save_viptime = ($userArr['user_save_viptime']) ? $userArr['user_save_viptime'] : 0;//用户存储VIP时间

        $this->user_last_stop_vip_time = ($userArr['user_last_stop_vip_time']) ? $userArr['user_last_stop_vip_time'] : 0;//用户上次暂停VIP时间

        $this->is_user_know_final_test = ($userArr['is_user_know_final_test']) ? $userArr['is_user_know_final_test'] : 0;//用户是否知道综合抽查


        $this->user_cat_view_time = ($userArr['cat_view_time']) ? $userArr['cat_view_time'] : 0;//用户课程目录访问时间 json


        $this->user_cat_view_arr = json_decode($this->user_cat_view_time);//用户课程目录访问时间 数组


        $this->heika_json = ($userArr['heika_vip']) ? $userArr['heika_vip'] : "[]";//用户黑卡VIP数据
        $this->heika_arr = json_decode($this->heika_json);//用户黑卡VIP数组


        $this->user_orders_json = ($userArr['user_orders']) ? $userArr['user_orders'] : "[]";//用户黑卡VIP数据
        $this->user_orders_arr = json_decode($this->user_orders_json);//用户黑卡VIP数组

        //$card_type, $total_vip_days, $big_course_type, $specific_course, $telephone_number

        //$this->heika_card_type=$this->heika_arr->card_type;//会员卡类型
        //$this->heika_can_use_expiration_date=$this->heika_arr->can_use_expiration_date;//会员卡类型
        // $this->heika_big_course_type=$this->heika_arr->big_course_type;//大课程分类
        //$this->heika_specific_course=$this->heika_arr->specific_course;//具体课程


        //已停止更新

        $isGuidance = ($userArr['isGuidance']) ? $userArr['isGuidance'] : 0;
        $this->isGuidance = $isGuidance ? $isGuidance : 0;
        if (!$isGuidance) {
            update_user_meta($this->user_id, "isGuidance", 1);

        }


        $this->get_book_word_arr();

        $this->get_user_undefault_book_arr();


    }


    /*
    * 判断是否VIP用户
     *
     * 返回数组
    * */


    public function judge_is_vip($page_big_course_type)
    {


        if (count($this->heika_arr) > 0) {
            //有黑卡会员数据


            foreach ($this->heika_arr as $arr) {

                if ($arr->big_course_type == $page_big_course_type) {

                    if ($arr->card_type == "FOREVER") {
                        //永久VIP


                        $this->isHeikaVip = 1;
                        $this->isOldVip = 0;
                        $this->isVip = 1;

                        $return_vip_arr = array(

                            "system" => "NEW",
                            "card_type" => "FOREVER",
                            "can_use_expiration_date" => 0
                        );

                        return $return_vip_arr;

                    } else {

                        //非永久则检查时间

                        if ($arr->can_use_expiration_date > current_time("timestamp")) {

                            $this->isOldVip = 0;
                            $this->isHeikaVip = 1;
                            $this->isVip = 1;

                            $return_vip_arr = array(

                                "system" => "NEW",
                                "card_type" => "HAVE_DATE",
                                "can_use_expiration_date" => $arr->can_use_expiration_date
                            );

                            return $return_vip_arr;

                        }

                    }


                    break;

                }


            }


        } else {


            if ($this->user_viptime > current_time("timestamp")) {


                $this->isOldVip = 1;
                $this->isHeikaVip = 0;
                $this->isVip = 1;

                $rest_vip_time = $this->user_viptime - current_time("timestamp");


                if ($rest_vip_time > 15 * 24 * 3600) {
                    $this->is_viptime_over_fifteen = true;//用户VIP天数是不是大于15
                }


                if ($rest_vip_time > 30 * 24 * 3600) {
                    $this->is_viptime_over_thirty = true;//用户VIP天数是不是大于30
                }


                if ($rest_vip_time > 120 * 24 * 3600) {
                    $this->is_viptime_over_120 = true;//用户VIP天数是不是大于120
                }


                $return_vip_arr = array(

                    "system" => "OLD",//系统类型
                    "card_type" => "HAVE_DATE",//会员卡类型
                    "can_use_expiration_date" => $this->user_viptime
                );


                return $return_vip_arr;//返回一个时间

            }


        }


        return false;


    }


    /*
     * 判断是否为测试用户
     * */

    public function judge_is_test_user()
    {
        $userStr = get_option("g_test_user", true);//获取测试用户字符串
        $userArr = explode("#", $userStr);//把测试用户字符串分割为测试用户数组
        foreach ($userArr as $user) {//循环判断
            if ($user == $this->user_id) {
                $this->is_test_user = true;
                break;
            }
        }
        return $this->is_test_user;
    }


    /*
     *
     * 处理用户复习信息
     * */


    /*
     * 获取用户在课程的最大翻页
     *
     * */

    public function get_user_course_rate_of_progress($post_id)
    {


        $course_rate_of_progress_json = get_user_meta($this->user_id, "course_rate_of_progress", true);//获取复习信息json
        //myDump($course_rate_of_progress_json);

        if (!$course_rate_of_progress_json) {
            //课程进度为空


        } else {


            $course_rate_of_progress_arr = json_decode($course_rate_of_progress_json);//解析json

            for ($i = 0; $i < count($course_rate_of_progress_arr); $i++) {
                if ($post_id == $course_rate_of_progress_arr[$i]->post_id) {
                    //找到课程
                    return $course_rate_of_progress_arr[$i]->maxpaged;

                }
            }

        }

        return 0;

    }


    /*
     *
     * 不再更新的方法
     *
     * */


    public function calcuUserLevel($catDiffNum)
    {
        $levelJson = get_user_meta($this->user_id, "user_level", true);
        if (!$levelJson) {
            $newLevelArr = array($catDiffNum);
            $newLevelJson = json_encode($newLevelArr);
            $this->user_level = $catDiffNum;
            return (update_user_meta($this->user_id, "user_level", $newLevelJson)) ? 1 : 0;
        } else {
            $levelArr = json_decode($levelJson);
            if (count($levelArr) < 10) {
                array_push($levelArr, $catDiffNum);//数组元素少于10个，将难度压入数组
                $avg = array_sum($levelArr) / count($levelArr);
                $this->user_level = round((float)number_format($avg, 2, '.', ''));
                $newLevelJson = json_encode($levelArr);
                return (update_user_meta($this->user_id, "user_level", $newLevelJson)) ? 1 : 0;
            } else {
                array_splice($levelArr, 0, 1);
                array_push($levelArr, $catDiffNum);//数组元素少于10个，将难度压入数组
                $avg = array_sum($levelArr) / count($levelArr);
                $this->user_level = round((float)number_format($avg, 2, '.', ''));
                $newLevelJson = json_encode($levelArr);
                return (update_user_meta($this->user_id, "user_level", $newLevelJson)) ? 1 : 0;
            }
        }
    }

    public function getUserLevel()
    {
        $levelJson = get_user_meta($this->user_id, "user_level", true);
        if ($levelJson) {
            $levelArr = json_decode($levelJson);
            $avg = array_sum($levelArr) / count($levelArr);
            $this->user_level = round((float)number_format($avg, 2, '.', ''));
        } else {
            $this->user_level = 0;
        }
    }


    public function saveUserTag($postTag)
    {

        if ($postTag) {
            $tagsJson = get_user_meta($this->user_id, "user_tags", true);
            if (!$tagsJson) {
                $newTagsArr = array();
                foreach ($postTag as $tag) {
                    array_push($newTagsArr, urlencode($tag));
                }

                $newTagsJson = urldecode(json_encode($newTagsArr));
                return (update_user_meta($this->user_id, "user_tags", $newTagsJson)) ? 1 : 0;
            } else {
                $tagsArr = json_decode($tagsJson);
                for ($i = 0; $i < count($tagsArr); $i++) {
                    $tagsArr[$i] = urlencode($tagsArr[$i]);
                }
                foreach ($postTag as $tag) {
                    array_push($tagsArr, urlencode($tag));
                }
                if (count($tagsArr) > 20) {
                    $num = count($tagsArr) - 20;
                    array_splice($tagsArr, 0, $num);
                }

                $newTagsJson = urldecode(json_encode($tagsArr));
                return (update_user_meta($this->user_id, "user_tags", $newTagsJson)) ? 1 : 0;
            }
        }
    }

    public function getUserTagsArr()
    {
        $tagsJson = get_user_meta($this->user_id, "user_tags", true);
        if ($tagsJson) {
            $tagsArr = json_decode($tagsJson);
            $tagsArr = array_count_values($tagsArr);
            arsort($tagsArr);
            $this->user_tagsArr = $tagsArr;
        }
    }

    private function get_book_word_arr()
    {
        if ($this->user_default_book) {
            $book_word_str = get_post_meta($this->user_default_book, "new_words_value", true);
            if ($book_word_str) {
                if (strpos($book_word_str, "#")) {
                    $this->user_book_word = explode("#", $book_word_str);

                } else {//有值无#号
                    $this->user_book_word = array($book_word_str);
                }

                for ($i = 0; $i < count($this->user_book_word); $i++) {
                    if (strpos($this->user_book_word[$i], "*")) {//查找有没有*字符,如果有
                        $this->user_book_word[$i] = trim($this->user_book_word[$i]);//去掉两边两边空格
                        $newArr = explode("*", $this->user_book_word[$i]);//依据*分割为数组
                        $jsonObj = json_decode(get_dict_data_only_local($newArr[0]));//获取单词json对象
                        $mostusedkey = $newArr[1];//获取当前释义


                    } else {
                        $this->user_book_word[$i] = trim($this->user_book_word[$i]);//去掉两边两边空格
                        $jsonObj = json_decode(get_dict_data_only_local($this->user_book_word[$i]));//获取单词json对象
                        $mostusedkey = $jsonObj->mostusedkey ? $jsonObj->mostusedkey : 0;//获取当前释义
                    }

                    $explainsStr = $jsonObj->explains ? $jsonObj->explains : "";//释义
                    if (!empty($explainsStr)) {//释义字符串非空
                        if (strpos($explainsStr, "@")) {
                            $explainsArr = explode("@", $explainsStr);//释义字符串分割为数组
                            $explains = ($explainsArr[$mostusedkey]) ? $explainsArr[$mostusedkey] : $explainsArr[0];//用$mostusedkey来确定最常用的那个释义

                        } else {
                            $explains = $explainsStr;
                        }
                        array_push($this->user_book_word_ctrans, $explains);//压入释义组
                    } else {//否则
                        $explains = "";//释义为空
                        array_push($this->user_book_word_ctrans, $explains);//压入释义组
                    }

                }
            }
        }
    }

    private function get_user_undefault_book_arr()
    {
        if ($this->user_default_book) {
            $arg = array(
                "author" => $this->user_id,
                'posts_per_page' => 100,
                'orderby' => 'date',
                'post_status' => 'publish'
            );
            $bookObjArr = get_posts($arg);
            foreach ($bookObjArr as $bookObj) {
                //如果单词本id与默认单词本id相同，抛弃
                if ($bookObj->ID == $this->user_default_book) {
                    continue;
                }

                //如果单词本id模板不是single-myBook,抛弃
                if (get_post_meta($bookObj->ID, "custom_post_template", true) != "single-myBook.php") {
                    continue;
                }


                //如果单词本内的单词数量大于等于50，抛弃
                $book_word_str = get_post_meta($bookObj->ID, "new_words_value", true);
                if ($book_word_str) {
                    $word_num = substr_count($book_word_str, '#') + 1;
                    if ($word_num >= 50) {
                        continue;
                    }
                }

                //单词本在复习计划中,抛弃
                if (isInReview($this->user_id, $bookObj->ID)) {
                    continue;
                }

                $this->user_undefault_book_arr[$bookObj->ID] = $bookObj->post_title;


            }

        }

    }


}


?>