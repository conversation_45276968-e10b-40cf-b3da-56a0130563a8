<?php
/**
 * Category Template: word
 */

// 处理 action 参数
if ($_GET['action']) {
    session_start();
    session_unset();
}

// 初始化用户数据和页面处理
$userData = new userData();
pcVisitHandle();
get_header();

// 获取分类数据
$cat_object = get_the_category();
$catData = new catData($cat_object, 100); // 分类页对象
$catData->getIsUserCollect($userData->user_fav_cat_arr); // 用户是否收藏该分类
$catData->recordUserViewTime($userData->user_id); // 统计用户访问时间
// myDump($catData->isUserNoRead($userData->user_id)); // 用户是否阅读过最新文章

get_top_nav(); // app顶部导航
?>

<style>
    .box_in_cat {
        width: 80%;
        margin-left: 10%;
        height: auto;
    }
    .box_in_cat_left {
        width: 36%;
        height: auto;
        float: left;
    }
    .box_in_cat_left img {
        width: 100%;
    }
    .box_in_cat_right {
        width: 50%;
        float: right;
    }
    .box_in_cat_right p {
        font-size: 0.9em;
        margin-top: 0.6em;
    }
    .box_in_cat_right_title {
        font-size: 1em;
        font-weight: 600;
        margin-top: 1em;
    }
    .word_cat_big_wrapper {
        border-top: 10px solid #e8e8e8;
        width: 100%;
        margin-top: 20px;
        float: left;
    }
    .word_cat_wrapper {
        margin-top: 10px;
        width: 90%;
        float: left;
        margin-left: 5%;
    }
    .word_cat_area {
        width: 33%;
        float: left;
        margin-top: 20px;
    }
    .word_cat_area span {
        display: block;
        background: limegreen;
        color: white;
        width: 3em;
        height: 1.5em;
        line-height: 1.5em;
        text-align: center;
        font-size: 0.5em;
        border-radius: 2px;
        margin-top: 10px;
    }
    .word_cat_lect {
        width: 60px;
        height: 60px;
        color: darkgray;
        border-radius: 5px;
        border: 1px solid darkgray;
        margin: 0px auto;
    }
    .word_cat_number {
        width: 100%;
        height: 34px;
        border-bottom: 1px solid darkgray;
        text-align: center;
        font-size: 22px;
        line-height: 34px;
        font-weight: bold;
    }
    .word_cat_status {
        width: 100%;
        height: 26px;
        text-align: center;
        line-height: 26px;
        font-size: 12px;
    }
</style>

<div class="box_in_cat">
    <div class="box_in_cat_left">
        <img class="book-media-box__thumb" src="<?php echo $catData->catImg; ?>">
    </div>
    <div class="box_in_cat_right">
        <div class="box_in_cat_right_title">
            <span><?php echo $catData->catName; ?></span>
        </div>
        <p class="box_in_cat_right_level">
            难度:
            <?php echo $catData->catDiffValue; ?>
        </p>
        <p>
            状态:
            <?php echo $catData->catUpdateValue; ?>
        </p>
    </div>
</div>

<div class="word_cat_big_wrapper">
    <div class="word_cat_wrapper">
        <?php 
        foreach ($catData->postObjectArr as $post_object) {
            switch ($userData->nowBookStatus($post_object->ID)) {
                case 0:
                    echo '<a href="' . home_url() . '/?p=' . $post_object->ID . '">';
                    echo '<div class="word_cat_area">';
                    echo '<div class="word_cat_lect">';
                    echo '<div class="word_cat_number">';
                    echo findNum(get_post($post_object->ID)->post_title);
                    echo '</div>';
                    if (get_post_meta($post_object->ID, "is_unvip_listen_value", true)) {
                        echo '<div class="word_cat_status">试听</div>';
                    } else {
                        echo '<div class="word_cat_status"></div>';
                    }
                    echo '</div>';
                    echo '</div>';
                    echo '</a>';
                    break;
                    
                case 1:
                    echo '<a href="' . home_url() . '/?p=' . $post_object->ID . '">';
                    echo '<div class="word_cat_area">';
                    echo '<div class="word_cat_lect" style="border: 1px solid red">';
                    echo '<div class="word_cat_number" style="color:red;border-bottom: 1px solid red">';
                    echo findNum(get_post($post_object->ID)->post_title);
                    echo '</div>';
                    echo '<div class="word_cat_status" style="color:red">需复习</div>';
                    echo '</div>';
                    echo '</div>';
                    echo '</a>';
                    break;
                    
                case 2:
                    echo '<a href="' . home_url() . '/?p=' . $post_object->ID . '">';
                    echo '<div class="word_cat_area">';
                    echo '<div class="word_cat_lect" style="border: 1px solid #79B8E3">';
                    echo '<div class="word_cat_number" style="color:#79B8E3;border-bottom: 1px solid #79B8E3">';
                    echo findNum(get_post($post_object->ID)->post_title);
                    echo '</div>';
                    echo '<div class="word_cat_status" style="color:#79B8E3">等待复习</div>';
                    echo '</div>';
                    echo '</div>';
                    echo '</a>';
                    break;
                    
                case 3:
                    echo '<a href="' . home_url() . '/?p=' . $post_object->ID . '">';
                    echo '<div class="word_cat_area">';
                    echo '<div class="word_cat_lect" style="border: 1px solid #08aa00">';
                    echo '<div class="word_cat_number" style="color:#08aa00;border-bottom: 1px solid #08aa00">';
                    echo findNum(get_post($post_object->ID)->post_title);
                    echo '</div>';
                    echo '<div class="word_cat_status" style="color:#08aa00">已完成</div>';
                    echo '</div>';
                    echo '</div>';
                    echo '</a>';
                    break;
                    
                default:
                    break;
            }
        }
        ?>
    </div>
    <div class="bottom_dis">
    </div>
    
    <?php
    if ($userData->isLogin) {
        $ajaxurl = admin_url("admin-ajax.php");
        register_enqueue_script('smallcat', '/js/smallcat.js?v0.5');
        $bigcat_array = array(
            'ajaxUrl' => $ajaxurl,
            'user_id' => $userData->user_id,
            'is_collect' => $catData->isUserCollect
        );
        wp_enqueue_script('smallcat');
        wp_localize_script('smallcat', 'smallcat', $bigcat_array);
    } else {
        $page_we_qr_code = $catData->getCatQrCode(); // 生成页面及收藏按钮
        ?>
        <script type="text/javascript">
            var attentionBtn = $("#attentionBtn")[0];
            attentionBtn.onclick = function(){
                $$showWechatLoginCanClose(<?php echo '"' . $page_we_qr_code . '"'; ?>);
            };
        </script>
        <?php
    }
    
    get_sidebar();
    get_footer();
    ?>