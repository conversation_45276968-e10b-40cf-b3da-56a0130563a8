<?php
/*
Template Name:me
*/




pcVisitHandle();


$userData = new userTotalData("");

// 确保用户头像和昵称有默认值
$webUrl = home_url(); // 网站主域名

// 处理用户头像：如果为空或只是相对路径，使用默认头像
if (empty($userData->user_avatar) || $userData->user_avatar == '/img/avatar_male.png') {
    $userData->user_avatar = $webUrl . '/img/avatar_male.png';
} elseif (strpos($userData->user_avatar, 'http') !== 0) {
    // 如果不是完整URL，补充域名
    $userData->user_avatar = $webUrl . $userData->user_avatar;
}

// 处理用户昵称：如果为空或无效，使用默认昵称
if (empty($userData->user_nickname) || trim($userData->user_nickname) == '' || $userData->user_nickname == '匿名用户') {
    $userData->user_nickname = '匿名用户';
}




$reviewMesHandle = new reviewMesHandle($userData->user_id, current_time("timestamp"), $userData->isHeikaVip);

$heikaHandle = new heikaHandle($userData->user_id);

// 检查用户是否有复习计划数据（用于重置课程记录功能）
$is_have_review_mes = (count($reviewMesHandle->review_mes_arr) > 0) ? 1 : 0;
error_log('[page-me.php] 用户复习计划检查 - 用户ID: ' . $userData->user_id . ', 是否有复习数据: ' . $is_have_review_mes . ', 复习计划数量: ' . count($reviewMesHandle->review_mes_arr));


$is_test_user = $userData->judge_is_test_user();//是否测试用户


/*VIP到期时间
日期格式 $vip_expiration_date_ymd
外部显示VIP信息 $vip_mes_output

*/

$vip_mes_output = "";//用户看到的VIP显示文字

if ($userData->isOldVip || $userData->isHeikaVip) {


    if ($userData->vip_mes_arr["system"] == "OLD") {

        $vip_expiration_date_ymd = date("Y.m.d  H:i", $userData->vip_mes_arr["can_use_expiration_date"]);
        $vip_mes_output = "VIP期限：" . $vip_expiration_date_ymd;


    } else {
        if ($userData->vip_mes_arr["card_type"] == "FOREVER") {

            $vip_expiration_date_ymd = "无限时长";
            $vip_mes_output = "黑卡VIP期限：" . $vip_expiration_date_ymd;

        } else {

            $vip_expiration_date_ymd = date("Y.m.d  H:i", $userData->vip_mes_arr["can_use_expiration_date"]);
            $vip_mes_output = "黑卡VIP期限：" . $vip_expiration_date_ymd;

        }


    }

} else {

    $vip_mes_output = "暂未开通VIP";

}


$nowTime = current_time("timestamp");

$vipaction_text = get_option("g_heika_vip_slogan");//VIP活动标语
$vipaction_url = get_option("g_vipaction_single_pro_url");//主推商品页

$recover_vip_url = home_url() . "/?page_id=54439";






$heika_task_url = home_url() . get_option("g_heika_task_page");//黑卡任务链接



$heika_task_text_arr = $reviewMesHandle->get_heika_tesk_detail();//黑卡任务信息

switch($heika_task_text_arr["task_content"]){

    case "LEARN_TWO":


        $heika_task_content_text = "学习2课";//返回数组

        $learn_num= $heika_task_text_arr["learn_num"];//学习课数

        if($learn_num==0){

            $heika_task_content_completeness = "未完成";//今日任务已完成

        }


        if($learn_num==1){

            $heika_task_content_completeness = "还需学习1课";//今日任务已完成

        }


        if($learn_num>=2){

            $heika_task_content_completeness = "已完成";//今日任务已完成

        }


        break;

    case "LEARN_ONE_REVIEW_ONE":

        $heika_task_content_text = "学习1课、复习1课";//返回数组

        $review_num = $heika_task_text_arr["review_num"];//复习课数

        $learn_num= $heika_task_text_arr["learn_num"];//学习课数



        if ($learn_num >= 1 && $review_num >= 1) {

            $heika_task_content_completeness = "已完成";//今日任务已完成
        } else {


            if ($learn_num >= 1&&$review_num==0) {
                $heika_task_content_completeness = "还需复习1课";//今日任务已完成
            }


            if ($learn_num == 0&&$review_num>=1) {
                $heika_task_content_completeness = "还需学习1课";//今日任务已完成
            }


            if ($learn_num == 0&&$review_num==0) {
                $heika_task_content_completeness = "未完成";//今日任务已完成
            }
        }



        break;

    case "FINISH_ALL_REVIEWING_CONTENT":


        $heika_task_content_text="完成今天所有复习课";

        $last_review_num=$heika_task_text_arr["last_review_num"];//剩余复习数

        if($last_review_num>0){
            $heika_task_content_completeness="还需复习".$last_review_num."课";
        }else{
            $heika_task_content_completeness="已完成";
        }




        break;


    default:
        break;


}









$history_cat_arr = array();//历史访问课程数组


if (!empty($userData->user_cat_view_time)) {

    $cat_view_arr = array();//临时数组，用来排序


    foreach ($userData->user_cat_view_arr as $o) {
        $cat_view_arr[$o->cat_id] = $o->time;
    }

    arsort($cat_view_arr);//按时间戳从高到低排序

    $i = 0;


    foreach ($cat_view_arr as $cat_id => $timestamp) {

        if ($i < 3) {
            $cat_obj = get_category($cat_id);

            //myDump($catObj);
            $cat_img = get_term_meta($cat_id, "imgUpload_value", true);

            $arr = array(
                "timestamp" => $timestamp,
                "cat_id" => $cat_id,
                "name" => urlencode($cat_obj->cat_name),
                "cat_img_url" => $cat_img
            );

            array_push($history_cat_arr, $arr);

        }


        $i++;


    }


}


if (count($history_cat_arr) > 0) {

    $history_cat_json = json_encode($history_cat_arr);
    $history_cat_json = urldecode($history_cat_json);
} else {
    $history_cat_json = "[]";
}


$today_review_url = home_url() . get_option("g_today_review_page");//今日复习url
$my_achieve_url = home_url() . get_option("g_my_achieve_page");//我的成就url
$setting_url = home_url() . get_option("g_setting_page");//我的设置url


//myDump($history_cat_json);



$today_review_num=count($reviewMesHandle->todayReviewArr);//今日复习数

$today_review_red_point_url=home_url()."/img/redpoint.png";//今日复习旁的小数点


$ajax_url = admin_url("admin-ajax.php");


get_header();


if ($userData->isLogin) {


    ?>


    <style>
        [v-cloak] {
            display: none;
        }

        #container {
            width: 100%;
        }

        .cat_head {
            width: 100%;
            height: 150px;
            background-color: #F74655;

            color: white;

        }

        .cat_head div {
            float: left;

        }

        .my_img {
            width: 25%;
            height: 100px;

        }

        .my_img img {
            width: 80%;
            max-height: 80px;
            margin: 10px 10%;

        }

        .my_text {
            width: 65%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

        }

        .my_text > div:first-child {
            margin-top: 15px;
            width: 100%;
            height: 25px;
            font-size: 14px;
            line-height: 25px;
            font-weight: 500;
        }

        .my_text > div:nth-child(2) {
            width: 100%;
            height: 20px;
            font-size: 14px;
            line-height: 20px;

        }

        .my_text > div:nth-child(3) {
            width: 100%;
            height: 20px;
            font-size: 14px;
            line-height: 20px;

        }

        .test_show {
            position: fixed;
            background: darkgoldenrod;
            opacity: 0.7;
            color: white;
            left: 10px;
            bottom: 0px;
            width: 300px;
            height: 150px;

        }

        .vipBtn {
            margin-top: 1px;
            width: 100%;
            height: 50px;
            background-color: white;
            font-size: 14px;
        }

        .vipBtn img {
            position: absolute;
            right: 50%;
            top: 120px;
            width: 50px;
            height: 50px;
        }

        .vipBtn > div {
            width: 50%;
            height: 50px;
            line-height: 50px;
            float: left;
            text-align: center;
        }

        .vipBtn2 {
            margin-top: 1px;
            width: 100%;
            height: 50px;
            background-color: white;
            font-size: 14px;
        }

        .vipBtn2 > div {
            width: 100%;
            height: 50px;
            line-height: 50px;
            float: left;
            text-align: center;
            font-size: 14px;
        }

        .cat_pic {
            width: 100%;
            height: 34px;
        }

        .cat_pic img {
            width: 36%;
            margin-left: 32%;
            margin-top: 8%;

        }

        .cat_status {
            width: 100%;
            height: 30px;
            text-align: center;
            line-height: 30px;
            font-size: 12px;
        }

        .list_area {
            width: 94%;
            float: left;
            margin-left: 3%;
            background: white;
            border-radius: 8px;
            margin-top: 20px;

        }

        .list_area_title {
            width: 100%;
            height: 40px;
            line-height: 40px;
        }

        .list_area_title span:first-child {
            width: 5%;
            float: left;
            display: block;
            height: 20px;
            border-right: 5px solid orangered;
            margin-top: 8px;

        }

        .list_area_title span:nth-child(2) {
            width: 26%;
            display: block;
            height: 36px;
            float: left;
            line-height: 36px;
            text-align: center;
            font-weight: bold;
            font-size: 16px;
        }

        .list_area_title span:nth-child(3) {
            width: 60%;
            display: block;
            height: 36px;
            float: right;
            line-height: 36px;
            text-align: right;
            color: #999;
            font-size: 12px;
        }

        .list_area_title a {
            color: #999;
        }

        .list_area_action {
            width: 100%;
            height: 40px;
            font-size: 14px;
            line-height: 40px;
            text-align: left;
        }

        .list_area_action span {
            color: #999;
            margin-left: 6%;
            font-size: 12px;
        }

        .list_area_action button {
            width: 90%;
            height: 36px;
            margin-top: 12px;
            margin-left: 5%;
            line-height: 36px;
            font-size: 16px;
            border-radius: 10px;
            border: 0;
            background: #FFAA25;
            color: #862116;
        }

        .list_area_full {
            width: 100%;
            float: left;

            background: white;
            margin-top: 20px;

        }

        .daily_area_action_history {
            width: 100%;

        }

        .daily_area_action_history div {
            width: 33%;
            height: 140px;
            float: left;
        }

        .daily_area_action_history div img {
            width: 80%;
            display: block;
            float: left;
            margin-left: 10%;
            max-height: 110px;
        }

        .daily_area_action_history div p {
            width: 70%;
            line-height: 30px;
            text-align: center;
            font-size: 8px;
            float: left;
            height: 30px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-left: 15%;
        }



        .daily_area_action_line {
            width: 100%;
            height: 50px;
            border-top: 1px solid #e8e8e8;
            line-height: 50px;
            font-size: 0.9em;
        }

        .daily_area_action_line span:first-child {
            margin-left: 10px;

        }

        .daily_area_action_line span:nth-child(2) {
            margin-left: 5px;

        }

        .daily_area_action_line span:nth-child(3) {
            float: right;

        }

        .daily_area_action_line img {
            width: 7px;
            height: 7px;
            margin-bottom: 10px;

        }


    </style>


    <div id="container">


        <!--头部开始-->
        <div class="cat_head" v-cloak>

            <div class="my_img">
                <img :src="user_avatar" class="img-responsive img-circle"/>
            </div>
            <div class="my_text">
                <div>{{user_nickname}}</div>
                <div>ID：{{user_id}}</div>
                <div>{{vip_mes_output}}</div>

            </div>


        </div>
        <!--头部结束-->


        <!--黑卡任务开始-->
        <div class="list_area" style="margin-top: -40px;" v-show="isHeikaVip==1" v-cloak @click.stop="click_heika_task">

            <div class="list_area_title">
                <span></span>

                <span>今日任务</span>


                <span>
                   查看详情&nbsp;>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                </span>

            </div>
            <div class="list_area_action">
                <span>{{heika_task_content_text}}</span>
                <span style="margin-left: 1px;">（{{heika_task_content_completeness}}）</span>
            </div>

        </div>
        <!--黑卡任务结束-->


        <!--恢复vip-->
        <div class="list_area" style="margin-top: -40px;" v-show="old_system_user_save_viptime>0" v-cloak>

            <div class="list_area_action">
                <span>您有暂停的VIP时间，可立即恢复！</span>
            </div>

            <div class="list_area_action" style="height: 60px;">
                <a :href="get_recover_vip_url()">
                    <button>恢复VIP时间</button>
                </a>
            </div>

        </div>
        <!--恢复vip-->


        <!--老会员升级或开通黑卡-->
        <div class="list_area" style="margin-top: -40px;" v-show="isOldVip==1&&old_system_user_save_viptime<=0" v-cloak>

            <div class="list_area_action">
                <span>{{vipaction_text}}</span>
            </div>

            <div class="list_area_action" style="height: 60px;" v-show="!is_old_system_vip_over_100()">
                <a :href="get_vipaction_url()">
                    <button>开通并升级黑卡VIP</button>
                </a>
            </div>

            <div class="list_area_action" style="height: 60px;" v-show="is_old_system_vip_over_100()">
                <a @click="old_viptime_go_to_heika">
                    <button>免费升级黑卡VIP</button>
                </a>
            </div>
        </div>
        <!--老会员升级或开通黑卡-->


        <!--非会员开通黑卡-->
        <div class="list_area" style="margin-top: -40px;"
             v-show="isOldVip!=1&&isHeikaVip!=1&&old_system_user_save_viptime<=0" v-cloak>

            <div class="list_area_action">
                <span>{{vipaction_text}}</span>
            </div>

            <div class="list_area_action" style="height: 60px;">
                <a :href="get_vipaction_url()">
                    <button>开通黑卡VIP</button>
                </a>
            </div>

        </div>
        <!--非会员开通黑卡-->


        <!--学习历史-->
        <div class="list_area_full" v-show="history_cat_arr.length>0" v-cloak>


            <div class="daily_area_action_line">
                <span class="glyphicon glyphicon-time" aria-hidden="true" style="color: #F74655"></span>
                <span>学习历史</span>
            </div>

            <div class="daily_area_action_history" v-for="(history_cat,key) in history_cat_arr">


                <div @click="click_cat(history_cat.cat_id)">
                    <img :src="history_cat.cat_img_url">

                    <p>{{history_cat.name}}</p>

                </div>

            </div>
        </div>
        <!--学习历史-->


        <!--其他菜单-->



        <div class="list_area_full" v-cloak>


            <a :href="get_today_review_url()">
                <div class="daily_area_action_line">
                    <span class="glyphicon glyphicon-retweet" aria-hidden="true" style="color: #F74655"></span>
                    <span>今日复习</span>
                    <img :src="get_today_review_red_point_url()" v-show="today_review_num>0">
                </div>
            </a>




            <!-- 重置课程记录功能 - 复刻page-setting.php的清空功能 -->
            <div class="daily_area_action_line" @click.stop="reset_course_records"
                 :style="{ cursor: is_have_review_mes == 1 ? 'pointer' : 'not-allowed', opacity: is_have_review_mes == 1 ? 1 : 0.5 }">
                <span class="glyphicon glyphicon-refresh" aria-hidden="true" style="color: #F74655"></span>
                <span v-if="is_have_review_mes == 1" style="color: #333">重置课程记录</span>
                <span v-else style="color: #999">重置课程记录</span>
            </div>
        </div>
        <!--其他菜单-->


    </div>


    <script>

        var container = new Vue({
            el: '#container',
            data: {

                user_id: "<?php echo $userData->user_id;?>",//用户id
                user_avatar: "<?php echo $userData->user_avatar;?>",
                user_nickname: "<?php echo $userData->user_nickname;?>",

                vip_mes_output: "<?php echo $vip_mes_output;?>",

                is_test_user: "<?php echo $is_test_user;?>",
                isOldVip: "<?php echo $userData->isOldVip;?>",

                old_system_vip_time: <?php echo !empty($heikaHandle->old_system_vip_time) ? $heikaHandle->old_system_vip_time : '0'; ?>,//老系统vip时间
                old_system_user_save_viptime: <?php echo !empty($heikaHandle->old_system_user_save_viptime) ? $heikaHandle->old_system_user_save_viptime : '0'; ?>,//老系统保存时间vip时间
                old_system_last_time: <?php echo !empty($heikaHandle->old_system_last_time) ? $heikaHandle->old_system_last_time : '0'; ?>,


                isHeikaVip: "<?php echo $userData->isHeikaVip;?>",
                nowTime:<?php echo $nowTime;?>,
                vipaction_text: "<?php echo $vipaction_text;?>",
                vipaction_url: "<?php echo $vipaction_url;?>",
                recover_vip_url: "<?php echo $recover_vip_url;?>",
                heika_task_url: "<?php echo $heika_task_url;?>",
                heika_task_content_text: "<?php echo $heika_task_content_text;?>",
                heika_task_content_completeness: "<?php echo $heika_task_content_completeness;?>",
                history_cat_arr:<?php echo $history_cat_json;?>,
                today_review_url:"<?php echo $today_review_url;?>",
                my_achieve_url:"<?php echo $my_achieve_url;?>",
                setting_url:"<?php echo $setting_url;?>",
                today_review_num:<?php echo $today_review_num;?>,
                today_review_red_point_url:"<?php echo $today_review_red_point_url;?>",
                web_url:"<?php echo home_url()?>",
                AJAX_ERROR_NUM: 0,
                ajax_url:"<?php echo $ajax_url;?>"


            },
            mounted: function() {
                // 前端验证用户头像和昵称
                this.validateUserData();
            },
            methods: {

                // 验证用户数据的完整性
                validateUserData: function() {
                    // 验证用户头像
                    if (!this.user_avatar || this.user_avatar.trim() === '' || this.user_avatar === 'undefined') {
                        this.user_avatar = this.web_url + '/img/avatar_male.png';
                        console.log('[page-me.php] 用户头像为空，使用默认头像');
                    }

                    // 验证用户昵称
                    if (!this.user_nickname || this.user_nickname.trim() === '' || this.user_nickname === 'undefined') {
                        this.user_nickname = '匿名用户';
                        console.log('[page-me.php] 用户昵称为空，使用默认昵称');
                    }
                },

                click_heika_task:function(){
                    window.location.href=this.heika_task_url;
                },

                click_cat: function (cat_id) {

                    window.location.href=this.web_url+"/?cat="+cat_id;

                },

                get_today_review_red_point_url:function(){
                    return this.today_review_red_point_url;
                },


                get_vipaction_url: function () {
                    return this.vipaction_url;
                },

                get_recover_vip_url: function () {
                    return this.recover_vip_url;
                },
                get_heika_task_url: function () {

                    return this.heika_task_url;
                },


                get_today_review_url: function () {

                    return this.today_review_url;
                },

                get_my_achieve_url: function () {

                    return this.my_achieve_url;
                },


                get_setting_url: function () {

                    return this.setting_url;
                },


                is_old_system_vip_over_100: function () {

                    if (this.old_system_last_time >= 100 * 24 * 3600) {
                        return true;
                    }

                    return false;

                },

                old_viptime_go_to_heika: function () {
                    //alert("老会员升级为黑卡");

                    var _this = this;

                    $$alert("提示","升级为黑卡VIP之后，会员有效期不会产生变化，连续一段时间完成学习任务，还可获得现金奖励。",function(){

                        $$loading("加载中");

                        _this.old_viptime_go_to_heika_ajax({
                            user_id: _this.user_id,
                            action: 'upgrade_old_to_heika'
                        }, function (res) {

                            $$closeLoading();


                            if(res!=0){

                                $$alert("提示","升级成功",function(){

                                    location.reload()

                                })

                            }else{
                                $$alert("提示","升级失败，请重试！",function(){
                                    location.reload()

                                })
                            }
                        });

                    });






                },




                old_viptime_go_to_heika_ajax: function (data, callback) {
                    var _this = this;


                    var doAjax = function () {
                        //ajax函数
                        $.ajax({
                            url: _this.ajax_url,
                            type: 'post',
                            data: data,
                            success: function (data, status) {
                                _this.AJAX_ERROR_NUM = 0;//ajax错误状态归0


                                //将结果转为字符串，去掉收尾空格
                                var res = data.toString().trim();
                                callback(res);

                            },
                            error: function () {
                                _this.AJAX_ERROR_NUM += 1;//ajax错误状态递增
                                if (_this.AJAX_ERROR_NUM >= 4) {
                                    _this.AJAX_ERROR_NUM = 0;//ajax错误状态归0
                                } else {
                                    doAjax();//第二次以上执行
                                }
                            }



                        });

                    };

                    doAjax();//执行AJAX

                }


            },

            computed: {},

            watch: {}
        });

        /*
         * 开始课程
         * */


        //console.log(container.history_cat_arr);
        //console.log(container.course_learning_stage_json);

        //console.log(container.is_test_user);

        $("body").css("background", "#F5F5F5");


        /*后退强制刷新*/

        var TYPE_NAVIGATE = 0, // 当前页面是通过点击链接，书签和表单提交，或者脚本操作，或者在url中直接输入地址，type值为0
            TYPE_RELOAD = 1, // 点击刷新页面按钮或者通过Location.reload()方法显示的页面，type值为1
            TYPE_BACK_FORWARD = 2, // 页面通过历史记录和前进后退访问时。type值为2
            TYPE_RESERVED = 255; // 任何其他方式，type值为255
        window.addEventListener('pageshow', function(e) {
            if (e.persisted || (window.performance && window.performance.navigation.type == TYPE_BACK_FORWARD)) {
                location.reload()
            }
        }, false)


    </script>


    <!-- 底部导航组件 -->
    <?php
    // 获取底部导航配置
    $course_text = get_option('g_bottom_nav_course_text');
    $course_text = !empty($course_text) ? $course_text : '课程';

    $course_url = get_option('g_bottom_nav_course_url');
    $course_url = !empty($course_url) ? $course_url : '/';

    $me_text = get_option('g_bottom_nav_me_text');
    $me_text = !empty($me_text) ? $me_text : '我的';

    $me_url = get_option('g_bottom_nav_me_url');
    $me_url = !empty($me_url) ? $me_url : '/个人中心/';

    // 构建完整URL
    $course_full_url = home_url($course_url);
    $me_full_url = home_url($me_url);
    ?>

    <style>
    /* 自定义底部导航样式 */
    .custom-bottom-nav {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        box-shadow: 0 -4px 20px rgba(247, 70, 85, 0.15);
        z-index: 1000;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .custom-nav-container {
        display: flex;
        width: 100%;
        max-width: 500px;
        margin: 0 auto;
    }

    .custom-nav-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        color: #666666;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        padding: 8px 10px;
        position: relative;
        border-radius: 12px;
        margin: 0 8px;
    }

    .custom-nav-item:hover {
        text-decoration: none;
        color: #F74655;
        background: rgba(247, 70, 85, 0.05);
        transform: translateY(-2px);
    }

    .custom-nav-item.active {
        color: #F74655;
    }

    .custom-nav-item.active::before {
        content: '';
        position: absolute;
        top: -2px;
        left: 50%;
        transform: translateX(-50%);
        width: 30px;
        height: 3px;
        border-radius: 0 0 3px 3px;
    }

    .custom-nav-icon {
        font-size: 20px;
        margin-bottom: 4px;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 28px;
        height: 28px;
    }

    .custom-nav-item.active .custom-nav-icon {
        transform: scale(1.1);
        color: #F74655;
    }

    .custom-nav-text {
        font-size: 11px;
        font-weight: 500;
        line-height: 1.2;
        color: inherit;
        text-align: center;
    }

    /* 为页面内容添加底部间距，避免被导航遮挡 */
    body {
        padding-bottom: 60px !important;
    }

    /* 移动端适配 */
    @media screen and (max-width: 768px) {
        .custom-bottom-nav {
            height: 55px;
        }

        body {
            padding-bottom: 55px !important;
        }

        .custom-nav-icon {
            font-size: 18px;
            width: 26px;
            height: 26px;
        }

        .custom-nav-text {
            font-size: 10px;
        }
    }

    /* 小屏幕设备 */
    @media screen and (max-width: 480px) {
        .custom-nav-container {
            margin: 0 5px;
        }

        .custom-nav-item {
            margin: 0 3px;
            padding: 6px 8px;
        }
    }
    </style>

    <div class="custom-bottom-nav">
        <div class="custom-nav-container">
            <a href="<?php echo $course_full_url; ?>" class="custom-nav-item">
                <div class="custom-nav-icon">
                    <span class="glyphicon glyphicon-education" style="color: #666666;"></span>
                </div>
                <div class="custom-nav-text"><?php echo $course_text; ?></div>
            </a>

            <a href="<?php echo $me_full_url; ?>" class="custom-nav-item active">
                <div class="custom-nav-icon">
                    <span class="glyphicon glyphicon-user" style="color: #F74655;"></span>
                </div>
                <div class="custom-nav-text"><?php echo $me_text; ?></div>
            </a>
        </div>
    </div>

    <?php

} else {
    get_login_page();//提示登录
}


get_footer();
?>
