<?php
/*
Template Name:ajax
*/

/**
 * AJAX处理页面 - 课程数据通讯地址 (page_id=13794)
 *
 * 处理来自single.php的AJAX请求，包括：
 * - selectWord: 查询单词释义
 * - addWord: 添加单词到生词本
 * - saveReviewMes3: 保存复习信息
 * - save_course_rate_of_progress: 保存课程进度
 * - user_listening_behavior: 保存用户行为数据

 *
 * <AUTHOR> Theme
 * @version 1.0
 */

// 开启错误日志记录
if (defined('WP_DEBUG') && WP_DEBUG) {
    error_log('[page-ajax.php] AJAX请求开始 - ' . date('Y-m-d H:i:s'));
    error_log('[page-ajax.php] POST数据: ' . print_r($_POST, true));
}

$action = $_POST['action'];
if (!$action) {
    error_log('[page-ajax.php] 错误: 缺少action参数');
    echo "禁止访问 - 缺少action参数";
    exit;
}

error_log('[page-ajax.php] 处理action: ' . $action);


/*下载微信文件 - 已停用但保留以防兼容性需要*/

/*
function downloadWeixinAudioAndRecognition($url, $media_id, $user_id)
{
    // 功能已停用，返回空数组以保持兼容性
    error_log('[page-ajax.php] downloadWeixinAudioAndRecognition功能已停用');
    return array();
}
*/

/*PHP数组相似度比较*/


/**
 * 查询单词释义
 * 处理单词查询请求
 */
if ($action == "selectWord") {
    error_log('[page-ajax.php] 开始查询单词');

    $word = $_POST['word'];//要查询的单词

    // 参数验证
    if (!$word) {
        error_log('[page-ajax.php] 查询单词失败: 缺少word参数');
        echo "";
        exit;
    }

    error_log('[page-ajax.php] 查询单词: ' . $word);

    $res = get_dict_data($word);

    if ($res) {
        if ($res == 2) {
            error_log('[page-ajax.php] 单词查询结果: 没有释义');
            echo false;//输出false,代表没有释义
        } else {
            error_log('[page-ajax.php] 单词查询成功');
            echo $res;//输出结果,代表正常
        }
    } else {
        error_log('[page-ajax.php] 单词查询结果: 没有查到这个词');
        echo "";//输出空代表没有查到这个词
    }
    exit;
}//查词

/**
 * 添加单词到生词本
 * 处理单词添加请求
 */
if ($action == "addWord") {
    error_log('[page-ajax.php] 开始添加单词到生词本');

    $book_id = $_POST['book_id'];
    $wordori = $_POST['wordori'];

    // 参数验证
    if (!$book_id || !$wordori) {
        error_log('[page-ajax.php] 添加单词失败: 参数不完整 book_id=' . $book_id . ', wordori=' . $wordori);
        echo "0"; // 参数错误
        exit;
    }

    error_log('[page-ajax.php] 添加单词: ' . $wordori . ' 到生词本: ' . $book_id);

    $word_str = get_post_meta($book_id, "new_words_value", true);

    if ($word_str) {//有字符串
        if (strpos($word_str, "#")) {
            //字符串带#，多个单词
            $word_str_arr = explode("#", $word_str);
            $isExist = false;
            foreach ($word_str_arr as $word) {//比对单词是否存在
                if ($wordori == trim($word)) {
                    $isExist = true;
                    break;
                }
            }
            if (!$isExist) {//如果不存在
                array_push($word_str_arr, $wordori);
                $new_word_str = implode("#", $word_str_arr);
                if (update_post_meta($book_id, "new_words_value", $new_word_str)) {
                    error_log('[page-ajax.php] 单词添加成功');
                    echo "1";//成功
                    exit;
                } else {
                    error_log('[page-ajax.php] 单词添加失败: 数据库更新失败');
                }
            } else {
                error_log('[page-ajax.php] 单词已存在');
                echo "2";//已存在
                exit;
            }
        } else {
            //只有一个单词
            if (trim($word_str) == $wordori) {
                error_log('[page-ajax.php] 单词已存在');
                echo "2";//已存在
                exit;
            }
            $new_word_str = $word_str . "#" . $wordori;
            if (update_post_meta($book_id, "new_words_value", $new_word_str)) {
                error_log('[page-ajax.php] 单词添加成功');
                echo "1";//成功
                exit;
            } else {
                error_log('[page-ajax.php] 单词添加失败: 数据库更新失败');
            }
        }
    } else {
        //第一个单词
        if (update_post_meta($book_id, "new_words_value", $wordori)) {
            error_log('[page-ajax.php] 首个单词添加成功');
            echo "1";//成功
            exit;
        } else {
            error_log('[page-ajax.php] 首个单词添加失败: 数据库更新失败');
        }
    }

    error_log('[page-ajax.php] 单词添加失败');
    echo "0"; // 失败
    exit;
}//添加单词


/*
 *
 *
 * 保存复习信息*/


if ($action == "saveReviewMes3") {


    /*用户ID*/
    $user_id = $_POST['user_id'];


    /*课程ID*/
    $post_id = $_POST['post_id'];


    /*课程测试错误次数*/
    $fail_num = $_POST['fail_num'];//综合抽查错误数
    if (!$fail_num) {
        $fail_num = 0;
    }


    /*课程分类ID*/
    $model = $_POST['model'];


    /*日常任务活动标识 - 已停用VIP功能*/
    $daily_aciton = $_POST['daily_aciton'];//日常活动是否开启，1为开启，0为不开启（VIP功能已停用）

    $todaymorning = strtotime(current_time("Y-m-d"));//当天0点

    $todaynight = $todaymorning + 24 * 3600 - 1;//当天23.59


    $review_mes_json = get_user_meta($user_id, "review_mes", true);//获取复习信息json

    if (!$review_mes_json) {//复习信息json值为空的情况


        update_user_meta($user_id, "review_begin_time", current_time("timestamp"));//更新学习开始时间
        update_user_meta($user_id, "review_now_time", current_time("timestamp"));//更新最新学习时间

        // VIP功能已停用，保留日常活动标识但不执行VIP操作


        /*完成次数*/
        $finishTimes = 1;

        /*下次复习时间*/
        $reviewTime = current_time("timestamp") + 24 * 3600 * 1;

        /*将课程ID 复习次数 课程分类ID 下次复习时间 测试错误次数 封装成一个 数组*/
        $json_arr = array("post_id" => $post_id, "finishTimes" => $finishTimes, "model" => $model, "reviewTime" => $reviewTime, "fail_num" => $fail_num);

        /*复习信息总数组*/
        $new_review_mes_arr = array();

        /*将单个课程的复习信息数组 合并入 复习信息总数组*/
        array_push($new_review_mes_arr, $json_arr);

        /*复习信息总数组转json*/
        $new_review_mes_json = json_encode($new_review_mes_arr);

        /*保存数据 - VIP功能已停用*/
        if (update_user_meta($user_id, 'review_mes', $new_review_mes_json)) {
            echo 101;//记录修改成功
        } else {
            echo 100;//记录修改失败
        }


        delete_user_learning_mes($user_id, $post_id);//删除课程访问信息
        return;
    } else {
        $nowTime = current_time("timestamp");//当前时间
        $review_now_time = get_user_meta($user_id, "review_now_time", true);


        // VIP功能已停用，保留时间检查逻辑但不执行VIP操作
        if ($review_now_time < $todaymorning) {
            //复习时间在昨天，VIP功能已停用
        }

        update_user_meta($user_id, "review_now_time", $nowTime);


        $review_mes_arr = json_decode($review_mes_json);//解析json

        for ($i = 0; $i < count($review_mes_arr); $i++) {
            if ($post_id == $review_mes_arr[$i]->post_id) {
                //找到单词本
                if ($review_mes_arr[$i]->reviewTime <= $todaynight && $review_mes_arr[$i]->reviewTime >= $todaymorning) {//如果计划复习时间>今天早上，小于今天晚上,计入复习
                    $review_mes_arr[$i]->finishTimes += 1;//复习次数+1
                    $review_mes_arr[$i]->model = $model;//课程分类
                    $review_mes_arr[$i]->fail_num = $fail_num;//错误次数
                    switch ($review_mes_arr[$i]->finishTimes) {
                        case 2:
                            $review_mes_arr[$i]->reviewTime = current_time("timestamp") + 24 * 3600 * 2;//两天
                            break;
                        case 3:
                            $review_mes_arr[$i]->reviewTime = current_time("timestamp") + 24 * 3600 * 2;//两天
                            break;
                        case 4:
                            $review_mes_arr[$i]->reviewTime = current_time("timestamp") + 24 * 3600 * 4;//四天
                            break;
                        case 5:
                            $review_mes_arr[$i]->reviewTime = current_time("timestamp") + 24 * 3600 * 6;//六天
                            break;
                        default:
                            $review_mes_arr[$i]->reviewTime = 0;
                            break;

                    }//修改下次复习时间
                    $new_review_mes_json = json_encode($review_mes_arr);//转为json
                    // VIP功能已停用，直接保存复习数据
                    if (update_user_meta($user_id, 'review_mes', $new_review_mes_json)) {
                        echo 101;//记录修改成功
                    } else {
                        echo 100;//记录修改失败
                    }
                    return;

                } else {//不计入复习
                    echo 0;//输出0代表复习时间不正确
                    return;
                }
            }
        }//循环查找单词本，如果存在，则修改值；

        //没有找到该单词本
        $finishTimes = 1;
        $reviewTime = current_time("timestamp") + 24 * 3600 * 1;
        $json_arr = array("post_id" => $post_id, "finishTimes" => $finishTimes, "model" => $model, "reviewTime" => $reviewTime, "fail_num" => $fail_num);
        array_push($review_mes_arr, $json_arr);
        $new_review_mes_json = json_encode($review_mes_arr);//转为json

        // VIP功能已停用，直接保存复习数据
        if (update_user_meta($user_id, 'review_mes', $new_review_mes_json)) {
            echo 101;//记录修改成功
        } else {
            echo 100;//记录修改失败
        }

        delete_user_learning_mes($user_id, $post_id);//删除课程访问信息
        return;
    }
}

/**
 * 朋友圈打卡 - 功能已停用
 * 保持兼容性，返回已签到状态
 */
if ($action == "sign_in_wechat_timeline") {
    error_log('[page-ajax.php] sign_in_wechat_timeline功能已停用，返回兼容性响应');
    echo 5; // 返回已签到状态以保持兼容性
    exit;
}


/**
 * 获得语音结果 - 功能已停用
 * 保持兼容性，返回空结果
 */
if ($action == "giveAMark") {
    error_log('[page-ajax.php] giveAMark功能已停用，返回兼容性响应');
    echo ""; // 返回空结果以保持兼容性
    exit;
}


/**
 * 有赞订单开通VIP - 功能已停用
 * 保持兼容性，返回订单已使用状态
 */
if ($action == "youzan_vip_go") {
    error_log('[page-ajax.php] youzan_vip_go功能已停用，返回兼容性响应');
    echo 5; // 返回订单已使用状态以保持兼容性
    exit;
}


if ($action == "youzan_vip_go_disabled") {

    require_once 'includes/youzanSdk/lib/YZTokenClient.php';

    $user_id = $_POST['user_id'];
    $tid = $_POST['tid'];


    if (!search_youzan_order_by_order_no($tid)) {//数据库里没有订单

        //检查有赞订单 并开通VIP

        /*
         * 抓取订单数据
         *
         * */

        $youzan_client_id = "a3b0ef40ff671be069";//有赞client_id
        $youzan_client_secret = "86a8f864d6af8ea09bebf57972c560de";//有赞client_secret
        $grant_type = "silent";//有赞默认参数
        $kdt_id = 17925206;//有赞店铺ID
        $url = "https://open.youzan.com/oauth/token";
        $post_string = "client_id={$youzan_client_id}&client_secret={$youzan_client_secret}&grant_type=silent&kdt_id={$kdt_id}";
        $resobj = json_decode(http_request($url, $post_string));
        $youzan_token = $resobj->access_token;//有赞acess_token


        $client = new YZTokenClient($youzan_token);
        $method = 'youzan.trade.get'; //要调用的api名称
        $api_version = '4.0.0'; //要调用的api版本号
        $my_params = array('tid' => $tid);
        $my_files = array();
        $youzan_order_res = $client->post($method, $api_version, $my_params, $my_files);


        /*
         * 订单是否已经支付
         *
         * */
        $is_user_payed = false;//订单状态预设为未支付

        $order_info_obj = $youzan_order_res["response"]["full_order_info"]["order_info"];//订单信息对象
        $order_status = $order_info_obj["status_str"];//获取订单状态
        $is_payed = $order_info_obj["order_tags"]["is_payed"];//订单是否支付
        if ($order_status == "已发货" && $is_payed) {//若订单状态为“已发货”并且订单已支付
            $is_user_payed = true;//确定用户已支付.
        }


        /*
            * 订单已支付的情况下，获取订单信息及用于信息
            * */


        if ($is_user_payed) {


            $order_obj = $youzan_order_res["response"]["full_order_info"]["orders"][0];//订单对象
            $bought_number = (int)$order_obj["num"];//购买数量
            $vip_days = $bought_number * (int)$order_obj["outer_item_id"];//应该开通的VIP天数

            $order_price = (float)$order_obj["total_fee"];//订单金额
            //myDump($order_price);
            $order_time_stp = current_time("timestamp");//时间戳；

            //myDump($order_price);
            create_youzan_order($tid, $user_id, $order_price, $order_time_stp);//在数据库内保存有赞的订单


            if (add_user_viptime($user_id, $vip_days)) {
                //为用户增加VIP时间

                echo $vip_days;//用户增加VIP时间成功；
            } else {
                echo 3;//发生异常，需要联系客服
            }


            $buyer_obj = json_decode($order_obj["buyer_messages"]);//用户对象
            $buyer_number = "";//用户手机号
            foreach ($buyer_obj as $n) {
                $buyer_number = $n;//获得用户手机号
            }

            update_user_meta($user_id, "telephone_number", $buyer_number);//保存用户手机号*/


        } else {
            echo 4;//订单未支付
        }


    } else {
        echo 5;//订单已经使用
    }


}
*/

/**
 * 暂停VIP - 功能已停用
 * 保持兼容性，返回失败状态
 */


if ($action == "pause_vip") {
    error_log('[page-ajax.php] pause_vip功能已停用，返回兼容性响应');
    echo 0; // 返回失败状态以保持兼容性
    exit;
}


/**
 * 恢复VIP - 功能已停用
 * 保持兼容性，返回失败状态
 */
if ($action == "recover_vip") {
    error_log('[page-ajax.php] recover_vip功能已停用，返回兼容性响应');
    echo 0; // 返回失败状态以保持兼容性
    exit;
}


/*
 * 语音评分
 *
 * */

/**
 * 语音评测功能 - 功能已停用
 * 保持兼容性，返回默认分数
 */
if ($action == "speech_assessment") {
    error_log('[page-ajax.php] speech_assessment功能已停用，返回兼容性响应');
    echo 60; // 返回默认分数以保持兼容性
    exit;
}


/**
 * 语音评测功能2 - 功能已停用
 * 保持兼容性，返回空响应
 */
if ($action == "speech_assessment2") {
    error_log('[page-ajax.php] speech_assessment2功能已停用，返回兼容性响应');
    echo ""; // 返回空响应以保持兼容性
    exit;
}


/*
 *
 * 保存用户学习进度
 *
 * */

/**
 * 保存课程学习进度
 * 处理来自single.php的save_course_rate_of_progress_ajax调用
 */
if ($action == "save_course_rate_of_progress") {
    error_log('[page-ajax.php] 开始保存课程进度');

    $user_id = $_POST['user_id'];
    $post_id = $_POST['post_id'];
    $maxpaged = $_POST['maxpaged'];

    // 参数验证
    if (!$user_id || !$post_id || !$maxpaged) {
        error_log('[page-ajax.php] 保存课程进度参数不完整: user_id=' . $user_id . ', post_id=' . $post_id . ', maxpaged=' . $maxpaged);
        echo "参数错误";
        exit;
    }

    error_log('[page-ajax.php] 保存课程进度 - 用户ID: ' . $user_id . ', 课程ID: ' . $post_id . ', 最大页数: ' . $maxpaged);

    $course_rate_of_progress_json = get_user_meta($user_id, "course_rate_of_progress", true);//获取课程进度json

    if (!$course_rate_of_progress_json) {
        //课程进度为空，创建新记录
        error_log('[page-ajax.php] 创建新的课程进度记录');

        /*将课程ID 课程最大翻页 封装成一个 数组*/
        $json_arr = array("post_id" => $post_id, "maxpaged" => $maxpaged, "timestamp" => current_time("timestamp"));

        /*课程进度总数组*/
        $new_course_rate_of_progress_arr = array();

        /*将单个课程的复习信息数组 合并入 课程进度总数组*/
        array_push($new_course_rate_of_progress_arr, $json_arr);

        /*复习信息总数组转json*/
        $new_course_rate_of_progress_json = json_encode($new_course_rate_of_progress_arr);

        /*保存数据*/
        if (update_user_meta($user_id, 'course_rate_of_progress', $new_course_rate_of_progress_json)) {
            error_log('[page-ajax.php] 新课程进度保存成功');
        } else {
            error_log('[page-ajax.php] 新课程进度保存失败');
        }

    } else {
        //更新现有课程进度
        error_log('[page-ajax.php] 更新现有课程进度');

        $course_rate_of_progress_arr = json_decode($course_rate_of_progress_json);//解析json

        $has_progress = false;//是否有进度

        for ($i = 0; $i < count($course_rate_of_progress_arr); $i++) {
            if ($post_id == $course_rate_of_progress_arr[$i]->post_id) {
                //找到课程
                $has_progress = true;
                if ($course_rate_of_progress_arr[$i]->maxpaged < $maxpaged) {
                    //如果课程最大翻页数<当前翻页数
                    error_log('[page-ajax.php] 更新课程最大页数从 ' . $course_rate_of_progress_arr[$i]->maxpaged . ' 到 ' . $maxpaged);
                    $course_rate_of_progress_arr[$i]->maxpaged = $maxpaged;//记录最大翻页
                    $course_rate_of_progress_arr[$i]->timestamp = current_time("timestamp");//记录时间戳
                }
                break;
            }
        }

        if (!$has_progress) {//无进度处理
            error_log('[page-ajax.php] 为课程添加新的进度记录');
            $json_arr = array("post_id" => $post_id, "maxpaged" => $maxpaged, "timestamp" => current_time("timestamp"));
            array_push($course_rate_of_progress_arr, $json_arr);
        }

        $new_course_rate_of_progress_json = json_encode($course_rate_of_progress_arr);//转为json

        /*保存数据*/
        if (update_user_meta($user_id, 'course_rate_of_progress', $new_course_rate_of_progress_json)) {
            error_log('[page-ajax.php] 课程进度更新成功');
        } else {
            error_log('[page-ajax.php] 课程进度更新失败');
        }
    }

    // 返回成功信息
    echo json_encode(array(
        'status' => 'success',
        'maxpaged' => $maxpaged,
        'user_id' => $user_id,
        'post_id' => $post_id,
        'timestamp' => current_time("timestamp")
    ));
    exit;
}


/*
 *
 * 保存问老师文本
 *
 * */


/**
 * 保存问老师文本 - 功能已停用
 * 保持兼容性，返回成功状态
 */
if ($action == "save_ask_teacher_text") {
    error_log('[page-ajax.php] save_ask_teacher_text功能已停用，返回兼容性响应');
    echo "1"; // 返回成功状态以保持兼容性
    exit;
}


/*
 *
 * 保存用户行为数据
 *
 * */

/**
 * 保存用户学习行为数据
 * 处理来自single.php的用户行为统计
 */
if ($action == "user_listening_behavior") {
    error_log('[page-ajax.php] 开始保存用户行为数据');

    $user_id = $_POST['user_id'];

    // 参数验证
    if (!$user_id) {
        error_log('[page-ajax.php] 用户行为数据保存失败: 缺少user_id');
        echo "参数错误";
        exit;
    }

    /*
     * 获得用户行为json 并转化为数组
     * */
    $user_listening_behavior = get_user_meta($user_id, "user_listening_behavior", true);

    if ($user_listening_behavior) {
        $user_listening_behavior_arr = json_decode($user_listening_behavior);
    } else {
        $user_listening_behavior_arr = array();
    }

    $post_id = $_POST['post_id'];
    $maxpaged = $_POST['maxpaged'];
    $entertime = $_POST['entertime'];
    $is_user_finish_the_course = $_POST['is_user_finish_the_course']; //用户是否完成课程
    $stay_for_time = $_POST['stay_for_time'];

    error_log('[page-ajax.php] 用户行为数据 - 用户ID: ' . $user_id . ', 课程ID: ' . $post_id . ', 停留时间: ' . $stay_for_time);

    /*
     * 新行为数据
     * */
    $new_behavior = array(
        'user_id' => $user_id,
        'post_id' => $post_id,
        'maxpaged' => $maxpaged,
        'entertime' => $entertime,
        'is_user_finish_the_course' => $is_user_finish_the_course,
        'stay_for_time' => $stay_for_time,
        'timestamp' => current_time("timestamp")
    );

    /*
     * 同一时间进入课程，替换之前用户行为
     * */
    if (count($user_listening_behavior_arr) > 0) {
        for ($i = 0; $i < count($user_listening_behavior_arr); $i++) {
            if ($user_listening_behavior_arr[$i]->post_id == $new_behavior['post_id']) {
                if ($user_listening_behavior_arr[$i]->entertime == $new_behavior['entertime']) {
                    error_log('[page-ajax.php] 替换相同时间的行为记录');
                    array_splice($user_listening_behavior_arr, $i, 1);
                    break;
                }
            }
        }
    }

    /*
     * 并入行为总数组
     * */
    array_push($user_listening_behavior_arr, $new_behavior);

    /*
     * 行为最大200个，超出则删除最早的记录
     * */
    if (count($user_listening_behavior_arr) > 200) {
        error_log('[page-ajax.php] 行为记录超过200个，删除最早记录');
        array_shift($user_listening_behavior_arr);
    }

    /*
     * 保存数据
     * */
    $new_user_listening_behavior = json_encode($user_listening_behavior_arr);

    if (update_user_meta($user_id, "user_listening_behavior", $new_user_listening_behavior)) {
        error_log('[page-ajax.php] 用户行为数据保存成功');
        echo json_encode(array(
            'status' => 'success',
            'message' => '用户行为存储成功',
            'behavior_count' => count($user_listening_behavior_arr)
        ));
    } else {
        error_log('[page-ajax.php] 用户行为数据保存失败');
        echo json_encode(array(
            'status' => 'error',
            'message' => '用户行为存储失败'
        ));
    }
    exit;
}


