<?php
/**
 * VIP时间管理AJAX处理函数
 * 
 * 这个文件包含所有与VIP时间管理相关的AJAX处理函数，负责处理来自VIP时间管理页面的请求。
 * 主要功能包括查询用户VIP信息、增加用户VIP时间和删除用户VIP资格。
 * 
 * 迁移说明：
 * 此文件从 wp-content/themes/shuimitao.online/plugins/functions/VIPTimeManagerAjax.php 迁移而来
 * 迁移到插件内部，确保功能独立于主题，提高代码的可维护性
 * 
 * @package VIPTimeManager
 * @version 1.0.0
 * <AUTHOR>
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

// 记录插件AJAX处理器加载
error_log('[VIPTimeManager] AJAX处理器开始加载');

// 定义一天的秒数，避免使用魔术数字
define('SECONDS_PER_DAY', 86400); // 24*3600

/**
 * 验证JSON字符串格式是否有效
 *
 * @param string $json 要验证的JSON字符串
 * @return bool 是否为有效的JSON
 */
function vip_manager_is_valid_json($json) {
    if (empty($json)) {
        return false;
    }
    
    json_decode($json);
    return (json_last_error() == JSON_ERROR_NONE);
}

/**
 * 查询用户VIP时间的AJAX处理函数
 * 
 * 功能概述：响应VIP时间管理页面的用户信息查询请求，返回指定用户的VIP相关信息。被page.php中search_user()函数调用。
 * 
 * 输入参数：
 * - $_POST['user_id']: 整数，要查询的用户ID
 * - $_POST['security']: 字符串，WordPress安全验证nonce
 * 
 * 输出结果：
 * - JSON格式字符串，包含用户ID、昵称、VIP类型、VIP系统和到期时间
 * - 如果查询失败，输出"0"
 * 
 * 核心逻辑：
 * 1. 验证请求安全性和用户权限
 * 2. 从用户元数据中获取黑卡VIP信息
 * 3. 解析JSON数据，检查用户是否拥有有效的VIP权限
 * 4. 构建并返回响应数据
 * 
 * 执行流程：
 * 验证请求 -> 获取用户VIP信息 -> 格式化响应数据 -> 返回JSON响应
 */
function vip_manager_select_user_vip_time() {
    error_log('[VIPTimeManager] select_user_vip_time AJAX请求开始处理');
    
    // 验证nonce以确保请求安全性
    check_ajax_referer('select_user_vip_time_nonce', 'security');
    error_log('[VIPTimeManager] Nonce验证通过');
    
    // 验证用户权限，确保只有管理员可以查询
    if (!current_user_can('manage_options')) {
        error_log('[VIPTimeManager] 权限验证失败 - 用户无管理员权限');
        echo 0; // 权限不足，返回0
        wp_die(); // 终止脚本执行
    }
    
    // 获取请求中的用户ID，确保转换为整数
    $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
    error_log('[VIPTimeManager] 查询用户ID: ' . $user_id);
    
    // 验证用户ID是否有效
    if ($user_id <= 0) {
        error_log('[VIPTimeManager] 用户ID无效: ' . $user_id);
        echo 0; // ID无效，返回0
        wp_die(); // 终止脚本执行
    }
    
    // 获取用户信息对象
    $user = get_user_by('id', $user_id);
    if (!$user) {
        error_log('[VIPTimeManager] 用户不存在: ' . $user_id);
        echo 0; // 用户不存在，返回0
        wp_die(); // 终止脚本执行
    }
    
    error_log('[VIPTimeManager] 用户信息获取成功: ' . $user->display_name);
    
    // 从用户元数据中获取黑卡VIP信息
    $heika_json = get_user_meta($user_id, "heika_vip", true);
    $is_vip = false;
    $expiration_date = 0;
    $card_type="";
    
    error_log('[VIPTimeManager] 黑卡VIP数据: ' . ($heika_json ? $heika_json : '无数据'));
    
    // 如果有黑卡数据，验证并解析JSON
    if (!empty($heika_json)) {
        if (!vip_manager_is_valid_json($heika_json)) {
            error_log("[VIPTimeManager] Invalid heika_vip JSON format for user: {$user_id}");
            $heika_array = array();
        } else {
            $heika_array = json_decode($heika_json, true);
            error_log('[VIPTimeManager] JSON解析成功，黑卡数据条数: ' . count($heika_array));
            
            // 检查用户是否为VIP
            if (!empty($heika_array) && is_array($heika_array)) {
                $current_time = current_time("timestamp");

                foreach ($heika_array as $heika_data) {
                    // 验证必要字段
                    if (!isset($heika_data['card_type'])) {
                        continue;
                    }

                    // 检查课程类型：只有big_course_type为"hlyyin"的VIP卡才有效
                    // 这与前端系统的判断逻辑保持一致（usersData.class.php第178行）
                    if (!isset($heika_data['big_course_type']) || $heika_data['big_course_type'] !== "hlyyin") {
                        error_log('[VIPTimeManager] 跳过VIP卡 - 课程类型不匹配: ' .
                                 (isset($heika_data['big_course_type']) ? $heika_data['big_course_type'] : '未设置'));
                        continue;
                    }

                    // 如果是永久卡，用户是VIP
                    if ($heika_data['card_type'] == "FOREVER") {
                        $card_type="FOREVER";
                        $is_vip = true;
                        $expiration_date = 0; // 永久卡的过期时间为0
                        error_log('[VIPTimeManager] 检测到永久VIP卡（课程类型: hlyyin）');
                        break;
                    }

                    // 如果是有期限的卡，且未过期，用户是VIP
                    if ($heika_data['card_type'] == "HAVE_DATE" &&
                        isset($heika_data['can_use_expiration_date']) &&
                        $heika_data['can_use_expiration_date'] > $current_time) {
                        $is_vip = true;
                        $card_type="HAVE_DATE";
                        $expiration_date = $heika_data['can_use_expiration_date'];
                        error_log('[VIPTimeManager] 检测到有效期VIP卡（课程类型: hlyyin），到期时间: ' . date('Y-m-d H:i:s', $expiration_date));
                        break;
                    }
                }
            }
        }
    }
    
    // 准备返回数据
    $response = array(
        'user_id' => $user_id,
        'user_nickname' => $user->display_name,
        'user_vip_card_type' => $is_vip ? $card_type : '普通用户',
        'user_vip_system' => 'HEIKA',
        'user_vip_expiration_date' => $expiration_date
    );
    
    error_log('[VIPTimeManager] 查询结果: ' . json_encode($response));
    
    // 返回JSON格式的数据
    echo json_encode($response);
    wp_die(); // 终止脚本执行
}
// 注册AJAX处理函数
add_action('wp_ajax_selectUserVipTime', 'vip_manager_select_user_vip_time');
error_log('[VIPTimeManager] selectUserVipTime AJAX action已注册');



/**
 * 增加黑卡VIP时间的AJAX处理函数
 * 
 * 功能概述：响应VIP时间管理页面的增加用户VIP时间请求。被page.php中save_heika_viptime()函数调用。
 * 
 * 输入参数：
 * - $_POST['user_id']: 整数，要增加VIP时间的用户ID
 * - $_POST['days']: 整数，要增加的天数，必须为正数
 * - $_POST['security']: 字符串，WordPress安全验证nonce
 * 
 * 输出结果：
 * - 成功时输出"1"
 * - 失败时输出"0"
 * 
 * 核心逻辑：
 * 1. 验证请求安全性和权限
 * 2. 确保天数为正数
 * 3. 获取用户的黑卡VIP数据并增加时间
 * 4. 更新用户元数据
 * 
 * 执行流程：
 * 验证请求 -> 检查天数 -> 增加VIP时间 -> 返回结果
 */
function vip_manager_increase_heika_vip_time() {
    error_log('[VIPTimeManager] increase_heika_vip_time AJAX请求开始处理');
    
    // 验证nonce以确保请求安全性
    check_ajax_referer('modify_vip_time_nonce', 'security');
    error_log('[VIPTimeManager] Nonce验证通过');
    
    // 验证用户权限，确保只有管理员可以修改
    if (!current_user_can('manage_options')) {
        error_log('[VIPTimeManager] 权限验证失败 - 用户无管理员权限');
        echo 0; // 权限不足，返回0
        wp_die(); // 终止脚本执行
    }
    
    // 获取请求参数
    $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
    $days = isset($_POST['days']) ? intval($_POST['days']) : 0;
    
    error_log('[VIPTimeManager] 增加VIP时间 - 用户ID: ' . $user_id . ', 天数: ' . $days);
    
    // 验证参数
    if ($user_id <= 0 || $days <= 0) {
        error_log('[VIPTimeManager] 参数验证失败 - 用户ID: ' . $user_id . ', 天数: ' . $days);
        echo 0; // 参数无效，返回0
        wp_die(); // 终止脚本执行
    }
    
    // 获取用户信息
    $user = get_user_by('id', $user_id);
    if (!$user) {
        error_log('[VIPTimeManager] 用户不存在: ' . $user_id);
        echo 0; // 用户不存在，返回0
        wp_die(); // 终止脚本执行
    }
    
    // 获取用户的黑卡VIP数据
    $heika_json = get_user_meta($user_id, "heika_vip", true);
    error_log('[VIPTimeManager] 当前黑卡数据: ' . ($heika_json ? $heika_json : '无数据'));
    
    // 验证JSON格式并解析
    if (!empty($heika_json) && !vip_manager_is_valid_json($heika_json)) {
        error_log("[VIPTimeManager] Invalid heika_vip JSON format for user: {$user_id}");
        $heika_array = array();
    } else {
        $heika_array = !empty($heika_json) ? json_decode($heika_json, true) : array();
    }
    
    $current_time = current_time("timestamp");
    $modified = false;
    
    // 如果有黑卡数据
    if (!empty($heika_array) && is_array($heika_array)) {
        
        // 遍历黑卡数据，寻找有期限的卡
        foreach ($heika_array as &$heika_data) {
            if (isset($heika_data['card_type']) && $heika_data['card_type'] == "HAVE_DATE") {
                $old_expiration = isset($heika_data['can_use_expiration_date']) ? $heika_data['can_use_expiration_date'] : 0;

                // 向后兼容：补全缺失的必需字段
                if (!isset($heika_data['big_course_type'])) {
                    $heika_data['big_course_type'] = "hlyyin";
                    error_log('[VIPTimeManager] 向后兼容 - 补全big_course_type字段');
                }
                if (!isset($heika_data['specific_course'])) {
                    $heika_data['specific_course'] = "ALL";
                }
                if (!isset($heika_data['training_expiration_date'])) {
                    $heika_data['training_expiration_date'] = 0;
                }
                if (!isset($heika_data['cash_back_times'])) {
                    $heika_data['cash_back_times'] = 0;
                }

                // 如果卡未过期，增加时间
                if ($old_expiration > $current_time) {
                    $heika_data['can_use_expiration_date'] += $days * SECONDS_PER_DAY;
                    error_log('[VIPTimeManager] 未过期卡延期 - 原到期: ' . date('Y-m-d H:i:s', $old_expiration) .
                             ', 新到期: ' . date('Y-m-d H:i:s', $heika_data['can_use_expiration_date']));
                } else {
                    // 卡已过期，从当前时间开始计算
                    $heika_data['can_use_expiration_date'] = $current_time + $days * SECONDS_PER_DAY;
                    error_log('[VIPTimeManager] 过期卡重新激活 - 新到期: ' . date('Y-m-d H:i:s', $heika_data['can_use_expiration_date']));
                }

                $modified = true;
                break;
            }
        }
        

    } else {
        // 用户没有黑卡数据，创建新的
        $new_expiration = $current_time + $days * SECONDS_PER_DAY;
        $heika_array[] = array(
            "card_type" => "HAVE_DATE",
            "can_use_expiration_date" => $new_expiration,
            "big_course_type" => "hlyyin",        // 必需：课程类型标识，系统硬编码检查此值
            "specific_course" => "ALL",           // 兼容：特定课程标识
            "training_expiration_date" => 0,      // 兼容：培训到期时间
            "cash_back_times" => 0                // 兼容：返现次数
        );
        error_log('[VIPTimeManager] 创建新VIP卡 - 到期时间: ' . date('Y-m-d H:i:s', $new_expiration) . ', 课程类型: hlyyin');
        $modified = true;
    }
    
    // 如果有修改，保存更新后的数据
    if ($modified) {
        $json_result = json_encode($heika_array);
        if ($json_result === false) {
            error_log("[VIPTimeManager] Failed to encode heika_vip data to JSON for user: {$user_id}");
            echo 0;
            wp_die();
        }
        
        $result = update_user_meta($user_id, "heika_vip", $json_result);
        error_log('[VIPTimeManager] 数据库更新结果: ' . ($result ? '成功' : '失败'));
        error_log('[VIPTimeManager] 更新后的数据: ' . $json_result);
        echo $result ? 1 : 0;
    } else {
        error_log('[VIPTimeManager] 没有找到可以修改的HAVE_DATE类型卡片');
        echo 0; // 没有可以修改的记录
    }
    
    wp_die(); // 终止脚本执行
}
// 注册AJAX处理函数
add_action('wp_ajax_increase_heika_vip_time', 'vip_manager_increase_heika_vip_time');
error_log('[VIPTimeManager] increase_heika_vip_time AJAX action已注册');




/**
 * 删除用户黑卡VIP资格的AJAX处理函数
 *
 * 功能概述：响应VIP时间管理页面的删除用户VIP资格请求。被page.php中delete_heika()函数调用。
 *
 * 输入参数：
 * - $_POST['user_id']: 整数，要删除VIP资格的用户ID
 * - $_POST['security']: 字符串，WordPress安全验证nonce
 *
 * 输出结果：
 * - 成功时输出"1"
 * - 失败时输出"0"
 *
 * 核心逻辑：
 * 直接调用WordPress的delete_user_meta函数删除用户的黑卡VIP元数据
 *
 * 执行流程：
 * 验证请求 -> 删除用户VIP资格 -> 返回结果
 *
 * 注意：函数名添加前缀避免与主题中的同名函数冲突
 */
function vip_manager_delete_heika() {
    error_log('[VIPTimeManager] delete_heika AJAX请求开始处理');
    
    // 验证nonce以确保请求安全性
    check_ajax_referer('delete_vip_nonce', 'security');
    error_log('[VIPTimeManager] Nonce验证通过');
    
    // 验证用户权限，确保只有管理员可以删除
    if (!current_user_can('manage_options')) {
        error_log('[VIPTimeManager] 权限验证失败 - 用户无管理员权限');
        echo 0; // 权限不足，返回0
        wp_die(); // 终止脚本执行
    }
    
    // 获取请求参数
    $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
    error_log('[VIPTimeManager] 删除VIP资格 - 用户ID: ' . $user_id);
    
    // 验证参数
    if ($user_id <= 0) {
        error_log('[VIPTimeManager] 用户ID无效: ' . $user_id);
        echo 0; // ID无效，返回0
        wp_die(); // 终止脚本执行
    }
    
    // 获取用户信息
    $user = get_user_by('id', $user_id);
    if (!$user) {
        error_log('[VIPTimeManager] 用户不存在: ' . $user_id);
        echo 0; // 用户不存在，返回0
        wp_die(); // 终止脚本执行
    }
    
    // 记录删除前的数据
    $old_data = get_user_meta($user_id, 'heika_vip', true);
    error_log('[VIPTimeManager] 删除前的VIP数据: ' . ($old_data ? $old_data : '无数据'));
    
    // 直接删除用户的黑卡VIP元数据
    $result = delete_user_meta($user_id, 'heika_vip');
    
    error_log('[VIPTimeManager] VIP资格删除结果: ' . ($result ? '成功' : '失败'));
    
    // 输出结果
    echo $result ? 1 : 0;
    wp_die(); // 终止脚本执行
}
// 注册AJAX处理函数
add_action('wp_ajax_delete_heika', 'vip_manager_delete_heika');
error_log('[VIPTimeManager] delete_heika AJAX action已注册');

error_log('[VIPTimeManager] AJAX处理器加载完成，所有函数已注册');

?>
