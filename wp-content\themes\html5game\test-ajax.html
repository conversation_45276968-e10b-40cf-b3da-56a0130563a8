<!DOCTYPE html>
<html>
<head>
    <title>AJAX功能测试页面</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>page-ajax.php 功能测试</h1>
    
    <div style="margin: 20px; padding: 20px; border: 1px solid #ccc;">
        <h2>1. 查询单词测试</h2>
        <input type="text" id="word" placeholder="输入要查询的单词" value="hello">
        <button onclick="testSelectWord()">查询单词</button>
        <div id="selectWordResult"></div>
    </div>
    
    <div style="margin: 20px; padding: 20px; border: 1px solid #ccc;">
        <h2>2. 添加单词测试</h2>
        <input type="text" id="bookId" placeholder="生词本ID" value="123">
        <input type="text" id="wordToAdd" placeholder="要添加的单词" value="test">
        <button onclick="testAddWord()">添加单词</button>
        <div id="addWordResult"></div>
    </div>
    
    <div style="margin: 20px; padding: 20px; border: 1px solid #ccc;">
        <h2>3. 保存课程进度测试</h2>
        <input type="text" id="userId" placeholder="用户ID" value="1">
        <input type="text" id="postId" placeholder="课程ID" value="456">
        <input type="text" id="maxPaged" placeholder="最大页数" value="10">
        <button onclick="testSaveCourseProgress()">保存进度</button>
        <div id="saveProgressResult"></div>
    </div>
    
    <div style="margin: 20px; padding: 20px; border: 1px solid #ccc;">
        <h2>4. 保存用户行为测试</h2>
        <button onclick="testUserBehavior()">保存用户行为</button>
        <div id="userBehaviorResult"></div>
    </div>

    <div style="margin: 20px; padding: 20px; border: 1px solid #ccc; background-color: #f0f0f0;">
        <h2>已停用功能（仅兼容性测试）</h2>
        <p style="color: #666;">以下功能已停用，但保持API兼容性</p>
        <button onclick="testDisabledFeatures()">测试语音/问老师功能</button>
        <button onclick="testVipFeatures()">测试VIP相关功能</button>
        <div id="disabledFeaturesResult"></div>
        <div id="vipFeaturesResult"></div>
    </div>

    <script>
        // AJAX基础URL - 需要根据实际配置修改
        const ajaxUrl = '/?page_id=13794';
        
        function testSelectWord() {
            const word = document.getElementById('word').value;
            $.ajax({
                url: ajaxUrl,
                type: 'POST',
                data: {
                    action: 'selectWord',
                    word: word
                },
                success: function(data) {
                    document.getElementById('selectWordResult').innerHTML = 
                        '<strong>结果:</strong> ' + JSON.stringify(data);
                },
                error: function() {
                    document.getElementById('selectWordResult').innerHTML = 
                        '<strong style="color:red;">错误:</strong> AJAX请求失败';
                }
            });
        }
        
        function testAddWord() {
            const bookId = document.getElementById('bookId').value;
            const word = document.getElementById('wordToAdd').value;
            $.ajax({
                url: ajaxUrl,
                type: 'POST',
                data: {
                    action: 'addWord',
                    book_id: bookId,
                    wordori: word
                },
                success: function(data) {
                    let result = '';
                    switch(data) {
                        case '1': result = '添加成功'; break;
                        case '2': result = '单词已存在'; break;
                        case '0': result = '添加失败'; break;
                        default: result = '未知结果: ' + data;
                    }
                    document.getElementById('addWordResult').innerHTML = 
                        '<strong>结果:</strong> ' + result;
                },
                error: function() {
                    document.getElementById('addWordResult').innerHTML = 
                        '<strong style="color:red;">错误:</strong> AJAX请求失败';
                }
            });
        }
        
        function testSaveCourseProgress() {
            const userId = document.getElementById('userId').value;
            const postId = document.getElementById('postId').value;
            const maxPaged = document.getElementById('maxPaged').value;
            $.ajax({
                url: ajaxUrl,
                type: 'POST',
                data: {
                    action: 'save_course_rate_of_progress',
                    user_id: userId,
                    post_id: postId,
                    maxpaged: maxPaged
                },
                success: function(data) {
                    document.getElementById('saveProgressResult').innerHTML = 
                        '<strong>结果:</strong> ' + JSON.stringify(data);
                },
                error: function() {
                    document.getElementById('saveProgressResult').innerHTML = 
                        '<strong style="color:red;">错误:</strong> AJAX请求失败';
                }
            });
        }
        
        function testUserBehavior() {
            $.ajax({
                url: ajaxUrl,
                type: 'POST',
                data: {
                    action: 'user_listening_behavior',
                    user_id: '1',
                    post_id: '456',
                    maxpaged: '10',
                    entertime: Math.floor(Date.now() / 1000),
                    is_user_finish_the_course: '1',
                    stay_for_time: '300'
                },
                success: function(data) {
                    document.getElementById('userBehaviorResult').innerHTML =
                        '<strong>结果:</strong> ' + JSON.stringify(data);
                },
                error: function() {
                    document.getElementById('userBehaviorResult').innerHTML =
                        '<strong style="color:red;">错误:</strong> AJAX请求失败';
                }
            });
        }

        function testDisabledFeatures() {
            // 测试已停用的save_ask_teacher_text功能
            $.ajax({
                url: ajaxUrl,
                type: 'POST',
                data: {
                    action: 'save_ask_teacher_text',
                    user_id: '1',
                    post_id: '456',
                    ask_teacher_text: '测试问题'
                },
                success: function(data) {
                    let result = '<strong>save_ask_teacher_text:</strong> ' + data + '<br>';

                    // 测试已停用的speech_assessment功能
                    $.ajax({
                        url: ajaxUrl,
                        type: 'POST',
                        data: {
                            action: 'speech_assessment',
                            user_id: '1',
                            media_id: 'test123',
                            question_stem_1: 'hello world'
                        },
                        success: function(data2) {
                            result += '<strong>speech_assessment:</strong> ' + data2 + '<br>';

                            // 测试已停用的speech_assessment2功能
                            $.ajax({
                                url: ajaxUrl,
                                type: 'POST',
                                data: {
                                    action: 'speech_assessment2',
                                    user_id: '1',
                                    media_id: 'test123'
                                },
                                success: function(data3) {
                                    result += '<strong>speech_assessment2:</strong> ' + (data3 || '(空响应)');
                                    document.getElementById('disabledFeaturesResult').innerHTML = result;
                                }
                            });
                        }
                    });
                },
                error: function() {
                    document.getElementById('disabledFeaturesResult').innerHTML =
                        '<strong style="color:red;">错误:</strong> 已停用功能测试失败';
                }
            });
        }

        function testVipFeatures() {
            // 测试VIP相关的已停用功能
            $.ajax({
                url: ajaxUrl,
                type: 'POST',
                data: {
                    action: 'youzan_vip_go',
                    user_id: '1',
                    tid: 'test123'
                },
                success: function(data) {
                    let result = '<strong>youzan_vip_go:</strong> ' + data + '<br>';

                    // 测试暂停VIP功能
                    $.ajax({
                        url: ajaxUrl,
                        type: 'POST',
                        data: {
                            action: 'pause_vip',
                            user_id: '1'
                        },
                        success: function(data2) {
                            result += '<strong>pause_vip:</strong> ' + data2 + '<br>';

                            // 测试恢复VIP功能
                            $.ajax({
                                url: ajaxUrl,
                                type: 'POST',
                                data: {
                                    action: 'recover_vip',
                                    user_id: '1'
                                },
                                success: function(data3) {
                                    result += '<strong>recover_vip:</strong> ' + data3 + '<br>';

                                    // 测试朋友圈打卡功能
                                    $.ajax({
                                        url: ajaxUrl,
                                        type: 'POST',
                                        data: {
                                            action: 'sign_in_wechat_timeline',
                                            user_id: '1'
                                        },
                                        success: function(data4) {
                                            result += '<strong>sign_in_wechat_timeline:</strong> ' + data4 + '<br>';

                                            // 测试语音评分功能
                                            $.ajax({
                                                url: ajaxUrl,
                                                type: 'POST',
                                                data: {
                                                    action: 'giveAMark',
                                                    voiceId: 'test123'
                                                },
                                                success: function(data5) {
                                                    result += '<strong>giveAMark:</strong> ' + (data5 || '(空响应)');
                                                    document.getElementById('vipFeaturesResult').innerHTML = result;
                                                }
                                            });
                                        }
                                    });
                                }
                            });
                        }
                    });
                },
                error: function() {
                    document.getElementById('vipFeaturesResult').innerHTML =
                        '<strong style="color:red;">错误:</strong> VIP功能测试失败';
                }
            });
        }
    </script>
</body>
</html>
