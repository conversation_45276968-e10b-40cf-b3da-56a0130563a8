<?php
$themename = "html5game";
$shortname = "g";


$categories = get_categories('hide_empty=0&orderby=name');
/*get_categories()函数可以获得分类所有信息，返回与查询参数相匹配的类别对象数组
 *
 * hide_empty
 *(布尔值)触发显示没有文章的分类。默认值为true（隐藏空类别）。有效的值包括:1(true)和0(false)。
 *以上语句表示不隐藏
 *
 * orderby
 *(字符)将分类按字母顺序或独有分类编号进行排序。默认为按分类 编号排序包括ID(默认)和Name
 *
 *
 *
 *
 *
 * */


//定义一个$wp_cats数组
$wp_cats = array();

//将数组$categories每个单元数值赋给$category_list，并遍历数组
foreach ($categories as $category_list ) {
//将cat_ID作为下标，cat_name作为值赋给$wp_cats
	$wp_cats[$category_list->cat_ID] = $category_list->cat_name;
}

//定义一个数组，用法未知
$number_entries = array("Select a Number:","1","2","3","4","5","6","7","8","9","10", "12","14", "16", "18", "20" );

//定义一个二维数组$options
$options = array (
	array( "name" => $themename." Options","type" => "title"),

//基本信息设置
	array(  "name" => "基本配置", "type" => "section"),

	array("type" => "open"),


    array(	"name" => "首页标题（g_title）","desc" => "","id" => $shortname."_title","type" => "text","std" => ".."),

    array(	"name" => "服务号appid（g_weixin_appid）","desc" => "","id" => $shortname."_weixin_appid","type" => "text","std" => ".."),


    array(	"name" => "服务号appsecret（g_weixin_appsecret）","desc" => "","id" => $shortname."_weixin_appsecret","type" => "text","std" => ".."),


     array(	"name" => "支付号appid（g_weixin_pay_appid）","desc" => "","id" => $shortname."_weixin_pay_appid","type" => "text","std" => ".."),


    array(	"name" => "支付号appsecret（g_weixin_pay_appsecret）","desc" => "","id" => $shortname."_weixin_pay_appsecret","type" => "text","std" => ".."),

    array(	"name" => "老师微信二维码（g_teacher_wechat_qr）","desc" => "上传老师微信二维码图片","id" => $shortname."_teacher_wechat_qr","type" => "upload","std" => ""),

    array(	"name" => "老师微信号（g_teacher_wechat_id）","desc" => "老师的微信号，用于用户复制添加","id" => $shortname."_teacher_wechat_id","type" => "text","std" => "hamigua_6666"),

    array(	"name" => "微信验证信息（g_wechat_verify_message）","desc" => "添加微信时的验证信息","id" => $shortname."_wechat_verify_message","type" => "text","std" => "课程咨询"),

    array( "type" => "close"),

    array(  "name" => "有赞商城", "type" => "section"),


    array(	"name" => "开放平台client_id（g_youzan_client_id）","desc" => "","id" => $shortname."_youzan_client_id","type" => "text","std" => ".."),

    array(	"name" => "开放平台client_secret（g_youzan_client_secret）","desc" => "","id" => $shortname."_youzan_client_secret","type" => "text","std" => ".."),

    array(	"name" => "有赞云client_id（g_youzanyun_client_id）","desc" => "","id" => $shortname."_youzanyun_client_id","type" => "text","std" => ".."),

    array(	"name" => "有赞云client_secret（g_youzanyun_client_secret）","desc" => "","id" => $shortname."_youzanyun_client_secret","type" => "text","std" => ".."),

    array(	"name" => "有赞云access_token请求（g_youzanyun_get_access_token_url）","desc" => "","id" => $shortname."_youzanyun_get_access_token_url","type" => "text","std" => ".."),

    array(	"name" => "有赞云订单请求（g_youzanyun_get_trade_url）","desc" => "","id" => $shortname."_youzanyun_get_trade_url","type" => "text","std" => ".."),

    array(	"name" => "有赞云粉丝请求（g_youzanyun_get_follower_url）","desc" => "","id" => $shortname."_youzanyun_get_follower_url","type" => "text","std" => ".."),

    array(	"name" => "有赞云打标签请求（g_youzanyun_scrm_tag_relation_add_url）","desc" => "","id" => $shortname."_youzanyun_scrm_tag_relation_add_url","type" => "text","std" => ".."),

    array(	"name" => "店铺kdt_id（g_youzan_kdt_id）","desc" => "","id" => $shortname."_youzan_kdt_id","type" => "text","std" => ".."),


    array(	"name" => "店铺首页（g_youzan_shop_url）","desc" => "优化地址：".home_url()."/?page_id=54513","id" => $shortname."_youzan_shop_url","type" => "text","std" => ".."),


    array(	"name" => "主推商品页（g_youzan_main_push_product_url）","desc" => "优化地址：".home_url()."/?page_id=54516","id" => $shortname."_youzan_main_push_product_url","type" => "text","std" => ".."),


    array(	"name" => "主推活动页（g_youzan_main_push_special_offers_url）","desc" => "优化地址：".home_url()."/?page_id=55137","id" => $shortname."_youzan_main_push_special_offers_url","type" => "text","std" => ".."),















		//测试用户设置
  array( "type" => "close"),
    array( "name" => "测试用户设置", "type" => "section"),
    array( "type" => "open"),
    array(	"name" => "测试用户ID（g_test_user）", "desc" => "","id" => $shortname."_test_user", "type" => "text", "std" => ""),








		 //VIP标语
    array( "type" => "close"),
	array( "name" => "开通VIP文字广告",
		"type" => "section"),
	array( "type" => "open"),



			array("name" => "VIP活动标语（g_vipaction_text）",
		"desc" => "",
		"id" => $shortname."_vipaction_text",
		"type" => "text",
		"std" => '..'),

		array("name" => "链接地址（g_vipaction_single_pro_url）",
		"desc" => "",
		"id" => $shortname."_vipaction_single_pro_url",
		"type" => "text",
		"std" => '..'),




			 //微信模板消息
    array( "type" => "close"),
	array( "name" => "微信模板消息",
		"type" => "section"),
	array( "type" => "open"),



		array("name" => "模板ID（g_wechat_template_mes_id）",
		"desc" => "",
		"id" => $shortname."_wechat_template_mes_id",
		"type" => "text",
		"std" => '..'),

		array("name" => "链接地址（g_wechat_template_mes_link）",
		"desc" => "",
		"id" => $shortname."_wechat_template_mes_link",
		"type" => "text",
		"std" => '..'),

		array("name" => "first.DATA（g_wechat_template_mes_first_data）",
		"desc" => "",
		"id" => $shortname."_wechat_template_mes_first_data",
		"type" => "text",
		"std" => '..'),

			array("name" => "keyword1.DATA（g_wechat_template_mes_keyword1_data）",
		"desc" => "",
		"id" => $shortname."_wechat_template_mes_keyword1_data",
		"type" => "text",
		"std" => '..'),

			array("name" => "keyword2.DATA（g_wechat_template_mes_keyword2_data）",
		"desc" => "",
		"id" => $shortname."_wechat_template_mes_keyword2_data",
		"type" => "text",
		"std" => '..'),


		array("name" => "remark.DATA（g_wechat_template_mes_remark_data）",
		"desc" => "",
		"id" => $shortname."_wechat_template_mes_remark_data",
		"type" => "text",
		"std" => '..'),

		array("name" => "测试用户ID（g_wechat_template_mes_test_id）",
		"desc" => "",
		"id" => $shortname."_wechat_template_mes_test_id",
		"type" => "text",
		"std" => '..'),


		array("name" => "测试链接（g_wechat_template_mes_single_test_url）",
		"desc" => "",
		"id" => $shortname."_wechat_template_mes_single_test_url",
		"type" => "atext",
		"std" => '..'),

		array("name" => "群发链接（g_wechat_template_mes_mass_send_url）",
		"desc" => "",
		"id" => $shortname."_wechat_template_mes_mass_send_url",
		"type" => "atext",
		"std" => '..'),

		array("name" => "清除链接（g_wechat_template_mes_clear_sign_url）",
		"desc" => "",
		"id" => $shortname."_wechat_template_mes_clear_sign_url",
		"type" => "atext",
		"std" => '..'),








		/*
		 *
		 *
		 * 课程：{{keyword1.DATA}}
参加人：{{keyword2.DATA}}
{{remark.DATA}}
		 *
		 * */









			 //通讯地址
    array( "type" => "close"),
	array( "name" => "课程通讯地址",
		"type" => "section"),
	array( "type" => "open"),

	array("name" => "课程信息通讯地址（g_course_ajax_url）",
		"desc" => "",
		"id" => $shortname."_course_ajax_url",
		"type" => "text",
		"std" => '..'),

			array("name" => "记忆系统通讯地址（g_course_review_ajax_url）",
		"desc" => "",
		"id" => $shortname."_course_review_ajax_url",
		"type" => "text",
		"std" => '..'),











			 //黑卡会员相关
    array( "type" => "close"),
	array( "name" => "黑卡会员相关",
		"type" => "section"),
	array( "type" => "open"),



		array("name" => "会员最高返现次数（g_hlyyin_most_cash_back_times）",
		"desc" => "",
		"id" => $shortname."_hlyyin_most_cash_back_times",
		"type" => "text",
		"std" => '..'),


		array(  "name" => "开启返现?（g_return_cash_is_open）",
		"desc" => "默认【关闭open】",
		"id" => $shortname."_return_cash_is_open",
		"type" => "select",
		"std" => "open",
		"options" => array("close", "open")),



			array("name" => "黑卡任务页（g_heika_task_page）",
		"desc" => "",
		"id" => $shortname."_heika_task_page",
		"type" => "text",
		"std" => '..'),

		array("name" => "约定学习天数（g_appoint_cash_back_days）",
		"desc" => "",
		"id" => $shortname."_appoint_cash_back_days",
		"type" => "text",
		"std" => '..'),

		// VIP引导和营销相关配置
		array("name" => "VIP引导标语（g_heika_vip_slogan）",
		"desc" => "用于各个页面引导用户点击开通VIP按钮",
		"id" => $shortname."_heika_vip_slogan",
		"type" => "text",
		"std" => '开通黑卡VIP，立享更多学习特权'),

		array("name" => "营销页标语1（g_heika_vip_slogan_lp_1）",
		"desc" => "用于VIP营销页面的标语（建议7-10字）",
		"id" => $shortname."_heika_vip_slogan_lp_1",
		"type" => "text",
		"std" => '轻松提升听说读写'),

		array("name" => "营销页标语2（g_heika_vip_slogan_lp_2）",
		"desc" => "用于VIP营销页面的另一条标语（建议7-10字）",
		"id" => $shortname."_heika_vip_slogan_lp_2",
		"type" => "text",
		"std" => '不再害怕开口说'),

		array("name" => "新用户赠送VIP天数（g_new_user_vip_days）",
		"desc" => "新用户注册时自动赠送的黑卡VIP天数",
		"id" => $shortname."_new_user_vip_days",
		"type" => "text",
		"std" => '5'),

		array("name" => "赠送VIP告知话语（g_heika_vip_gift_message）",
		"desc" => "赠送VIP后显示给用户的消息，使用{days}作为天数的占位符",
		"id" => $shortname."_heika_vip_gift_message",
		"type" => "text",
		"std" => '恭喜您获得{days}天黑卡VIP会员，请尽情享用吧！'),

		//黑卡会员群
  array( "type" => "close"),
    array( "name" => "黑卡会员群", "type" => "section"),
    array( "type" => "open"),


   array("name" => "短期群活码页（g_short_time_group_qrcode）",
		"desc" => "",
		"id" => $shortname."_short_time_group_qrcode",
		"type" => "text",
		"std" => '..'),


    	array("name" => "有限群活码页（g_long_time_group_qrcode）",
		"desc" => "",
		"id" => $shortname."_long_time_group_qrcode",
		"type" => "text",
		"std" => '..'),


		array("name" => "无限群活码页（g_forever_group_qrcode）",
		"desc" => "",
		"id" => $shortname."_forever_group_qrcode",
		"type" => "text",
		"std" => '..'),

















		    //用户相关
    array( "type" => "close"),
	array( "name" => "用户相关",
		"type" => "section"),
	array( "type" => "open"),
    array(	"name" => "每日复习课数（g_review_num）", "desc" => "","id" => $shortname."_review_num", "type" => "text", "std" => ""),


    	array("name" => "今日复习页（g_today_review_page）",
		"desc" => "",
		"id" => $shortname."_today_review_page",
		"type" => "text",
		"std" => '..'),


    	array("name" => "我的成就页（g_my_achieve_page）",
		"desc" => "",
		"id" => $shortname."_my_achieve_page",
		"type" => "text",
		"std" => '..'),


		array("name" => "设置页（g_setting_page）",
		"desc" => "",
		"id" => $shortname."_setting_page",
		"type" => "text",
		"std" => '..'),

    array(	"name" => "综合抽查失败基数（废弃更新）", "desc" => "","id" => $shortname."_myTest_fail_num", "type" => "text", "std" => ""),






	);



			function mytheme_add_admin() {
				global $themename, $shortname, $options;
				if ( isset($_GET['page']) && $_GET['page'] == basename(__FILE__) ) {
					if ( 'save' == $_REQUEST['action'] ) {
						foreach ($options as $value) {
							update_option( $value['id'], $_REQUEST[ $value['id'] ] );
						}
						foreach ($options as $value) {
							if( isset( $_REQUEST[ $value['id'] ] ) ) {
							    update_option( $value['id'], $_REQUEST[ $value['id'] ]  );
							} else {
							    delete_option( $value['id'] );
							}}
							header("Location: admin.php?page=theme_options.php&saved=true");
							die;
						}
							else if( 'reset' == $_REQUEST['action'] ) {
								foreach ($options as $value) {
									delete_option( $value['id'] ); }
									header("Location: admin.php?page=theme_options.php&reset=true");
									die;
								}
							}
							add_theme_page($themename."主题设置", $themename."主题设置", 'edit_themes', basename(__FILE__), 'mytheme_admin');
						}

			function mytheme_add_init() {
				$file_dir=get_bloginfo('template_directory');
				wp_enqueue_style("functions", $file_dir."/includes/options/options.css", false, "1.0", "all");
				wp_enqueue_script("rm_script", $file_dir."/includes/options/rm_script.js", false, "1.0");

				// 加载WordPress媒体库脚本，用于图片上传功能
				wp_enqueue_media();
			}


function mytheme_admin() {
	global $themename, $shortname, $options;
	$i=0;
	if ( $_REQUEST['saved'] ) echo '<div id="message" class="updated fade"><p><strong>'.$themename.'主题设置已保存</strong></p></div>';
	if ( $_REQUEST['reset'] ) echo '<div id="message" class="updated fade"><p><strong>'.$themename.'主题已重新设置</strong></p></div>';

	?>


    <div class="wrap rm_wrap">
		<div id="icon-themes" class="icon32"><br></div>
		<h2><?php echo $themename; ?>主题设置</h2>
		<p>当前使用主题: <?php echo $themename; ?> |设计者: WPmonster</p>


        <?php
		function show_category() {
			/*WordPress包含一个操作数据库的类——wpdb
			 * WordPress定义了$wpdb的全局变量，所以请直接调用该全局变量$wpdb的实例来操作数据库
			 *
			 *
			 * */
				global $wpdb;
				$request = "SELECT $wpdb->terms.term_id, name FROM $wpdb->terms ";
				$request .= " LEFT JOIN $wpdb->term_taxonomy ON $wpdb->term_taxonomy.term_id = $wpdb->terms.term_id ";
				$request .= " WHERE $wpdb->term_taxonomy.taxonomy = 'category' ";
				$request .= " ORDER BY term_id asc";
				$categorys = $wpdb->get_results($request);
				foreach ($categorys as $category) { //调用菜单
				$output = '<span>'.$category->name."(<em>".$category->term_id.'</em>)</span>';
					echo $output;
                }
                }//栏目列表结束
?>


<!--<div id="all_cat">
	<h4>站点所有分类ID:</h4>
	<?php /*show_category(); */?>
	<br/>
	<small>注: 这些分类ID将在下面的【布局样式设置】中用到。</small>
</div>-->

<style>
.rm_wrap {
    width: 800px;
}
.rm_opts label {
    font-size: 12px;
    width: 350px;
    display: block;
    float: left;
}
</style>

<div class="rm_opts">
	<form method="post">
		<?php
		foreach ($options as $value) {
			switch ( $value['type'] ) {
				case "open":
		?>

		<?php break;?>

		<?php case "close":?>

</div>
		</div>
		<br />
		<?php break; case "title": ?>

		<?php break;?>

		<!--文本-->

		<?php case 'text': ?>
		<div class="rm_input rm_text">
			<label for="<?php echo $value['id']; ?>"><?php echo $value['name']; ?></label>
			<input name="<?php echo $value['id']; ?>" id="<?php echo $value['id']; ?>" type="<?php echo $value['type']; ?>" value="<?php if ( get_settings( $value['id'] ) != "") { echo stripslashes(get_settings( $value['id'])  ); } else { echo $value['std']; } ?>" />
			<small><?php echo $value['desc']; ?></small><div class="clearfix"></div>
		</div>
		<?php break;?>
		<!--文本-->


		<!--选项-->
		<?php case 'select': ?>
		<div class="rm_input rm_select">
			<label for="<?php echo $value['id']; ?>"><?php echo $value['name']; ?></label>
			<select name="<?php echo $value['id']; ?>" id="<?php echo $value['id']; ?>">
				<?php foreach ($value['options'] as $option) { ?>
				<option <?php if (get_settings( $value['id'] ) == $option) { echo 'selected="selected"'; } ?>><?php echo $option; ?></option><?php } ?>
			</select>
			<small><?php echo $value['desc']; ?></small><div class="clearfix"></div>
		</div>
		<?php break;?>
		<!--选项-->


		<!--按钮-->
		<?php case 'atext': ?>


			<div class="rm_input rm_text">
			<label for="<?php echo $value['id']; ?>">
			<span><?php echo $value['name']; ?></span>
			<a href="<?php if ( get_option( $value['id'] ) != "") { echo stripslashes(get_option( $value['id'])  ); } else { echo $value['std']; } ?>" target="_blank">访问链接</a>
			</label>
			<input name="<?php echo $value['id']; ?>" id="<?php echo $value['id']; ?>" type="text" value="<?php if ( get_option( $value['id'] ) != "") { echo stripslashes(get_option( $value['id'])  ); } else { echo $value['std']; } ?>" />
			<small><?php echo $value['desc']; ?></small>

			<div class="clearfix"></div>
		    </div>


		<?php break;?>
		<!--按钮-->

		<?php case "checkbox": ?>
		<div class="rm_input rm_checkbox">
			<label for="<?php echo $value['id']; ?>"><?php echo $value['name']; ?></label>
			<?php if(get_option($value['id'])){ $checked = "checked=\"checked\""; }else{ $checked = "";} ?>
			<input type="checkbox" name="<?php echo $value['id']; ?>" id="<?php echo $value['id']; ?>" value="true" <?php echo $checked; ?> />
			<small><?php echo $value['desc']; ?></small><div class="clearfix"></div>
		</div>

		<?php break;?>

		<!--图片上传-->
		<?php case 'upload': ?>
		<div class="rm_input rm_upload">
			<label for="<?php echo $value['id']; ?>"><?php echo $value['name']; ?></label>
			<input name="<?php echo $value['id']; ?>" id="<?php echo $value['id']; ?>" type="text" value="<?php if ( get_option( $value['id'] ) != "") { echo stripslashes(get_option( $value['id'])  ); } else { echo $value['std']; } ?>" />
			<input id="<?php echo $value['id']; ?>_button" class="upload_image_button" type="button" value="上传图片" />
			<br/>
			<?php if ( get_option( $value['id'] ) != "") { ?>
			<img src="<?php echo stripslashes(get_option( $value['id'] )); ?>" style="max-width: 200px; margin-top: 10px;" />
			<?php } ?>
			<small><?php echo $value['desc']; ?></small><div class="clearfix"></div>
		</div>
		<?php break;?>
		<!--图片上传-->

		<?php case "section":?>
		<?php $i++;?>
		<div class="rm_section">
			<div class="rm_title">
			<h3>
			<img src="<?php bloginfo('template_directory')?>/includes/options/clear.png" class="inactive" alt="""><?php echo $value['name']; ?>
			</h3>
			<span class="submit">
			<input name="save<?php echo $i; ?>" type="submit" value="保存设置" />
			</span>
			<div class="clearfix"></div>
        </div>
			<div class="rm_options">
				<?php break; ?>
				<?php } ?>
				<?php } ?>
				<input type="hidden" name="action" value="save" />
			</form>

			<script type="text/javascript">
			jQuery(document).ready(function($) {
				// 图片上传功能
				$('.upload_image_button').click(function() {
					var send_attachment_bkp = wp.media.editor.send.attachment;
					var button = $(this);
					var input_field = button.prev('input[type="text"]');

					wp.media.editor.send.attachment = function(props, attachment) {
						input_field.val(attachment.url);
						// 显示预览图片
						var preview = input_field.parent().find('img');
						if (preview.length) {
							preview.attr('src', attachment.url);
						} else {
							input_field.after('<br/><img src="' + attachment.url + '" style="max-width: 200px; margin-top: 10px;" />');
						}
						wp.media.editor.send.attachment = send_attachment_bkp;
					}

					wp.media.editor.open(button);
					return false;
				});
			});
			</script>

		   </div>


<?php } ?>

<?php
		function mytheme_wp_head() {
			$stylesheet = get_option('h_alt_stylesheet');
			if($stylesheet != ''){?>
			<link href="<?php bloginfo('template_directory'); ?>/styles/<?php echo $stylesheet; ?>" rel="stylesheet" type="text/css" />
			<?php }
		}
		add_action('wp_head', 'mytheme_wp_head');
		add_action('admin_init', 'mytheme_add_init');
		add_action('admin_menu', 'mytheme_add_admin');
?>