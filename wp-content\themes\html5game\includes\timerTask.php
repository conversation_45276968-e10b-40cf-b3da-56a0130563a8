<?php

function cron_add_tenminutely($schedules)
{
    // 10分钟一次定时任务
    $schedules['tenminutely'] = array(
        'interval' => 600, // 1周 = 60秒 * 60分钟 * 24小时 * 7天
        'display' => __('十分钟一次')
    );
    return $schedules;
}

add_filter('cron_schedules', 'cron_add_tenminutely');

function cron_add_halfhourly($schedules)
{
    // 半小时一次
    $schedules['halfhourly'] = array(
        'interval' => 1800, // 1周 = 60秒 * 60分钟 * 24小时 * 7天
        'display' => __('半小时一次')
    );
    return $schedules;
}

add_filter('cron_schedules', 'cron_add_halfhourly');

/*
 * 定时获取微信access_token
 *
 * */


if (!wp_next_scheduled('get_weixin_access_token')) {
    wp_schedule_event(current_time( 'timestamp' ), 'tenminutely', 'get_weixin_access_token');
}

add_action('get_weixin_access_token', 'get_weixin_access_token');


/*
 *
 * 保存到数据库main_wechat_access_token
 *
 * */



/**
 * @功能概述: 定时获取微信服务号access_token
 *           从微信API获取新的access_token并更新到数据库
 *           确保微信JS-SDK和其他微信功能正常运行
 *
 * @执行流程:
 *   1. 获取微信服务号配置信息
 *   2. 构造微信API请求URL
 *   3. 发送请求获取新的access_token
 *   4. 验证响应并更新数据库
 *   5. 记录获取时间用于调试
 */
function get_weixin_access_token()
{
    $log_prefix = '[TimerTask-AccessToken] ';
    error_log($log_prefix . '[开始] 定时获取微信access_token任务开始');

    // 步骤 1: 获取微信服务号配置
    $appid = get_option("g_weixin_appid");
    $appsecret = get_option("g_weixin_appsecret");

    if (empty($appid) || empty($appsecret)) {
        error_log($log_prefix . '[错误] 微信配置缺失 - AppID: ' . $appid . ', AppSecret: ' . (empty($appsecret) ? '空' : '已配置'));
        return;
    }

    error_log($log_prefix . '[步骤 1] 微信配置获取完成 - AppID: ' . $appid);

    // 步骤 2: 构造微信API请求URL
    $grant_type = 'client_credential';
    $url = 'https://api.weixin.qq.com/cgi-bin/token?grant_type=' . $grant_type . '&appid=' . $appid . '&secret=' . $appsecret;
    error_log($log_prefix . '[步骤 2] 构造API请求URL完成');

    // 步骤 3: 发送请求获取access_token
    $get_the_json = file_get_contents($url);

    if ($get_the_json === false) {
        error_log($log_prefix . '[错误] 无法获取微信API响应，请检查网络连接');
        return;
    }

    error_log($log_prefix . '[步骤 3] 微信API响应: ' . $get_the_json);

    // 步骤 4: 解析响应并验证
    $obj = json_decode($get_the_json);

    if (!empty($obj->errcode)) {
        error_log($log_prefix . '[错误] 微信API返回错误 - 错误代码: ' . $obj->errcode . ', 错误信息: ' . $obj->errmsg);
        return;
    }

    if (empty($obj->access_token)) {
        error_log($log_prefix . '[错误] 微信API响应中未包含access_token');
        return;
    }

    $access_token = $obj->access_token;
    error_log($log_prefix . '[步骤 4] access_token获取成功: ' . substr($access_token, 0, 20) . '...');

    // 步骤 5: 更新数据库
    if (!get_option("main_wechat_access_token")) {
        add_option("main_wechat_access_token", $access_token);
        error_log($log_prefix . '[步骤 5] 新增access_token到数据库');
    } else {
        update_option('main_wechat_access_token', $access_token);
        error_log($log_prefix . '[步骤 5] 更新数据库中的access_token');
    }

    // 步骤 6: 记录获取时间
    $current_datetime = current_time('mysql');
    update_option("main_wechat_access_token_get_time", $current_datetime);
    error_log($log_prefix . '[步骤 6] 记录获取时间: ' . $current_datetime);

    error_log($log_prefix . '[完成] 微信access_token定时获取任务完成');
}





?>