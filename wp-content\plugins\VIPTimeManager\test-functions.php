<?php
/**
 * VIPTimeManager函数冲突测试文件
 * 
 * 这个文件用于测试插件中的函数是否与主题中的函数存在冲突
 * 如果没有冲突，这个文件应该能够正常加载而不产生错误
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

// 记录测试开始
error_log('[VIPTimeManager-Test] 开始测试函数冲突');

// 测试所有插件函数是否存在
$functions_to_test = [
    'vip_manager_is_valid_json',
    'vip_manager_select_user_vip_time', 
    'vip_manager_increase_heika_vip_time',
    'vip_manager_delete_heika'
];

foreach ($functions_to_test as $function_name) {
    if (function_exists($function_name)) {
        error_log('[VIPTimeManager-Test] ✅ 函数存在: ' . $function_name);
    } else {
        error_log('[VIPTimeManager-Test] ❌ 函数不存在: ' . $function_name);
    }
}

// 测试AJAX action是否注册
$ajax_actions = [
    'selectUserVipTime',
    'increase_heika_vip_time', 
    'delete_heika'
];

foreach ($ajax_actions as $action) {
    if (has_action('wp_ajax_' . $action)) {
        error_log('[VIPTimeManager-Test] ✅ AJAX action已注册: wp_ajax_' . $action);
    } else {
        error_log('[VIPTimeManager-Test] ❌ AJAX action未注册: wp_ajax_' . $action);
    }
}

// 测试VIP数据结构兼容性
$test_user_id = 1; // 使用管理员用户进行测试
if ($test_user_id > 0) {
    $heika_json = get_user_meta($test_user_id, "heika_vip", true);
    if (!empty($heika_json)) {
        $heika_array = json_decode($heika_json, true);
        if (!empty($heika_array) && is_array($heika_array)) {
            foreach ($heika_array as $index => $heika_data) {
                $has_big_course_type = isset($heika_data['big_course_type']);
                $big_course_type_value = $has_big_course_type ? $heika_data['big_course_type'] : '未设置';
                error_log('[VIPTimeManager-Test] VIP卡' . ($index + 1) . ' - big_course_type: ' . $big_course_type_value);

                if ($has_big_course_type && $heika_data['big_course_type'] === 'hlyyin') {
                    error_log('[VIPTimeManager-Test] ✅ VIP卡' . ($index + 1) . ' 兼容前端系统');
                } else {
                    error_log('[VIPTimeManager-Test] ⚠️ VIP卡' . ($index + 1) . ' 需要兼容性处理');
                }
            }
        }
    } else {
        error_log('[VIPTimeManager-Test] 用户' . $test_user_id . '无VIP数据');
    }
}

error_log('[VIPTimeManager-Test] 测试完成，如果没有冲突错误，说明修复成功');

?>
