<?php
/*
Plugin Name: VIPTimeManager
Plugin URI: https://www.shuimitao.online/
Description: 后台增减用户黑卡VIP时间，兼容前端系统VIP判断逻辑
Version: 1.1.0
Author: showlin
Author URI: https://www.shuimitao.online/
License: GPL

更新日志：
v1.1.0 - 2025-07-09
- 修复VIP数据结构兼容性问题
- 添加big_course_type字段支持（值为"hlyyin"）
- 添加向后兼容处理，自动补全缺失字段
- 优化VIP查询逻辑，确保与前端系统一致
- 增强日志记录，便于调试和监控
*/

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

// 记录插件加载
error_log('[VIPTimeManager] 插件开始加载 - 版本 1.1.0 - 兼容前端系统');

// 加载AJAX处理函数
// 迁移说明：原本在主题中的AJAX处理函数现在集成到插件内部
// 这样确保功能独立于主题，提高代码的可维护性和稳定性
require_once plugin_dir_path(__FILE__) . 'includes/ajax-handlers.php';
error_log('[VIPTimeManager] AJAX处理器文件已加载');

// 加载测试文件（仅在调试模式下）
if (defined('WP_DEBUG') && WP_DEBUG) {
    require_once plugin_dir_path(__FILE__) . 'test-functions.php';
    error_log('[VIPTimeManager] 测试文件已加载');
}

add_action('admin_menu', 'set_VIPTimeManager_menu');

function set_VIPTimeManager_menu() {
    error_log('[VIPTimeManager] 注册管理菜单');
    add_options_page('用户VIP时间管理', '用户VIP时间管理', 'administrator', __FILE__, 'VIPTimeManager_setting_page');
}

function VIPTimeManager_setting_page() {
    error_log('[VIPTimeManager] 加载设置页面');
    include('includes/page.php');
}

error_log('[VIPTimeManager] 插件加载完成');

?>
