<?php
/**
 * LearningReviewDebugger函数冲突测试文件
 * 
 * 这个文件用于测试插件中的函数是否与主题中的函数存在冲突
 * 如果没有冲突，这个文件应该能够正常加载而不产生错误
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

// 记录测试开始
error_log('[LearningReviewDebugger-Test] 开始测试函数冲突');

// 测试所有插件函数是否存在
$functions_to_test = [
    'lrd_admin_review_get_user_data',
    'lrd_admin_record_category_visit',
    'lrd_admin_reset_category_visit',
    'lrd_admin_review_get_user_review_mes',
    'lrd_reset_alter_review_time',
    'lrd_reset_alter_heika_data_time',
    'lrd_reset_current_user_learn_detail',
    'lrd_reset_current_user_today_learn_detail',
    'lrd_reset_current_user_heika_check_in_detail',
    'lrd_reset_current_user_heika_daily_learning_task_content',
    'lrd_reset_current_user_heika_daily_learning_task_content_total',
    'lrd_admin_review_clear_review_mes',
    'lrd_get_current_timestamp',
    'lrd_admin_review_clean_user_all_data',
    'lrd_admin_reset_user_review_mes_and_course_rate_of_progress'
];

foreach ($functions_to_test as $function_name) {
    if (function_exists($function_name)) {
        error_log('[LearningReviewDebugger-Test] ✅ 函数存在: ' . $function_name);
    } else {
        error_log('[LearningReviewDebugger-Test] ❌ 函数不存在: ' . $function_name);
    }
}

// 测试AJAX action是否注册
$ajax_actions = [
    'admin_review_get_user_data',
    'admin_record_category_visit',
    'admin_reset_category_visit',
    'admin_review_get_user_review_mes',
    'reset_alter_review_time',
    'reset_alter_heika_data_time',
    'reset_current_user_learn_detail',
    'reset_current_user_today_learn_detail',
    'reset_current_user_heika_check_in_detail',
    'reset_current_user_heika_daily_learning_task_content',
    'reset_current_user_heika_daily_learning_task_content_total',
    'admin_review_clear_review_mes',
    'get_current_timestamp',
    'admin_review_clean_user_all_data',
    'admin_reset_user_review_mes_and_course_rate_of_progress'
];

foreach ($ajax_actions as $action) {
    if (has_action('wp_ajax_' . $action)) {
        error_log('[LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_' . $action);
    } else {
        error_log('[LearningReviewDebugger-Test] ❌ AJAX action未注册: wp_ajax_' . $action);
    }
}

// 测试重构完整性
$migration_status = array(
    'functions_migrated' => count($functions_to_test),
    'ajax_actions_preserved' => count($ajax_actions),
    'prefix_applied' => 'lrd_',
    'theme_independence' => true
);

error_log('[LearningReviewDebugger-Test] 重构状态: ' . json_encode($migration_status));

// 检查是否有原主题函数残留（这些应该不存在了）
$old_theme_functions = [
    'admin_review_get_user_data',
    'admin_record_category_visit',
    'reset_alter_review_time'
];

$conflicts_found = 0;
foreach ($old_theme_functions as $old_function) {
    if (function_exists($old_function)) {
        error_log('[LearningReviewDebugger-Test] ⚠️ 检测到可能的函数冲突: ' . $old_function);
        $conflicts_found++;
    }
}

if ($conflicts_found === 0) {
    error_log('[LearningReviewDebugger-Test] ✅ 未检测到函数冲突');
} else {
    error_log('[LearningReviewDebugger-Test] ⚠️ 检测到 ' . $conflicts_found . ' 个潜在冲突');
}

error_log('[LearningReviewDebugger-Test] 测试完成，如果没有冲突错误，说明重构成功');

?>
