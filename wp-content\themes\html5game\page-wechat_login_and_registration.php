<?php
/**
 * @功能概述: 微信网页授权登录和注册处理页面
 *           处理微信OAuth2.0授权回调，实现用户自动注册和登录功能
 *           支持已关注和未关注用户的不同处理流程
 *
 * Template Name: wechat_login_and_registration
 */

$log_prefix = '[WechatLoginRegistration] ';
error_log($log_prefix . '[开始] 微信登录注册页面处理开始');

/**
 * @分步说明: 参数验证与初始化
 *
 *   1. 验证微信授权必需参数
 *       1.1. 检查code参数是否存在（微信授权码）
 *       1.2. 检查state参数是否存在（状态参数）
 *       1.3. 参数缺失时清理会话并跳转首页
 */

// 步骤 1.1-1.2: 验证微信授权必需参数
if (empty($_GET['code']) || empty($_GET['state'])) {
    error_log($log_prefix . '[步骤 1.1-1.2] 参数验证失败 - code: ' . ($_GET['code'] ?? 'null') . ', state: ' . ($_GET['state'] ?? 'null'));

    // 步骤 1.3: 清理会话并跳转首页
    session_start();
    session_unset();
    error_log($log_prefix . '[步骤 1.3] 参数缺失，清理会话并跳转首页');
    header('Location:' . home_url());
    exit();
}

error_log($log_prefix . '[步骤 1] 参数验证通过 - code: ' . $_GET['code'] . ', state: ' . $_GET['state']);

// 步骤 2: 解析授权参数和配置信息
$state = $_GET['state']; // 状态参数，格式："页面类型|页面ID|来源渠道"
$web_page_type = ""; // 需跳转的页面类型
$web_page_id = 0; // 需跳转的页面ID
$source_of_registered_users = ""; // 注册用户来源标识
$weburl = home_url(); // 当前网站域名，使用动态获取确保域名正确
$wechat_interface_access_token = get_option("main_wechat_access_token"); // 微信服务号接口访问令牌

error_log($log_prefix . '[步骤 2] 基础配置获取完成 - 网站域名: ' . $weburl . ', state: ' . $state);

/**
 * @分步说明: state参数解析
 *
 *   2.1. 检查state参数格式
 *       2.1.1. 如果不是"home"，则按"|"分割解析
 *       2.1.2. 提取页面类型、页面ID和用户来源信息
 *       2.1.3. 记录解析结果用于后续跳转
 */
if ($state != "home") {
    // 步骤 2.1.1: 按分隔符解析state参数
    $stateArr = explode('|', $state);

    // 步骤 2.1.2: 提取各个组成部分
    $web_page_type = $stateArr[0]; // 页面类型：post/page/cat
    $web_page_id = $stateArr[1]; // 页面ID
    $source_of_registered_users = $stateArr[2]; // 用户来源渠道

    // 步骤 2.1.3: 记录解析结果
    error_log($log_prefix . '[步骤 2.1] state参数解析完成 - 页面类型: ' . $web_page_type . ', 页面ID: ' . $web_page_id . ', 用户来源: ' . $source_of_registered_users);
} else {
    error_log($log_prefix . '[步骤 2.1] 检测到首页访问，无需解析state参数');
}

// 步骤 3: 获取微信授权码和配置信息
$service_appid = get_option("g_weixin_appid"); // 微信服务号AppID
$service_appsecret = get_option("g_weixin_appsecret"); // 微信服务号AppSecret
$code = $_GET['code']; // 微信授权码，用于换取access_token

error_log($log_prefix . '[步骤 3] 微信配置获取完成 - AppID: ' . $service_appid . ', code: ' . $code);

/**
 * @分步说明: 通过授权码获取access_token
 *
 *   3.1. 构造微信OAuth2.0 access_token获取API请求
 *       3.1.1. 使用标准的微信网页授权access_token接口
 *       3.1.2. 包含必需参数：appid、secret、code、grant_type
 *   3.2. 发送API请求并解析响应
 *       3.2.1. 调用微信API获取access_token
 *       3.2.2. 解析JSON响应为数组格式
 */

// 步骤 3.1: 构造获取access_token的API请求URL
$url = 'https://api.weixin.qq.com/sns/oauth2/access_token?appid=' . $service_appid . '&secret=' . $service_appsecret . '&code=' . $code . '&grant_type=authorization_code';
error_log($log_prefix . '[步骤 3.1] 构造access_token请求URL: ' . $url);

// 步骤 3.2: 发送API请求并解析响应
$res = file_get_contents($url);
$get_access_token_arr = json_decode($res, true); // 解析JSON响应为数组

error_log($log_prefix . '[步骤 3.2] access_token API响应: ' . $res);



/**
 * @分步说明: 处理access_token响应并进入用户登录注册流程
 *
 *   4. 验证access_token响应是否成功
 *       4.1. 检查响应中是否包含错误代码
 *       4.2. 如果成功，进入用户登录或注册场景
 *   5. 刷新access_token以确保有效性
 *       5.1. 提取refresh_token用于刷新
 *       5.2. 构造刷新token的API请求
 *       5.3. 获取新的有效access_token
 */

// 步骤 4: 验证access_token获取是否成功
if (!isset($get_access_token_arr['errcode'])) {
    error_log($log_prefix . '[步骤 4] access_token获取成功，无错误代码，开始用户登录注册流程');

    // 步骤 5.1: 提取refresh_token
    $refresh_token = $get_access_token_arr['refresh_token']; // 用于刷新access_token的refresh_token
    error_log($log_prefix . '[步骤 5.1] 提取refresh_token: ' . $refresh_token);

    // 步骤 5.2: 构造刷新access_token的API请求URL
    $refresh_access_token_url = "https://api.weixin.qq.com/sns/oauth2/refresh_token?appid=";
    $refresh_access_token_url .= $service_appid . "&grant_type=refresh_token&refresh_token=" . $refresh_token;
    error_log($log_prefix . '[步骤 5.2] 构造refresh_token请求URL: ' . $refresh_access_token_url);

    // 步骤 5.3: 发送刷新请求并获取新的access_token
    $refresh_access_token_obj = json_decode(file_get_contents($refresh_access_token_url));
    error_log($log_prefix . '[步骤 5.3] refresh_token API响应: ' . json_encode($refresh_access_token_obj));



    /**
     * @分步说明: 获取用户详细信息
     *
     *   6. 验证refresh_token响应并获取用户信息
     *       6.1. 检查refresh_token是否成功获取
     *       6.2. 使用服务号access_token和openid获取用户详细信息
     *       6.3. 调用微信用户信息接口获取用户资料
     */

    // 步骤 6.1: 验证refresh_token响应是否成功
    if (!isset($refresh_access_token_obj->errcode)) {
        error_log($log_prefix . '[步骤 6.1] refresh_token成功，openid: ' . $refresh_access_token_obj->openid);

        // 步骤 6.2: 构造获取用户信息的API请求URL
        // 注意：这里应该使用OAuth2.0获取的用户access_token，而不是服务号access_token
        $get_user_info_url = "https://api.weixin.qq.com/sns/userinfo?access_token=";
        $get_user_info_url .= $refresh_access_token_obj->access_token . "&openid=" . $refresh_access_token_obj->openid . "&lang=zh_CN";
        error_log($log_prefix . '[步骤 6.2] 构造用户信息请求URL: ' . $get_user_info_url);


        // 步骤 6.3: 发送用户信息请求并解析响应
        $user_info_obj = json_decode(file_get_contents($get_user_info_url));

        // 安全检查：确保属性存在再访问
        $nickname = isset($user_info_obj->nickname) ? $user_info_obj->nickname : '未知用户';
        $subscribe = isset($user_info_obj->subscribe) ? $user_info_obj->subscribe : 0;

        error_log($log_prefix . '[步骤 6.3] 用户信息获取完成，用户昵称: ' . $nickname . ', 关注状态: ' . $subscribe);
        error_log($log_prefix . '[步骤 6.3] 用户信息API完整响应: ' . json_encode($user_info_obj));

        /**
         * @分步说明: 用户关注状态检查和处理
         *
         *   7. 检查用户是否关注公众号
         *       7.1. 如果未关注（subscribe=0），跳转到引导关注页面
         *       7.2. 如果已关注（subscribe=1），进入注册或登录流程
         */

        // 步骤 7.1: 处理未关注用户
        if ($subscribe == 0) {
            error_log($log_prefix . '[步骤 7.1] 用户未关注公众号，跳转到引导关注页面');

            // 跳转至引导关注页面，使用动态域名
            header('Location:' . $weburl . "/?page_id=71667");
            exit();
        }

        // 步骤 7.2: 处理已关注用户，进入注册或登录流程
        if ($subscribe != 0) {
            error_log($log_prefix . '[步骤 7.2] 用户已关注公众号，开始注册或登录流程');

            // 用户关注状态：0=未关注，1=已关注


            //已关注用户获得其 $user_info_obj->unionid,作为自动注册或登录的账号

            $user_login = $user_info_obj->unionid;//将 unionid 作为用户账号


            //通过unionid 获取用户ID user_id

            $user_id = get_user_id($user_login);//用户ID






            /*
            *
            * $user_id 不存在的情况下，需要自动注册，并自动登录
            *
            * */


            if(!$user_id){


                //user_id不存在的情况下,需要自动注册


                //进入自动注册流程


                $user_login = $user_info_obj->unionid;//用户账户
                $user_password = substr(md5(uniqid(microtime())), 0, 10);//用户随机密码


                //获得用户昵称，并过滤表情

                if($user_info_obj->nickname){
                    $user_nickname = $user_info_obj->nickname;
                    $user_nickname = preg_replace('~\xEE[\x80-\xBF][\x80-\xBF]|\xEF[\x81-\x83][\x80-\xBF]~', '', $user_nickname);

                }else{
                    $user_nickname="匿名用户";
                }







                //注册
                $user_id = wp_create_user($user_login, $user_password);






                //注册成功后
                if($user_id){


                    //记录各种数据

                    add_user_meta($user_id, "openid_to_hlyyin", $user_info_obj->openid);//openid

                    //add_user_meta($user_id, 'user_sex', $user_info_obj->sex);//性别

                    add_user_meta($user_id, 'user_nickname', $user_nickname);//昵称



                    if($user_info_obj->headimgurl){

                        add_user_meta($user_id, 'user_avatar', $user_info_obj->headimgurl);//更新头像
                    }else{
                        add_user_meta($user_id, 'user_avatar', "");//更新头像
                    }




                    add_user_meta($user_id, 'user_post_info', "");//历史浏览信息

                    add_user_meta($user_id, 'user_viptime', 0);//用户vip时间

                    add_user_meta($user_id, 'week_mind', "");//模板提醒消息标识

                    add_user_meta($user_id, 'user_register_time', current_time("timestamp"));//记录用户注册时间

                    add_user_meta($user_id, 'user_last_login_time', current_time("timestamp"));//记录用户登录时间


                    add_user_meta($user_id, 'subscribe_scene', $user_info_obj->subscribe_scene);//记录用户关注公众号场景



                    add_user_meta($user_id, 'subscribe_time', $user_info_obj->subscribe_time);//记录用户登录时间


                    add_user_meta($user_id, 'source_of_registered_users', $source_of_registered_users);//记录用户来源


                    //有用户来源的话，在第三方商城“有赞”内，加入标签


                    if($source_of_registered_users){
                        tag_add_in_youzan($user_id,$source_of_registered_users);//有赞打标签
                    }



                    /*
                     *
                     * 给予用户3天VIP时间
                     * */



                    give_new_user_free_vip_time($user_id,3,$source_of_registered_users);








                    //进入自动登录流程


                    wp_set_current_user($user_id);//设置当前用户信息

                    wp_set_auth_cookie($user_id);//设置当前cookie

                    do_action('wp_login', $user_login);//登录




                }







            }





            /*
             * user_id存在的情况下,说明已经注册,只要自动登录
             *
             * */


            if ($user_id) {

                //自动登录中

                wp_set_current_user($user_id);//设置当前用户信息

                wp_set_auth_cookie($user_id);//设置当前cookie

                do_action('wp_login', $user_login);//登录


                //更新一些信息


                //记录登录时间

                update_user_meta($user_id, 'user_last_login_time', current_time("timestamp"));

                //记录或更新用户openid

                $openid_to_hlyyin = get_user_meta($user_id, "openid_to_hlyyin", true);//hlyyin的openid
                if (!$openid_to_hlyyin) {
                    update_user_meta($user_id, "openid_to_hlyyin", $user_info_obj->openid);//更新hlyyin openid
                }


                //更新头像信息


                if($user_info_obj->headimgurl){

                    update_user_meta($user_id, 'user_avatar', $user_info_obj->headimgurl);//更新头像
                }







            }









        }



    }









}


/**
 * @分步说明: 最终跳转处理
 *
 *   最终步骤. 根据原始访问页面类型构造跳转URL并执行跳转
 *       最终.1. 根据页面类型（post/page/cat/home）构造对应的URL
 *       最终.2. 使用动态域名确保跳转到正确的域名
 *       最终.3. 执行HTTP重定向并结束处理
 */

error_log($log_prefix . '[最终步骤] 开始构造跳转URL，页面类型: ' . $web_page_type . ', 页面ID: ' . $web_page_id);

// 最终.1: 根据页面类型构造跳转URL
if ($web_page_type == "post") {
    // 文章页面跳转
    $go_url = $weburl . '/?p=' . $web_page_id;
    error_log($log_prefix . '[最终.1] 构造文章页面跳转URL: ' . $go_url);

} elseif ($web_page_type == "page") {
    // 单独页面跳转
    $go_url = $weburl . '/?page_id=' . $web_page_id;
    error_log($log_prefix . '[最终.1] 构造单独页面跳转URL: ' . $go_url);

} elseif ($web_page_type == "cat") {
    // 分类页面跳转
    $go_url = $weburl . '/?cat=' . $web_page_id;
    error_log($log_prefix . '[最终.1] 构造分类页面跳转URL: ' . $go_url);

} else {
    // 默认首页跳转
    $go_url = $weburl . "/";
    error_log($log_prefix . '[最终.1] 构造首页跳转URL: ' . $go_url);
}

// 最终.2-3: 执行HTTP重定向
error_log($log_prefix . '[最终.2-3] 执行跳转到: ' . $go_url);
header('Location:' . $go_url);
exit();




