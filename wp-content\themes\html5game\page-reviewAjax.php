<?php
/*
Template Name:reviewAjax
*/

// 开启错误日志记录
if (defined('WP_DEBUG') && WP_DEBUG) {
    error_log('[page-reviewAjax.php] 记忆系统AJAX请求开始 - ' . date('Y-m-d H:i:s'));
    error_log('[page-reviewAjax.php] POST数据: ' . print_r($_POST, true));
}

$action = $_POST['action'];
if (!$action) {
    error_log('[page-reviewAjax.php] 错误: 缺少action参数');
    echo "禁止访问";
    exit;
}

error_log('[page-reviewAjax.php] 处理action: ' . $action);





// 获取并验证POST参数
$user_id = $_POST['user_id'];//用户ID
$isHeikaVip = $_POST['isHeikaVip'];//是否为黑卡会员（0或1）
$course_status = $_POST['course_status'];//课程状态（0=新学习，1=复习）
$nowTime = $_POST['nowTime'];//当前时间戳
$post_id = $_POST['post_id'];//课程ID
$model = $_POST['model'];//课程分类ID

// 参数验证和日志记录
if (!$user_id || !isset($post_id) || !isset($model)) {
    error_log('[page-reviewAjax.php] 错误: 缺少必需参数 - user_id: ' . $user_id . ', post_id: ' . $post_id . ', model: ' . $model);
    echo 0;
    exit;
}

error_log('[page-reviewAjax.php] 参数验证通过 - 用户ID: ' . $user_id . ', 课程ID: ' . $post_id . ', 分类ID: ' . $model . ', 黑卡状态: ' . $isHeikaVip . ', 课程状态: ' . $course_status);





// 修复构造函数参数顺序：正确顺序为 ($user_id, $isHeikaVip, $nowTime)
// 之前错误的顺序会导致时间戳被误认为黑卡状态，黑卡状态被误认为时间参数
$reviewMesHandle = new reviewMesHandle($user_id, $isHeikaVip, $nowTime);//初始化 记忆系统对象
error_log('[page-reviewAjax.php] 记忆系统对象初始化 - 用户ID: ' . $user_id . ', 黑卡状态: ' . $isHeikaVip . ', 时间: ' . $nowTime);


/*
 *
 * 处理课程记忆内容
 *
 * */

// 初始化返回数组
$return_arr = array();

// 保存用户复习计划信息
error_log('[page-reviewAjax.php] 开始保存复习计划 - 课程ID: ' . $post_id . ', 分类ID: ' . $model);
if ($reviewMesHandle->save_user_review_mes($post_id, $model)) {
    $return_arr['save_user_review_mes'] = 1;
    error_log('[page-reviewAjax.php] 复习计划保存成功');
} else {
    $return_arr['save_user_review_mes'] = 0;
    error_log('[page-reviewAjax.php] 复习计划保存失败');
}



if($reviewMesHandle->save_user_learn_detail($post_id,$course_status)){

    $return_arr['save_user_learn_detail'] = 1;


}else{

    $return_arr['save_user_learn_detail'] = 0;

}

$return_arr=$reviewMesHandle->set_heika_check_in_detail($return_arr);


//myDump($return_arr);






if($return_arr['save_user_review_mes']==1){
    $jsonStr=json_encode($return_arr);
    echo $jsonStr;
}else{
    echo 0;
}


















?>
