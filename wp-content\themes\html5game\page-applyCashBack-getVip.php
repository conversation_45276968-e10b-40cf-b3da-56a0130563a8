<?php
/*
Template Name:applyCashBack-getVip
*/



if ($_GET['action']) {
    session_start();
    session_unset();
}


pcVisitHandle();


$userData = new userDataTest();//初始化用户对象


$webUrl = home_url();//网站域名


$heika_page_url=$webUrl."/?page_id=72549";


//https://xgn.shuimitao.online/?page_id=72549







get_header();


if ($userData->isLogin) {


    //若有连续学习返现机会

    if($userData->continuously_learning_days_cash_back_chance>0){


        $is_already_add_viptime=0;


        //myDump($userData->cash_back_times);


        $userData->cash_back_times+=1;
        update_user_meta($userData->user_id,"cash_back_times",$userData->cash_back_times);

        //返现次数+1
        //保存返现次数


        $userData->continuously_learning_days_cash_back_chance-=1;
        update_user_meta($userData->user_id,"continuously_learning_days_cash_back_chance",$userData->continuously_learning_days_cash_back_chance);

        //保存$userData->continuously_learning_days_cash_back_chance




        //增加黑卡时间


       $telephone_number = get_user_meta($userData->user_id, "telephone_number", true);

        if (!$telephone_number) {
            $telephone_number = "暂无记录";

        }



        $heikaHandle = new heikaHandle($userData->user_id);//黑卡对象




        if ($heikaHandle->add_can_use_expiration_date("HAVE_DATE", 110, "hlyyin", "ALL", $telephone_number)) {
            $is_already_add_viptime=1;
        }




    }




    ?>


    <style>
        [v-cloak] {
            display: none;
        }

        .big_wraper {
            width: 90%;
            margin-left: 5%;
        }

        .big_wraper > div {

            width: 100%;
            float: left;

        }

        .big_wraper img {
            width: 100%;
            height: 100%;
            opacity: 1;
        }


        .big_wraper > div:first-child {

            margin-top: 30px;
            width: 100%;
            text-align: center;
            font-size: 16px;
            font-weight: 500;

        }



        .big_wraper > div:nth-child(3) {

            width: 80%;
            margin-left: 10%;
            text-align: center;
            font-size: 14px;
            height: 30px;

        }

        .big_wraper > div:nth-child(3) > span:nth-child(2) {

            padding: 1px 5px;
            background: yellow;
            color: orangered;
            border-radius: 3px;

        }

        .big_wraper > div:nth-child(3) > span {

            font-size: 14px;

        }

        .big_wraper > div:nth-child(4) {

            width: 70%;
            margin-left: 15%;
            text-align: center;
            font-size: 12px;
            color: darkgray;
            height: 30px;

        }



        .big_wraper > div:nth-child(5) {

            width: 80%;
            margin-left: 10%;
            text-align: center;
            font-size: 14px;
            height: 30px;

        }

        .big_wraper > div:nth-child(5) > span:nth-child(2) {

            padding: 1px 5px;
            background: yellow;
            color: orangered;
            border-radius: 3px;

        }

        .big_wraper > div:nth-child(5) > span {

            font-size: 14px;

        }


        .big_wraper > div:nth-child(6) {

            width: 70%;
            margin-left: 15%;
            text-align: center;
            font-size: 12px;
            color: darkgray;
            height: 30px;

        }


    </style>

    <div id="container">


        <!--未达到要求-->

        <div style="margin-top: 20%" class="weui-loadmore weui-loadmore_line"
             v-show="continuously_learning_days_cash_back_chance==0&&total_learning_days_cash_back_chance==0" v-cloak>
            <span class="weui-loadmore__tips">您还未达到要求</span>
        </div>

        <!--未达到要求-->

        <!--达到要求-->


        <div style="margin-top: 20%" class="weui-msg" v-show="is_already_add_viptime==1">
            <div class="weui-msg__icon-area"><i class="weui-icon-success weui-icon_msg"></i></div>
            <div class="weui-msg__text-area">
                <h2 class="weui-msg__title">操作成功</h2>
                <p class="weui-msg__desc">您已获得110天黑卡会员时间。</p>
            </div>
            <div class="weui-msg__opr-area">
                <p class="weui-btn-area">
                    <a @click.stop="go_to_heika_page" class="weui-btn weui-btn_primary">确定</a>

                </p>
            </div>
        </div>
        <!--达到要求-->







    </div>


    <script>

        var container = new Vue({
            el: '#container',


            data: {

                user_id:<?php echo $userData->user_id?>,

                continuously_learning_days_cash_back_chance:<?php echo $userData->continuously_learning_days_cash_back_chance;?>,
                total_learning_days_cash_back_chance:<?php echo $userData->total_learning_days_cash_back_chance;?>,
                is_already_add_viptime:<?php echo $is_already_add_viptime;?>,
                heika_page_url:"<?php echo $heika_page_url;?>"



            },


            methods: {



                go_to_heika_page:function(){

                    window.location=this.heika_page_url;

                }






            },

            computed: {},


            watch: {}
        });


    </script>


    <?php


} else {
    get_login_page();//提示登录

}


get_footer();
?>
