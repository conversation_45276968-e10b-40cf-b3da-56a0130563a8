<?php
/*
Template Name:la<PERSON>weixin
*/


if ($_GET['action']) {
    session_start();
    session_unset();
}


pcVisitHandle();


$userData = new userDataTest();//初始化用户对象




// 从主题配置中获取老师微信二维码，如果没有配置则使用默认图片
$teacher_wechat_qr = get_option('g_teacher_wechat_qr');
$miki_qrcode_img = !empty($teacher_wechat_qr) ? $teacher_wechat_qr : home_url() . "/wp-content/uploads/2020/02/miki.jpg";

// 调试信息：记录配置读取情况
error_log('[LaoshiWeixin] 老师微信二维码配置: ' . ($teacher_wechat_qr ? $teacher_wechat_qr : '未配置'));
error_log('[LaoshiWeixin] 最终使用图片: ' . $miki_qrcode_img);




get_header();


if ($userData->isLogin) {

    ?>


    <style>
        [v-cloak] {
            display: none;
        }

        .big_wraper {
            width: 90%;
            margin-left: 5%;
        }

        .big_wraper > div {

            width: 100%;
            float: left;

        }

        .big_wraper img {
            width: 100%;
            height: 100%;
            opacity: 1;
        }


        .big_wraper > div:first-child {

            margin-top: 30px;
            width: 100%;
            text-align: center;
            font-size: 16px;
            font-weight: 500;

        }



        .big_wraper > div:nth-child(3) {

            width: 80%;
            margin-left: 10%;
            text-align: center;
            font-size: 14px;
            height: 30px;

        }

        .big_wraper > div:nth-child(3) > span:nth-child(2) {

            padding: 1px 5px;
            background: yellow;
            color: orangered;
            border-radius: 3px;

        }

        .big_wraper > div:nth-child(3) > span {

            font-size: 14px;

        }

        .big_wraper > div:nth-child(4) {

            width: 70%;
            margin-left: 15%;
            text-align: center;
            font-size: 12px;
            color: darkgray;
            height: 30px;

        }



        .big_wraper > div:nth-child(5) {

            width: 80%;
            margin-left: 10%;
            text-align: center;
            font-size: 14px;
            height: 30px;

        }

        .big_wraper > div:nth-child(5) > span:nth-child(2) {

            padding: 1px 5px;
            background: yellow;
            color: orangered;
            border-radius: 3px;

        }

        .big_wraper > div:nth-child(5) > span {

            font-size: 14px;

        }


        .big_wraper > div:nth-child(6) {

            width: 70%;
            margin-left: 15%;
            text-align: center;
            font-size: 12px;
            color: darkgray;
            height: 30px;

        }


    </style>

    <div id="container">







        <div class="big_wraper" v-cloak>


            <div>添加老师微信，了解详情</div>

            <div>
                <img src="<?php echo $miki_qrcode_img; ?>">
            </div>



            <div style="margin-top: 20px">
                <span>微信号:</span>
                <span @click.stop="copy_weixin_2" id="weixin_2">hamigua_6666</span>
            </div>
            <div>点击微信号即可复制</div>


            <div style="margin-top: 20px">
                <span>验证信息:</span>
                <span @click.stop="copy_weixin_content" id="copy_weixin_content">课程咨询</span>
            </div>
            <div>点击内容可复制</div>


        </div>





    </div>


    <script>

        var container = new Vue({
            el: '#container',


            data: {

                user_id:<?php echo $userData->user_id?>,

                continuously_learning_days_cash_back_chance:<?php echo $userData->continuously_learning_days_cash_back_chance;?>,
                total_learning_days_cash_back_chance:<?php echo $userData->total_learning_days_cash_back_chance;?>


            },


            methods: {


                copy_weixin_2: function () {

                    var weixin_2 = document.getElementById("weixin_2").innerText;
                    var oInput = document.createElement('input');
                    oInput.value = weixin_2;
                    document.body.appendChild(oInput);
                    oInput.select();
                    document.execCommand("Copy");
                    oInput.className = 'oInput';
                    oInput.style.display = 'none';
                    alert('复制成功');


                },

                copy_weixin_content: function () {

                    var weixin_content = document.getElementById("copy_weixin_content").innerText;
                    var oInput = document.createElement('input');
                    oInput.value = weixin_content;
                    document.body.appendChild(oInput);
                    oInput.select();
                    document.execCommand("Copy");
                    oInput.className = 'oInput';
                    oInput.style.display = 'none';
                    alert('复制成功');


                }


            },

            computed: {},


            watch: {}
        });


    </script>


    <?php


} else {
    get_login_page();//提示登录

}


get_footer();
?>
